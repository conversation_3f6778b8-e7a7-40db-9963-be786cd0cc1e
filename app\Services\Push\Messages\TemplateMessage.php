<?php

namespace App\Services\Push\Messages;

/**
 * 模板消息类 - 仅在确实需要模板功能时使用
 */
class TemplateMessage extends Message
{
    /**
     * 模板ID
     */
    protected string $templateId;

    /**
     * 模板数据
     */
    protected array $templateData;

    public function __construct(
        string $templateId,
        array $templateData,
        string|array $target
    ) {
        $this->templateId = $templateId;
        $this->templateData = $templateData;
        $this->target = $target;
    }

    public function getTemplateId(): string
    {
        return $this->templateId;
    }

    public function getTemplateData(): array
    {
        return $this->templateData;
    }
}
