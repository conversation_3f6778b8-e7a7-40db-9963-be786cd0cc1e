<?php

namespace App\Http\AdminControllers;

use App\Http\Controllers\Controller;
use App\Services\CloudFiles\Facades\CloudFiles;
use App\Utils\Respond;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class MaterialsController extends AdminBaseController
{
    protected const PERMISSION_MAP = [
        'policy' => '媒体管理.获取上传策略',
    ];

    /**
     * 获取文件上传策略和配置
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function policy(Request $request) {
        // 获取当前使用的云存储驱动
        //        $driver = $request->input('driver', config('cloudfiles.default', 'oss'));

        $driver = config('cloudfiles.default', 'oss');
        // 生成文件目录
        $dir = 'cguc/' . date('Ymd') . '/' . Str::random(10) . '/';
        $filename = Str::uuid()
                       ->toString();
        // 生成回调地址
        $callbackUrl = route('api.materials.upload.callback', ['driver' => $driver, 'filename' => $filename]);

        try {
            // 获取上传策略
            $result = CloudFiles::driver($driver)
                                ->policy(callbackRoute: $callbackUrl, dir: $dir, contentLengthRange: 10 * 1024 * 1024);

            // 返回完整的上传配置
            return Respond::success([
                'filename'        => Str::uuid()
                                        ->toString(),
                'provider'        => $driver,  // 存储提供商
                'upload_url'      => 'https://livehls.oss-cn-shanghai.aliyuncs.com', // 上传域名
                'dir'             => $dir,  // 文件目录
                'policy'          => $result['policy'],  // 上传策略
                'signature'       => $result['signature'],  // 签名
                'callback'        => $result['callback'] ?? '',  // 回调配置
                'access_key'      => $result['AccessKeyId'] ?? $result['OSSAccessKeyId'] ?? '',  // 访问密钥ID
                'expire'          => $result['expire'],  // 过期时间
                'host'            => $result['host'],  // CDN域名
                // 针对不同云存储的特定参数
//                'provider_config' => $this->getProviderConfig($driver),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code'    => 500,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
