<?php

namespace App\Enums;

enum PushMessageCategoryEnum: int
{

    // 消息分类
    case CATEGORY_SYSTEM = 1;    // 系统通知
    case CATEGORY_ACTIVITY = 2;  // 活动通知
    case CATEGORY_AT = 3;        // @我的
    case CATEGORY_WALLET = 4;    // 钱包通知
    case CATEGORY_COMMENT = 5;   // 被评论
    case CATEGORY_LIKE = 6;      // 被点赞
    case CATEGORY_FOLLOW = 7;    // 关注通知
    case CATEGORY_HISTORY = 8;     // 历史消息
    // case CATEGORY_DEPARTMENT = 9; // 部门通知
    case CATEGORY_WORKSPACE = 99; // 工作台消息
    // case CATEGORY_APPLICATION = 98; // 应用消息


    public function label(): string {
        return match ($this) {
            self::CATEGORY_SYSTEM => '系统通知',
            self::CATEGORY_ACTIVITY => '活动通知',
            self::CATEGORY_AT => '@我的',
            self::CATEGORY_WALLET => '钱包通知',
            self::CATEGORY_COMMENT => '被评论',
            self::CATEGORY_LIKE => '被点赞',
            self::CATEGORY_FOLLOW => '关注通知',
            self::CATEGORY_HISTORY => '历史消息',
            self::CATEGORY_WORKSPACE => '工作台消息',
            // self::CATEGORY_APPLICATION => '应用消息',
            default => '',
        };
    }

    public function icon(): string {
        return match ($this) {
            self::CATEGORY_SYSTEM => 'https://kczliveoss.cztv.tv/static/message/messageicon_alert.png', // 系统通知用铃铛图标
            self::CATEGORY_ACTIVITY => 'https://kczliveoss.cztv.tv/static/message/messageicon_collection.png', // 活动通知用日历图标
            self::CATEGORY_AT => 'https://kczliveoss.cztv.tv/static/message/messageicon_at.png', // @我的用@图标
            self::CATEGORY_WALLET => 'https://kczliveoss.cztv.tv/static/message/messageicon_wallet.png', // 钱包通知用钱包图标
            self::CATEGORY_COMMENT => 'https://kczliveoss.cztv.tv/static/message/messageicon_comment.png', // 评论用对话图标
            self::CATEGORY_LIKE => 'https://kczliveoss.cztv.tv/static/message/messageicon_like.png', // 点赞用心形图标
            self::CATEGORY_FOLLOW => 'https://kczliveoss.cztv.tv/static/message/messageicon_follow.png', // 关注用添加用户图标
            self::CATEGORY_HISTORY => 'https://kczliveoss.cztv.tv/static/message/messageicon_history.png', // 历史消息用历史图标
            self::CATEGORY_WORKSPACE => 'https://kczliveoss.cztv.tv/static/message/messageicon_workspace.png', // 工作台消息用工作台图标
            // self::CATEGORY_APPLICATION => 'https://kczliveoss.cztv.tv/cguc/20250331/cGhxkY6jvS/fb266fb5-7001-48ae-99fb-f42824cb2acc.png', // 应用消息用工作台图标
            default => '',
        };
    }

    public function style():string {
        return match ($this) {
            self::CATEGORY_SYSTEM => 'notice', // 系统通知用铃铛图标
            self::CATEGORY_ACTIVITY => 'activity', // 活动通知用日历图标
            self::CATEGORY_AT => 'interaction', // @我的用@图标
            self::CATEGORY_COMMENT => 'interaction', // 评论用对话图标
            self::CATEGORY_LIKE => 'interaction', // 点赞用心形图标
            self::CATEGORY_FOLLOW => 'interaction', // 关注用添加用户图标
            self::CATEGORY_WALLET => 'wallet', // 钱包通知用钱包图标
            self::CATEGORY_HISTORY => 'history', // 历史消息用历史图标
            self::CATEGORY_WORKSPACE => 'custom', // 工作台消息用自定义图标
            // self::CATEGORY_APPLICATION => 'custom', // 应用消息用自定义图标
            default => '',
        };
    }

    public function sort(): int {
        return match ($this) {
            self::CATEGORY_SYSTEM => 1,
            self::CATEGORY_ACTIVITY => 2,
            self::CATEGORY_AT => 3,
            self::CATEGORY_WALLET => 4,
            self::CATEGORY_COMMENT => 5,
            self::CATEGORY_LIKE => 6,
            self::CATEGORY_FOLLOW => 7,
            self::CATEGORY_HISTORY => 8,
            self::CATEGORY_WORKSPACE => 2,
            // self::CATEGORY_APPLICATION => 2,
            default => 255,
        };
    }

    public static function options(): array {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }
}
