<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\PushMessageCategoryEnum;
use App\Enums\PushMessageDeliveryEnum;

/**
 * Push Template Resource
 * @property-read \App\Models\PushTemplate $resource
 * @mixin \App\Models\PushTemplate
 */
class PushTemplateResource extends JsonResource
{
    public function toArray(Request $request): array {
        return [
            'code'                  => $this->code,
            'name'                  => $this->name,
            'title'                 => $this->title,
            'content'               => $this->content,
            'category'              => $this->category,
            'category_text'         => $this->category ? PushMessageCategoryEnum::from($this->category)
                                                                                           ->label() : '',
            'delivery_type'         => $this->delivery_type,
            'delivery_type_text'    => $this->delivery_type ? PushMessageDeliveryEnum::from($this->delivery_type)
                                                                                                ->label() : '',
            'allowed_params'        => $this->allowed_params,
            'allowed_extend_params' => $this->allowed_extend_params,
            'status'                => $this->status,
            'status_text'           => $this->status_text,
        ];
    }
}
