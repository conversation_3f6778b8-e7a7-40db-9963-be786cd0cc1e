<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->unsignedTinyInteger('status')->nullable()->comment('状态:2待审核,1审核通过,0审核不通过')->after('is_revoked');
            $table->uuid('created_by')->nullable()->comment('创建人ID')->after('status');
            $table->uuid('updated_by')->nullable()->comment('更新人ID')->after('created_by');
            $table->uuid('review_by')->nullable()->comment('审核人ID')->after('updated_by');
            $table->timestamp('review_at')->nullable()->comment('审核时间')->after('review_by');
            $table->string('review_remark')->nullable()->comment('审核备注')->after('review_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('created_by');
            $table->dropColumn('updated_by');
            $table->dropColumn('review_by');
            $table->dropColumn('review_at');
            $table->dropColumn('review_remark');
        });
    }
};
