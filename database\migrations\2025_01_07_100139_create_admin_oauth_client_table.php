<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('admin_oauth_client', function (Blueprint $table) {
            $table->id();
            $table->uuid('admin_uuid')->comment('管理员UUID');
            $table->unsignedBigInteger('oauth_client_id')->comment('OAuth客户端ID');
            $table->tinyInteger('is_owner')->default(0)->comment('是否拥有者:1是,0否');
            $table->timestamps();

            $table->unique(['admin_uuid', 'oauth_client_id'],'unique_admin_oauth_client');
            $table->index(['admin_uuid']);
            $table->index(['oauth_client_id']);
        });
    }

    public function down(): void {
        Schema::dropIfExists('admin_oauth_client');
    }
};
