<?php

namespace App\Services\Push\Services;

use App\Services\Push\Contracts\MessageInterface;
use App\Services\Push\Contracts\PushServiceInterface;
use App\Services\Push\Exceptions\PushException;
use App\Services\Push\Support\Config;

abstract class AbstractPushService implements PushServiceInterface
{
    /**
     * 配置
     */
    protected Config $config;

    /**
     * 当前平台
     */
    protected string $platform = '';

    /**
     * 单次批量发送的最大数量
     */
    protected const MAX_BATCH_SIZE = 500;

    public function __construct(array $config)
    {
        $this->config = new Config($config);
    }

    /**
     * 转换消息为平台所需格式
     */
    abstract protected function transformMessage(MessageInterface $message): array;

    /**
     * 发送请求到推送平台
     */
    abstract protected function sendRequest(array $params): array;

    /**
     * 推送消息
     */
    public function push(MessageInterface $message): array
    {
        $params = $this->transformMessage($message);
        return $this->sendRequest($params);
    }

    /**
     * 按用户ID推送
     */
    public function pushToUsers(MessageInterface $message, string|array $userIds): array
    {
        $message->setTargetType('alias')
                ->setTarget($userIds);

        return $this->push($message);
    }

    /**
     * 按设备推送
     */
    public function pushToDevices(MessageInterface $message, string|array $deviceTokens): array
    {
        $message->setTargetType('device')
                ->setTarget($deviceTokens);

        return $this->push($message);
    }

    /**
     * 模板消息推送
     *
     * @throws PushException
     */
    public function pushTemplate(MessageInterface $message, string $templateId, array $templateData = []): array
    {
        throw new PushException('Template message not supported by default');
    }

    /**
     * 设置平台
     */
    public function platform(string $platform): self
    {
        $this->platform = strtolower($platform);
        return $this;
    }

    /**
     * 获取当前平台
     */
    public function getPlatform(): string
    {
        return $this->platform;
    }

    /**
     * 构建错误响应
     */
    protected function buildErrorResponse(string $message, int $code = 0): array
    {
        return [
            'success' => false,
            'error_code' => $code,
            'error_message' => $message
        ];
    }
}
