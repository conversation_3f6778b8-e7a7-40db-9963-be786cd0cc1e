<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Enums\PushMessageCategoryEnum;
use App\Exceptions\PushException;
use App\Jobs\ProcessPushMessageJob;
use App\Jobs\ProcessSubscriptionPushJob;
use App\Models\AdminUser;
use App\Models\Client;
use App\Models\OauthClientSubscription;
use App\Models\OauthClientTemplate;
use App\Models\PushMessage;
use App\Models\PushTemplate;
use App\Models\UserDevice;
use App\Models\UserMessage;
use App\Models\UserSubscription;
use App\Http\Resources\SubscriptionUserResource;
use App\Models\OAuthUserAuthorization;
use App\Models\User;
use App\Utils\OAuthCacher;
use App\Utils\RedirectToBuilder;
use App\Utils\Respond;
use App\Utils\Tools;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class PushController extends Controller
{
    const BATCH_SIZE = 20; // 推送批次大小
    const MAX_TEXT_LENGTH = 100; // 最大文本长度

    protected $validationMessages = [
        'template_params.required_with' => '使用模板时模板参数不能为空',
        'extend_params.required_with'   => '使用扩展功能时扩展参数不能为空',
        'redirect_to.required_if'       => '当需要跳转时跳转地址不能为空',
        'template_code.required'        => '模板代码不能为空',
        'template_code.string'          => '模板代码必须是字符串',
        'template_code.max'             => '模板代码不能超过50个字符',
        'to_open_id.required'           => '接收用户ID不能为空',
        'to_open_id.string'             => '接收用户ID必须是字符串',
        'template_params.array'         => '模板参数必须是数组格式',
        'extend_params.array'           => '扩展参数必须是数组格式',
        'extend_params.max'             => '扩展参数不能超过5个',
        'redirect_to.string'            => '重定向地址必须是字符串',
        'subscription_code.required'    => '订阅代码不能为空',
        'subscription_code.string'      => '订阅代码必须是字符串',
        'subscription_code.max'         => '订阅代码不能超过50个字符',
    ];

    /**
     * 单用户推送
     * @throws PushException
     * @throws ValidationException
     */
    public function pushToUser(Request $request)
    {
        $validated = $request->validate([
            'template_code'   => 'required|string|max:50',
            'to_open_id'      => 'required|string',
            'template_params' => 'array|nullable',
            'extend_params'   => 'array|nullable|max:5',
            'redirect_to'     => 'string|nullable',
        ]);

        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        $template = $this->getAndValidateTemplate($validated['template_code'], PushMessage::PUSH_TYPE_PERSONAL);

        // 获取用户 UUID

        $userUuid = $validated['to_open_id'];

        if (!Str::isUuid($userUuid)) {
            $userUuid = OAuthCacher::getUserUuidByOpenId($userUuid, $client->id);
        }

        if ($client->is_workspace_client === Client::IS_WORKSPACE_CLIENT_YES) {
            $userUuid = AdminUser::where('uuid', $userUuid)
                ->where('status', AdminUser::STATUS_ENABLED)
                ->value('bind_user_uuid');
        }

        if (!$userUuid) {
            throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        $templateParams = $this->filterTemplateParams($template->allowed_params ?? [], $validated['template_params'] ?? []);

        $extendParams = $this->filterExtendParams($template->allowed_extend_params ?? [], $validated['extend_params'] ?? []);

        if ($redirectTo = $validated['redirect_to'] ?? '') {
            if (RedirectToBuilder::isValidRedirectTo($redirectTo)) {
                $extendParams['redirect_to'] = $redirectTo;
            }
        }

        $validated['template_params'] = $templateParams;
        $validated['extend_params'] = $extendParams;

        // 创建消息记录
        $message = $this->createPushMessage(template: $template, pushType: PushMessage::PUSH_TYPE_PERSONAL, params: $validated, userUuid: $userUuid);

        // 创建用户消息记录
        $this->createUserMessages($message->id, collect([$userUuid]));

        // 使用普通推送Job即可,因为广播消息不需要批处理
        ProcessPushMessageJob::dispatch($message);

        return Respond::success([
            'message_id' => $message->msg_key,
        ]);
    }

    /**
     * 平台群发推送
     * @throws PushException
     * @throws ValidationException
     */
    public function broadcast(Request $request)
    {
        $validated = $request->validate([
            'template_code'   => 'required|string|max:50',
            'template_params' => 'array|nullable',
            'extend_params'   => 'array|nullable',
            'redirect_to'     => 'string|nullable',
        ]);

        $template = $this->getAndValidateTemplate($validated['template_code'], PushMessage::PUSH_TYPE_BROADCAST);

        //        if (!$template->cloud_template_id) {
        //            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        //        }

        $templateParams = $this->filterTemplateParams($template->allowed_params ?? [], $validated['template_params'] ?? []);

        $extendParams = $this->filterExtendParams($template->allowed_extend_params ?? [], $validated['extend_params'] ?? []);

        if ($redirectTo = $validated['redirect_to'] ?? '') {
            if (RedirectToBuilder::isValidRedirectTo($redirectTo)) {
                $extendParams['redirect_to'] = $redirectTo;
            }
        }

        $validated['template_params'] = $templateParams;
        $validated['extend_params'] = $extendParams;

        $message = $this->createPushMessage(template: $template, pushType: PushMessage::PUSH_TYPE_BROADCAST, params: $validated);

        // 使用普通推送Job即可,因为广播消息不需要批处理
        ProcessPushMessageJob::dispatch($message);

        return Respond::success(['message_id' => $message->msg_key]);
    }

    /**
     * 创建应用订阅
     * @throws PushException
     */
    public function createSubscription(Request $request)
    {
        $validated = $request->validate([
            'name'          => 'required|string|max:50',
            'template_code' => 'required|string|max:50',
            'description'   => 'nullable|string|max:200',
        ]);

        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        // 验证模板是否可用
        $template = PushTemplate::where('code', $validated['template_code'])
            ->where('status', PushTemplate::STATUS_ENABLE)
            ->first();

        if (!$template || OauthClientTemplate::where('oauth_client_id', $client->id)
            ->where('push_template_id', $template->id)
            ->where('status', OauthClientTemplate::STATUS_ENABLE)
            ->doesntExist()
        ) {
            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }

        // 生成唯一的订阅代码
        $subscriptionCode = 'SUB_' . Str::random(16);
        while (OauthClientSubscription::where('subscription_code', $subscriptionCode)
            ->exists()
        ) {
            $subscriptionCode = 'SUB_' . Str::random(16);
        }

        // 创建订阅
        $subscription = new OauthClientSubscription([
            'oauth_client_id'   => $client->id,
            'name'              => $validated['name'],
            'description'       => $validated['description'],
            'push_template_id'  => $template->id,
            'subscription_code' => $subscriptionCode,
            'status'            => OauthClientSubscription::STATUS_ENABLE,
        ]);

        $subscription->save();

        return Respond::success([
            'subscription_code' => $subscriptionCode,
        ]);
    }

    /**
     * 为用户订阅
     * @throws ValidationException
     * @throws PushException
     */
    public function subscribeUser(Request $request)
    {
        $validated = $request->validate([
            'subscription_code' => 'required|string|max:50',
            'to_open_id'        => 'required|string',
        ]);

        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        // 获取订阅配置
        $subscription = OauthClientSubscription::where('oauth_client_id', $client->id)
            ->where('subscription_code', $validated['subscription_code'])
            ->where('status', OauthClientSubscription::STATUS_ENABLE)
            ->first();

        if (!$subscription) {
            throw new PushException(ErrorCodeEnum::PUSH_SUBSCRIPTION_NOT_FOUND);
        }

        // 获取用户 UUID
        $userUuid = $validated['to_open_id'];
        if (!Str::isUuid($userUuid)) {
            $userUuid = OAuthCacher::getUserUuidByOpenId($userUuid, $client->id);
        }

        if ($client->is_workspace_client === Client::IS_WORKSPACE_CLIENT_YES) {
            $userUuid = AdminUser::where('uuid', $userUuid)
                ->where('status', AdminUser::STATUS_ENABLED)
                ->value('bind_user_uuid');
        }

        if (!$userUuid) {
            throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        // 检查是否已订阅
        $userSubscription = UserSubscription::where('oauth_client_subscription_id', $subscription->id)
            ->where('user_uuid', $userUuid)
            ->where('status', UserSubscription::STATUS_ENABLE)
            ->first();

        if ($userSubscription) {
            if ($userSubscription->status === UserSubscription::STATUS_ENABLE) {
                return Respond::success(['message' => '订阅成功']);
            }

            // 如果之前取消过订阅，则重新启用
            $userSubscription->status = UserSubscription::STATUS_ENABLE;
            $userSubscription->save();
        } else {
            // 创建新的订阅记录
            UserSubscription::create([
                'oauth_client_subscription_id' => $subscription->id,
                'user_uuid'                    => $userUuid,
                'status'                       => UserSubscription::STATUS_ENABLE,
            ]);
        }

        return Respond::success(['message' => '订阅成功']);
    }

    /**
     * 取消订阅
     */
    public function unsubscribeUser(Request $request)
    {
        $validated = $request->validate([
            'subscription_code' => 'required|string|max:50',
            'to_open_id'        => 'required|string',
        ]);

        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        // 获取订阅配置
        $subscription = OauthClientSubscription::where('oauth_client_id', $client->id)
            ->where('subscription_code', $validated['subscription_code'])
            ->where('status', OauthClientSubscription::STATUS_ENABLE)
            ->first();

        if (!$subscription) {
            throw new PushException(ErrorCodeEnum::PUSH_SUBSCRIPTION_NOT_FOUND);
        }

        // 获取用户 UUID
        $userUuid = $validated['to_open_id'];
        if (!Str::isUuid($userUuid)) {
            $userUuid = OAuthCacher::getUserUuidByOpenId($userUuid, $client->id);
        }

        if ($client->is_workspace_client === Client::IS_WORKSPACE_CLIENT_YES) {
            $userUuid = AdminUser::where('uuid', $userUuid)
                ->where('status', AdminUser::STATUS_ENABLED)
                ->value('bind_user_uuid');
        }

        if (!$userUuid) {
            throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        // 检查是否已订阅
        $userSubscription = UserSubscription::where('oauth_client_subscription_id', $subscription->id)
            ->where('user_uuid', $userUuid)
            ->where('status', UserSubscription::STATUS_ENABLE)
            ->first();

        if (!$userSubscription) {
            throw new PushException(ErrorCodeEnum::PUSH_SUBSCRIPTION_NOT_FOUND);
        }

        $userSubscription->status = UserSubscription::STATUS_DISABLE;
        $userSubscription->save();

        return Respond::success(['message' => '取消订阅成功']);
    }

    /**
     * 触发订阅推送
     * @throws ValidationException
     */
    public function triggerSubscription(Request $request)
    {
        $validated = $request->validate([
            'subscription_code' => 'required|string|max:50',
            'template_params'   => 'array|nullable',
            'extend_params'     => 'array|nullable',
            'redirect_to'       => 'string|nullable',
        ]);

        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }
        // 获取订阅配置
        $subscription = OauthClientSubscription::where('oauth_client_id', $client->id)
            ->where('subscription_code', $validated['subscription_code'])
            ->where('status', OauthClientSubscription::STATUS_ENABLE)
            ->firstOrFail();

        $template = $subscription->pushTemplate;

        // 筛选并验证模板参数
        $templateParams = $this->filterTemplateParams($template->allowed_params ?? [], $validated['template_params'] ?? []);

        // 筛选扩展参数
        $extendParams = $this->filterExtendParams($template->allowed_extend_params ?? [], $validated['extend_params'] ?? []);

        // 处理重定向链接
        if ($redirectTo = $validated['redirect_to'] ?? '') {
            if (RedirectToBuilder::isValidRedirectTo($redirectTo)) {
                $extendParams['redirect_to'] = $redirectTo;
            }
        }

        $validated['template_params'] = $templateParams;
        $validated['extend_params'] = $extendParams;

        $message = $this->createPushMessage(template: $subscription->pushTemplate, pushType: PushMessage::PUSH_TYPE_PERSONAL, params: $validated, subscriptionId: $subscription->id);

        // 使用订阅处理Job进行批量处理
        ProcessSubscriptionPushJob::dispatch($message);

        return Respond::success();
    }

    /**
     * 获取订阅用户列表
     */
    public function getSubscriptionUsers(Request $request)
    {
        $validated = $request->validate([
            'subscription_code' => 'required|string|max:50',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ], $this->validationMessages);

        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        $subscription = OauthClientSubscription::where('oauth_client_id', $client->id)
            ->where('subscription_code', $validated['subscription_code'])
            ->where('status', OauthClientSubscription::STATUS_ENABLE)
            ->firstOrFail();

        $page = $validated['page'] ?? 1;
        $perPage = $validated['per_page'] ?? 20;

        $users = User::select(['uuid', 'nickname', 'avatar'])
            ->where('status', User::STATUS_ACTIVE)
            ->whereHas('userSubscriptions', function ($query) use ($subscription) {
                $query->where('oauth_client_subscription_id', $subscription->id)
                    ->where('status', UserSubscription::STATUS_ENABLE);
            })
            ->whereHas('oauthAuthorizations', function ($query) use ($client) {
                $query->where('client_id', $client->id)
                      ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO); // Authorization is not revoked
            })
            ->with(['oauthBindings' => function ($query) use ($client) {
                $query->where('client_id', $client->id)
                      ->select('user_uuid', 'open_id'); // Select only necessary fields + foreign key for matching
            }])
            ->paginate($perPage, ['*'], 'page', $page);

        // 使用资源类转换数据
        return Respond::success(SubscriptionUserResource::collection($users));
    }

    /**
     * 获取并验证模板
     * @throws PushException
     */
    private function getAndValidateTemplate(string $code, int $pushType): PushTemplate
    {
        $clientId = OAuthCacher::getClientIdByKey(Tools::getClientId());

        $template = PushTemplate::where('code', $code)
            ->where('status', PushTemplate::STATUS_ENABLE)
            ->first();

        if (!$template || OauthClientTemplate::where('oauth_client_id', $clientId)
            ->where('push_template_id', $template->id)
            ->doesntExist()
        ) {
            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }

        if ($pushType == PushMessage::PUSH_TYPE_BROADCAST && !$template->isBroadcast()) {
            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }

        if ($pushType == PushMessage::PUSH_TYPE_PERSONAL && $template->isBroadcast()) {
            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }

        return $template;
    }

    /**
     * 创建推送消息
     */
    private function createPushMessage(PushTemplate $template, int $pushType, array $params, ?string $userUuid = null, ?int $subscriptionId = null): PushMessage
    {
        $message = new PushMessage([
            'msg_key'                      => Str::uuid()
                ->toString(),
            'push_template_id'             => $template->id,
            'oauth_client_id'              => Tools::getClientId(),
            'oauth_client_subscription_id' => $subscriptionId,
            'title'                        => $template->title,
            'content'                      => $template->content,
            'category'                     => $template->category,
            'delivery_type'                => $template->delivery_type,
            'push_type'                    => $pushType,
            'target_id'                    => $userUuid ?? null,
            'expired_seconds'              => 300,
            'template_params'              => $params['template_params'] ?? null,
            'extend_params'                => $params['extend_params'] ?? null,
            'status'                       => PushMessage::STATUS_PENDING,
            'push_time'                    => Carbon::now(),
            'is_silent'                    => $template->is_silent,
            'show_client_info'             => $template->show_client_info,
        ]);

        $message->save();

        return $message;
    }

    /**
     * 批量创建用户消息记录
     */
    private function createUserMessages(int $messageId, Collection $userUuids): void
    {
        $records = $userUuids->map(function ($userUuid) use ($messageId) {
            return [
                'user_uuid'       => $userUuid,
                'push_message_id' => $messageId,
                'is_read'         => UserMessage::IS_READ_NO,
                'created_at'      => Carbon::now(),
                'updated_at'      => Carbon::now(),
            ];
        })
            ->toArray();

        UserMessage::insert($records);
    }

    /**
     * 获取目标设备列表
     */
    private function getTargetDevices(array $platformTypes, ?string $userId = null): Collection
    {
        $query = UserDevice::where('is_active', 1)
            ->whereIn('platform_type', $platformTypes);

        if ($userId) {
            $query->where('user_uuid', $userId);
        }

        return $query->get()
            ->map(function ($device) {
                return [
                    'user_uuid'     => $device->user_uuid,
                    'device_token'  => $device->device_token,
                    'platform_type' => $device->platform_type,
                ];
            });
    }

    /**
     * 筛选并验证模板参数
     * @throws ValidationException
     */
    private function filterTemplateParams(array $allowedParams, array $inputParams): array
    {
        // 筛选允许的参数
        $filteredParams = [];
        foreach ($allowedParams as $key => $desc) {
            if (key_exists($key, $inputParams)) {
                $filteredParams[$key] = $this->sanitizeHtml($inputParams[$key]);
            } else {
                // 验证必填参数
                throw ValidationException::withMessages([
                    "template_params.{$key}" => "缺少必要参数: {$desc}",
                ]);
            }
        }

        return $filteredParams;
    }

    private function filterExtendParams(array $allowedParams, array $inputParams): array
    {
        $filteredParams = [];
        foreach ($allowedParams as $key => $desc) {
            $filteredParams[$key] = isset($inputParams[$key]) ? $this->sanitizeHtml($inputParams[$key]) : '';
        }

        return $filteredParams;
    }

    /**
     * 过滤HTML标签，保留加粗、斜体、下划线等基本格式标签
     * 并限制文本长度最多100个字符，超出部分截断
     * @param mixed $value 需要过滤的值
     * @return mixed 过滤后的值
     */
    private function sanitizeHtml($value)
    {
        if (is_string($value)) {
            // 保留加粗、斜体、下划线等基本格式标签，移除其他HTML标签
            $filtered = strip_tags($value, '<b><strong><i><em><u><br>');

            // 限制文本长度最多100个字符，超出部分截断
            if (mb_strlen($filtered) > self::MAX_TEXT_LENGTH) {
                $filtered = mb_substr($filtered, 0, self::MAX_TEXT_LENGTH) . '...';
            }

            return $filtered;
        } elseif (is_array($value)) {
            // 递归处理数组
            foreach ($value as $k => $v) {
                $value[$k] = $this->sanitizeHtml($v);
            }
            return $value;
        }

        // 其他类型直接返回
        return $value;
    }
}
