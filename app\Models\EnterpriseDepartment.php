<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Kalnoy\Nestedset\NodeTrait;

/**
 * @property string $id
 * @property string $enterprise_id 所属企业ID
 * @property string $name 部门名称
 * @property string $code 部门编码
 * @property string|null $parent_id 父部门ID
 * @property int $_lft
 * @property int $_rgt
 * @property int|null $sort
 * @property string|null $remark
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Kalnoy\Nestedset\Collection<int, EnterpriseDepartment> $children
 * @property-read int|null $children_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\EnterpriseContact> $contacts
 * @property-read int|null $contacts_count
 * @property-read \App\Models\Enterprise|null $enterprise
 * @property-read EnterpriseDepartment|null $parent
 * @method static \Kalnoy\Nestedset\Collection<int, static> all($columns = ['*'])
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment ancestorsAndSelf($id, array $columns = [])
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment ancestorsOf($id, array $columns = [])
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment applyNestedSetScope(?string $table = null)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment byEnterprise($enterpriseId)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment countErrors()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment d()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment defaultOrder(string $dir = 'asc')
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment descendantsAndSelf($id, array $columns = [])
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment descendantsOf($id, array $columns = [], $andSelf = false)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment fixSubtree($root)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment fixTree($root = null)
 * @method static \Kalnoy\Nestedset\Collection<int, static> get($columns = ['*'])
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment getNodeData($id, $required = false)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment getPlainNodeData($id, $required = false)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment getTotalErrors()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment hasChildren()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment hasParent()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment isBroken()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment leaves(array $columns = [])
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment makeGap(int $cut, int $height)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment moveNode($key, $position)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment newModelQuery()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment newQuery()
 * @method static Builder<static>|EnterpriseDepartment onlyTrashed()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment orWhereAncestorOf(bool $id, bool $andSelf = false)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment orWhereDescendantOf($id)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment orWhereNodeBetween($values)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment orWhereNotDescendantOf($id)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment orderByHierarchy()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment query()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment rebuildSubtree($root, array $data, $delete = false)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment rebuildTree(array $data, $delete = false, $root = null)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment reversed()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment root(array $columns = [])
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereAncestorOf($id, $andSelf = false, $boolean = 'and')
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereAncestorOrSelf($id)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereCode($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereCreatedAt($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereDeletedAt($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereDescendantOf($id, $boolean = 'and', $not = false, $andSelf = false)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereDescendantOrSelf(string $id, string $boolean = 'and', string $not = false)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereEnterpriseId($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereId($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereIsAfter($id, $boolean = 'and')
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereIsBefore($id, $boolean = 'and')
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereIsLeaf()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereIsRoot()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereLft($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereName($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereNodeBetween($values, $boolean = 'and', $not = false, $query = null)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereNotDescendantOf($id)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereParentId($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereRemark($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereRgt($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereSort($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment whereUpdatedAt($value)
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment withDepth(string $as = 'depth')
 * @method static Builder<static>|EnterpriseDepartment withTrashed()
 * @method static \Kalnoy\Nestedset\QueryBuilder<static>|EnterpriseDepartment withoutRoot()
 * @method static Builder<static>|EnterpriseDepartment withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperEnterpriseDepartment
 */
class EnterpriseDepartment extends Model
{
    use HasFactory, SoftDeletes, HasUuids, NodeTrait;

    protected $fillable = [
        'enterprise_id',
        'name',
        'code',
        'parent_id',
        'level',
        'sort',
        'remark',
    ];

    /*
    |--------------------------------------------------------------------------
    | 关联关系
    |--------------------------------------------------------------------------
    */

    /**
     * 关联企业
     */
    public function enterprise()
    {
        return $this->belongsTo(Enterprise::class, 'enterprise_id', 'id');
    }

    /**
     * 关联部门联系人
     */
    public function contacts()
    {
        return $this->hasMany(EnterpriseContact::class, 'department_id', 'id');
    }

    /*
    |--------------------------------------------------------------------------
    | NodeTrait UUID 兼容方法
    |--------------------------------------------------------------------------
    */

    /**
     * 获取父ID键名
     */
    public function getParentIdName()
    {
        return 'parent_id';
    }
    
    /**
     * 为支持UUID类型的parent_id，重写此方法
     */
    // protected function newNestedSetQuery($table = null)
    // {
    //     $builder = $this->newQuery();
        
    //     if (is_null($table)) {
    //         $table = $this->getTable();
    //     }
        
    //     // 添加企业ID筛选
    //     if ($this->enterprise_id) {
    //         $builder->where('enterprise_id', $this->enterprise_id);
    //     }
        
    //     return $builder;
    // }

    /**
     * 插入新节点时考虑企业ID
     */
    // public function newScopedQuery($excludeSelf = false)
    // {
    //     $query = $this->newQuery();
        
    //     if ($this->enterprise_id) {
    //         $query->where('enterprise_id', $this->enterprise_id);
    //     }
        
    //     if ($excludeSelf && $this->exists) {
    //         $query->where($this->getKeyName(), '!=', $this->getKey());
    //     }
        
    //     return $query;
    // }

    /**
     * 查询作用域：按企业ID筛选
     */
    public function scopeByEnterprise(Builder $query, $enterpriseId): Builder
    {
        return $query->where('enterprise_id', $enterpriseId);
    }

    /**
     * 用于层级排序的方法
     */
    public function scopeOrderByHierarchy(Builder $query): Builder
    {
        return $query->orderBy('_lft');
    }

    /**
     * 获取部门层级树
     */
    public static function getDepartmentTree($enterpriseId)
    {
        return static::byEnterprise($enterpriseId)
            ->defaultOrder()
            ->get()
            ->toTree();
    }
}
