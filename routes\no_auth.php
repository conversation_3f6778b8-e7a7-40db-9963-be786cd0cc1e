<?php

use App\Http\Controllers\{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    User<PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    SystemController,
    OAuthController,
    <PERSON>ush<PERSON>ontroller,
    RealnameAuthController,
    MaterialController,
    WorkSpaceController
};
use App\Http\AdminControllers\AuthController as AdminAuthController;

/*
|--------------------------------------------------------------------------
| 前台公开路由
|--------------------------------------------------------------------------
*/

Route::prefix('api')
    ->group(function () {
        // 认证相关
        Route::prefix('auth')
            ->group(function () {
                // 登录路由（支持多种登录方式）
                Route::post('login/{type}', [AuthController::class, 'login'])
                    ->whereIn('type', [
                        'sms',          // 短信验证码登录
                        'third',        // 第三方平台登录
                        'apple_id',     // Apple ID登录
                        'oneclick',     // 一键登录
                        'pwd',           // 密码登录
                    ]);

                Route::get('check', [AuthController::class, 'checkAuth']);         // 检查登录状态
                Route::post('bind-mobile', [AuthController::class, 'bindMobile']); // 绑定手机号
            });

        // 短信服务
        Route::prefix('sms')
            ->group(function () {
                Route::post('send', [SmsController::class, 'sendSms']);           // 发送验证码短信
            });

        // 用户相关
        Route::prefix('user')
            ->group(function () {
                // 找回密码
                Route::post('find-pwd-one', [UserController::class, 'findPwdOne']);   // 找回密码第一步
                Route::post('find-pwd-two', [UserController::class, 'findPwdTwo']);   // 找回密码第二步

                // 敏感数据处理
                Route::post('decrypt', [UserController::class, 'decryptSensitive']);   // 解密敏感数据

                // 人脸验证回调
                Route::match(['get', 'post'], 'face-verify/callback', [
                    RealnameAuthController::class,
                    'faceVerifyCallback',
                ])
                    ->name('face-verify.callback');
            });

        // 系统接口
        Route::get('applications', [SystemController::class, 'applications']); // 获取应用列表
        // version 5.2.9使用
        Route::get('new-applications', [SystemController::class, 'newApplications']); // 获取新应用列表

        // OAuth2.0公开接口
        Route::middleware('signature:app')
            ->prefix('oauth2')->group(function () {
                Route::get('client/{clientKey}', [OAuthController::class, 'getClientInfo']);    // 获取客户端信息
            });

        Route::middleware('signature:client')
            ->prefix('oauth2')
            ->group(function () {
                Route::post('access_token', [OAuthController::class, 'issueToken']);            // 获取访问令牌
                Route::post('user_info', [OAuthController::class, 'getUserInfo']);              // 获取用户信息
                Route::post('department-list', [OAuthController::class, 'getDepartmentList']);    // 获取部门列表
                Route::post('admin-list', [OAuthController::class, 'getAdminList']);              // 获取管理员列表

                Route::post('enterprise/departments', [WorkSpaceController::class, 'enterpriseDepartments']); // 获取企业部门列表
                Route::post('enterprise/full-contacts', [WorkSpaceController::class, 'enterpriseFullContacts']);           // 获取企业通讯录
            });

        // 推送服务公开接口
        Route::middleware('signature:client')
            ->prefix('push/service')
            ->group(function () {
                Route::post('to-user', [PushController::class, 'pushToUser']);                  // 推送给指定用户
                Route::post('create-subscription', [PushController::class, 'createSubscription']); // 创建订阅
                Route::post('subscribe-user', [PushController::class, 'subscribeUser']);          // 订阅用户
                Route::post('unsubscribe-user', [PushController::class, 'unsubscribeUser']);        // 取消订阅
                Route::post('subscription', [PushController::class, 'triggerSubscription']);      // 触发订阅
                Route::post('subscribe-user-list', [PushController::class, 'getSubscriptionUsers']);   // 获取订阅用户列表
                Route::post('broadcast', [PushController::class, 'broadcast']);                   // 广播消息
            });

        // 媒体上传回调接口
        Route::prefix('materials')
            ->group(function () {
                Route::post('/callback/{driver}', [
                    MaterialController::class,
                    'callback',
                ])
                    ->name('api.materials.upload.callback');
            });
    });

/*
|--------------------------------------------------------------------------
| 后台公开路由
|--------------------------------------------------------------------------
*/
Route::prefix('admin/api')
    ->group(function () {
        // 认证相关
        Route::prefix('auth')
            ->group(function () {
                Route::post('login', [AdminAuthController::class, 'login']);                // 管理员登录
                Route::post('sms-login', [AdminAuthController::class, 'smsLogin']);        // 管理员短信登录
                Route::post('qr-code', [AdminAuthController::class, 'generateQrCode']);     // 生成扫码登录二维码
                Route::post('qr-status', [AdminAuthController::class, 'checkQrStatus']);    // 查询扫码状态
                Route::post('qr-cancel', [AdminAuthController::class, 'cancelQrLogin']);    // 取消扫码登录
            });

        Route::prefix('sms')
            ->group(function () {
                Route::post('send', [SmsController::class, 'sendSmsAdminLogin']);           // 发送验证码短信
            });
    });

Route::middleware('signature:client')
    ->post('api/test/test-signature', ['App\Http\Controllers\TestController', 'testSignature']);

// Route::post('api/test/get-real-ip', ['App\Http\Controllers\TestController', 'getRealIP']);
