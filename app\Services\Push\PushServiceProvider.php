<?php

namespace App\Services\Push;

use Illuminate\Support\ServiceProvider;

class PushServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->mergeConfigFrom(
            __DIR__.'/Config/push.php', 'push'
        );

        $this->app->singleton('push', function ($app) {
            return new PushManager($app);
        });

        $this->app->singleton(PushManager::class, function ($app) {
            return $app['push'];
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/Config/push.php' => config_path('push.php'),
            ], 'push-config');
        }
    }
}
