<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $enterprise_id 所属企业ID
 * @property string $user_id 所属用户ID
 * @property int $status 状态:1启用,0禁用
 * @property string|null $remark 备注
 * @property string|null $created_by 创建人
 * @property string|null $updated_by 更新人
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereEnterpriseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperEnterpriseAdmin
 */
class EnterpriseAdmin extends Model
{
    //
}
