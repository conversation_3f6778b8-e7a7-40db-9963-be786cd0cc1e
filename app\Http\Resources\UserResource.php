<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 *  User Resource
 *
 * @property-read User $resource
 * @mixin User
 */
class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array {
        return [
            'uuid'                    => $this->uuid,
            'username'                => $this->username,
            'nickname'                => $this->display_nickname,
            'nickname_confirmed'      => $this->nickname_confirmed,
            'gender'                  => $this->gender,
            'gender_txt'              => $this->gender_txt,
            'mobile'                  => $this->masked_sensitive_info['mobile'],
            'mobile_confirmed'        => $this->isMobileConfirmed(),
            'avatar'                  => $this->display_avatar,
            'avatar_confirmed'        => $this->avatar_confirmed,
            'birthday'                => $this->birthday,
            'comment'                 => $this->display_comment,
            'comment_confirmed'       => $this->comment_confirmed,
            'status_txt'              => $this->status_txt,
            'realname_auth_confirmed' => $this->isRealnameAuthConfirmed(),
            'realname_auth_name'      => $this->masked_sensitive_info['realname'],
            'realname_auth_identity'  => $this->masked_sensitive_info['identity'],
            'realname_auth_times'     => 5,
            'third_party_wx'          => $this->third_party['app_wechat_nickname'] ?? '',
            'is_bind_third_party_wx'  => isset($this->wx_openids['app']) ? 1 : 0,
            'is_set_pwd'              => $this->password ? 1 : 0,
            'has_workspace'           => $this->bindAdminUser ? 1 : 0,
        ];
    }
}
