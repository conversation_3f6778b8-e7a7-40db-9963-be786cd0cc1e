<?php

namespace App\Enums;

enum OAuthScopeEnum: string
{
    case BASE = 'base';
    case MOBILE = 'mobile';
    case IDENTITY = 'identity';
    case USERLIST = 'user_list';
    case ADMINBASE = 'admin_base';
    case FULL_ADMIN_CONTACTS = 'full_admin_contacts';
    case ACCESS_ADMIN_LIST = 'access_admin_list';
    case DEPARTMENTLIST = 'department_list';
    case ROLELIST = 'role_list';
    case ADMINPUSH = 'admin_push';

    public function label(): string {
        return match ($this) {
            self::BASE => '基础信息(昵称、头像、性别)',
            self::MOBILE => '手机号码',
            self::IDENTITY => '实名信息',
            self::ADMINBASE => '管理员基础信息',
            self::FULL_ADMIN_CONTACTS => '完整通讯录列表',
            self::ACCESS_ADMIN_LIST => '可访问管理员列表',
            self::DEPARTMENTLIST => '完整部门列表',
            self::ROLELIST => '角色列表',
            self::ADMINPUSH => '管理员推送',
            default => '',
        };
    }

    public static function options(): array {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    public static function globalOptions(): array {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                self::BASE,
                self::MOBILE,
                self::IDENTITY,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    public static function adminOptions(): array {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                self::ADMINBASE,
                self::FULL_ADMIN_CONTACTS,
                self::ACCESS_ADMIN_LIST,
                self::DEPARTMENTLIST,
                self::ROLELIST,
                self::ADMINPUSH,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }
}
