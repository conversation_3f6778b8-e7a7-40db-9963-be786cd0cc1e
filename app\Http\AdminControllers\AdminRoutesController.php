<?php

namespace App\Http\AdminControllers;

use Illuminate\Http\Request;
use App\Models\AdminRoute;
use App\Utils\Respond;

class AdminRoutesController extends AdminBaseController{
    protected $validationMessages = [
        'name.required' => '路由名称不能为空',
        'name.max' => '路由名称不能超过50个字符',
        'path.required' => '路由路径不能为空',
        'path.max' => '路由路径不能超过255个字符',
        
    ];

    protected const PERMISSION_MAP = [
        'show' => '菜单管理.详情',
        'store' => '菜单管理.添加',
        'update' => '菜单管理.编辑',
        'destroy' => '菜单管理.删除',
    ];

    public function index(Request $request){
        $tree = (new AdminRoute)->withQuery(function ($query) use ($request) {
            return $query->with([
                'roles',
                'permissions',
            ]);
        })
                                ->toTree();

        return Respond::success($tree);
    }

    public function show($id){
        $route = AdminRoute::with('parent')->findOrFail($id);
        return view('admin.routes.show', compact('route'));
    }
    
    public function store(Request $request){
        $this->validate($request, [
            'name' => 'required|max:50',
            'path' => 'required|max:255',
            'parent_id' => 'nullable|exists:admin_routes,id',
            // 'sort' => 'required|integer|min:0',
            // 'hidden' => 'required|boolean',
            'roles' => 'nullable|array',
            'roles.*' => 'required|exists:roles,id',
        ], $this->validationMessages);

        $route = AdminRoute::create($request->all());
        
        if($request->filled('roles')){
            $route->roles()->sync(array_map('intval', $request->input('roles')));
        }
        return Respond::success();
    }

    public function update(Request $request, $id){
        $route = AdminRoute::findOrFail($id);
        $this->validate($request, [
            'name' => 'required|max:50',
            'path' => 'required|max:255',
            'parent_id' => 'nullable|exists:admin_routes,id',
            // 'sort' => 'required|integer|min:0', 
            // 'hidden' => 'required|boolean',
            'roles' => 'nullable|array',
            'roles.*' => 'required|exists:roles,id',
        ], $this->validationMessages);

        $route->update($request->all());
        if($request->filled('roles')){
            $route->roles()->sync(array_map('intval', $request->input('roles')));
        }
        return Respond::success();
    }

    public function destroy($id){
        $route = AdminRoute::findOrFail($id);
        $route->delete();
        return Respond::success();
    }

}
