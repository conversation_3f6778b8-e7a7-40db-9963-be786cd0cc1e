<?php

namespace App\Models\Traits;

use Illuminate\Support\Arr;
use Illuminate\Support\Str;

/**
 * 活动日志隐私数据处理 Trait
 * 用于在记录日志前处理敏感字段
 */
trait SanitizesActivityLogTrait
{
    /**
     * 处理属性以移除或屏蔽敏感数据
     *
     * @param array $properties
     * @return array
     */
    public function sanitizeActivityLogProperties(array $properties): array
    {
        if (isset($properties['attributes'])) {
            $properties['attributes'] = $this->sanitizeAttributes($properties['attributes']);
        }
        
        if (isset($properties['old'])) {
            $properties['old'] = $this->sanitizeAttributes($properties['old']);
        }
        
        return $properties;
    }
    
    /**
     * 处理属性数组
     *
     * @param array $attributes
     * @return array
     */
    protected function sanitizeAttributes(array $attributes): array
    {
        $sensitiveFields = $this->getSensitiveFields();
        
        foreach ($attributes as $key => $value) {
            if ($this->isSensitiveField($key, $sensitiveFields)) {
                $attributes[$key] = $this->maskSensitiveValue($key, $value);
            }
        }
        
        return $attributes;
    }
    
    /**
     * 获取敏感字段列表
     *
     * @return array
     */
    protected function getSensitiveFields(): array
    {
        return property_exists($this, 'sensitiveFields') 
            ? $this->sensitiveFields 
            : [
                'password', 'password_confirmation', 'current_password',
                'secret', 'token', 'api_key', 'private_key',
                'credit_card', 'card_number', 'card_cvc', 'card_expiry',
                'social_security', 'ssn', 'id_number',
                'mobile', 'phone', 'email', 'address'
            ];
    }
    
    /**
     * 检查字段是否敏感
     *
     * @param string $fieldName
     * @param array $sensitiveFields
     * @return bool
     */
    protected function isSensitiveField(string $fieldName, array $sensitiveFields): bool
    {
        foreach ($sensitiveFields as $sensitive) {
            if ($fieldName === $sensitive || Str::contains($fieldName, $sensitive)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 屏蔽敏感值
     *
     * @param string $fieldName
     * @param mixed $value
     * @return string|array
     */
    protected function maskSensitiveValue(string $fieldName, $value): string|array
    {
        // 如果是数组或对象，递归处理
        if (is_array($value) || is_object($value)) {
            $array = (array) $value;
            foreach ($array as $key => $item) {
                $array[$key] = $this->maskSensitiveValue($key, $item);
            }
            return $array;
        }
        
        // 如果是密码相关字段，返回固定掩码
        if (Str::contains($fieldName, ['password', 'secret', 'token', 'key'])) {
            return '[已加密]';
        }
        
        // 对于手机号、邮箱等，保留部分信息
        if (is_string($value)) {
            if (Str::contains($fieldName, ['mobile', 'phone']) && strlen($value) > 4) {
                return substr($value, 0, 3) . '****' . substr($value, -4);
            }
            
            if (Str::contains($fieldName, 'email') && Str::contains($value, '@')) {
                list($name, $domain) = explode('@', $value);
                $maskedName = substr($name, 0, 2) . str_repeat('*', max(strlen($name) - 2, 2));
                return $maskedName . '@' . $domain;
            }
            
            // 其他敏感字段，保留首尾字符
            if (strlen($value) > 6) {
                return substr($value, 0, 2) . str_repeat('*', strlen($value) - 4) . substr($value, -2);
            }
        }
        
        // 其他情况，完全掩码
        return '[敏感信息]';
    }
} 