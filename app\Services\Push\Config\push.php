<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Push Service
    |--------------------------------------------------------------------------
    |
    | 默认推送服务
    |
    */
    'default' => env('PUSH_DRIVER', 'umeng'),

    /*
    |--------------------------------------------------------------------------
    | Push Services
    |--------------------------------------------------------------------------
    |
    | 这里可以配置多个推送服务的参数
    | 注意: 这些配置会覆盖 config/services.php 中的相关配置
    |
    */
    'drivers' => [
        'umeng' => [
            'android' => [
                'appKey'          => env('UMENG_ANDROID_APP_KEY'),
                'appMasterSecret' => env('UMENG_ANDROID_APP_MASTER_SECRET'),
                'productionMode'  => env('UMENG_ANDROID_PRODUCTION_MODE', false),
                'channelActivity' => env('UMENG_ANDROID_CHANNEL_ACTIVITY', ''),
                'mainActivity'    => env('UMENG_ANDROID_MAIN_ACTIVITY', ''),
            ],
            'ios'     => [
                'appKey'          => env('UMENG_IOS_APP_KEY'),
                'appMasterSecret' => env('UMENG_IOS_APP_MASTER_SECRET'),
                'productionMode'  => env('UMENG_IOS_PRODUCTION_MODE', false),
            ],
            'harmony' => [
                'appKey'          => env('UMENG_HARMONY_APP_KEY'),
                'appMasterSecret' => env('UMENG_HARMONY_APP_MASTER_SECRET'),
                'productionMode'  => env('UMENG_HARMONY_PRODUCTION_MODE', false),
                'channelCategory' => 'NORMAL',
            ],
        ],

        'jpush' => [
            'app_key'         => env('JPUSH_APP_KEY'),
            'master_secret'   => env('JPUSH_MASTER_SECRET'),
            'production_mode' => env('JPUSH_PRODUCTION_MODE', false),
            'max_retry_times' => env('JPUSH_MAX_RETRY_TIMES', 3),
        ],

        'mpaas' => [
            'app_id'            => env('MPAAS_APP_ID'),
            'workspace_id'      => env('MPAAS_WORKSPACE_ID'),
            'access_key_id'     => env('MPAAS_ACCESS_KEY_ID'),
            'access_key_secret' => env('MPAAS_ACCESS_KEY_SECRET'),
        ],
    ],
];
