<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Permission;
use App\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建企业相关权限
        $enterprisePermissions = [
            // 企业管理员管理
            '企业管理员.查看列表' => '查看企业管理员列表',
            '企业管理员.创建' => '创建企业管理员',
            '企业管理员.查看详情' => '查看企业管理员详情',
            '企业管理员.编辑' => '编辑企业管理员',
            '企业管理员.删除' => '删除企业管理员',
            '企业管理员.绑定用户' => '绑定用户为企业管理员',
            '企业管理员.解绑用户' => '解绑企业管理员',

            // 企业角色管理
            '企业角色管理.查看列表' => '查看企业角色列表',
            '企业角色管理.创建' => '创建企业角色',
            '企业角色管理.查看详情' => '查看企业角色详情',
            '企业角色管理.编辑' => '编辑企业角色',
            '企业角色管理.删除' => '删除企业角色',
            '企业角色管理.分配角色' => '为管理员分配角色',
            '企业角色管理.撤销角色' => '撤销管理员角色',
            '企业角色管理.查看权限' => '查看企业权限列表',

            // 企业应用管理
            '企业应用管理.查看列表' => '查看企业应用列表',
            '企业应用管理.创建' => '创建企业应用',
            '企业应用管理.查看详情' => '查看企业应用详情',
            '企业应用管理.编辑' => '编辑企业应用',
            '企业应用管理.删除' => '删除企业应用',
            '企业应用管理.审核通过' => '审核通过企业应用',
            '企业应用管理.审核拒绝' => '审核拒绝企业应用',
            '企业应用管理.查看待审核' => '查看待审核应用',

            // 企业部门管理
            '企业部门管理.查看列表' => '查看企业部门列表',
            '企业部门管理.创建' => '创建企业部门',
            '企业部门管理.查看详情' => '查看企业部门详情',
            '企业部门管理.编辑' => '编辑企业部门',
            '企业部门管理.删除' => '删除企业部门',

            // 企业联系人管理
            '企业联系人管理.查看列表' => '查看企业联系人列表',
            '企业联系人管理.创建' => '创建企业联系人',
            '企业联系人管理.查看详情' => '查看企业联系人详情',
            '企业联系人管理.编辑' => '编辑企业联系人',
            '企业联系人管理.删除' => '删除企业联系人',
            '企业联系人管理.批量导入' => '批量导入企业联系人',
        ];

        foreach ($enterprisePermissions as $name => $description) {
            Permission::firstOrCreate([
                'name' => $name,
                'guard_name' => 'enterprise',
            ], [
                'description' => $description,
            ]);
        }

        // 创建默认的企业角色
        $defaultRoles = [
            '企业超级管理员' => '拥有所有企业权限',
            '企业应用管理员' => '管理企业应用',
            '企业部门管理员' => '管理企业部门和联系人',
        ];

        foreach ($defaultRoles as $roleName => $description) {
            Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'enterprise',
            ], [
                'description' => $description,
            ]);
        }

        // 为企业超级管理员分配所有权限
        $superAdminRole = Role::where('name', '企业超级管理员')
                             ->where('guard_name', 'enterprise')
                             ->first();
        
        if ($superAdminRole) {
            $allPermissions = Permission::where('guard_name', 'enterprise')->get();
            $superAdminRole->syncPermissions($allPermissions);
        }

        // 为企业应用管理员分配应用相关权限
        $appAdminRole = Role::where('name', '企业应用管理员')
                           ->where('guard_name', 'enterprise')
                           ->first();
        
        if ($appAdminRole) {
            $appPermissions = Permission::where('guard_name', 'enterprise')
                                       ->where('name', 'like', '企业应用管理.%')
                                       ->get();
            $appAdminRole->syncPermissions($appPermissions);
        }

        // 为企业部门管理员分配部门和联系人相关权限
        $deptAdminRole = Role::where('name', '企业部门管理员')
                            ->where('guard_name', 'enterprise')
                            ->first();
        
        if ($deptAdminRole) {
            $deptPermissions = Permission::where('guard_name', 'enterprise')
                                        ->where(function ($query) {
                                            $query->where('name', 'like', '企业部门管理.%')
                                                  ->orWhere('name', 'like', '企业联系人管理.%');
                                        })
                                        ->get();
            $deptAdminRole->syncPermissions($deptPermissions);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除企业相关权限
        Permission::where('guard_name', 'enterprise')->delete();
        
        // 删除企业相关角色
        Role::where('guard_name', 'enterprise')->delete();
    }
};
