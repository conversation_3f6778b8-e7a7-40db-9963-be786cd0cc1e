<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $id
 * @property string $access_token_id
 * @property int $is_revoked
 * @property string $expires_at
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property-read \App\Models\AccessToken|null $accessToken
 * @property-read mixed $revoked_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken whereAccessTokenId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken whereIsRevoked($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RefreshToken whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperRefreshToken
 */
class RefreshToken extends Model
{
    use  HasFactory;

    protected $table = 'oauth_refresh_tokens';

    protected $keyType = 'string';

    public $incrementing = false;

    protected $fillable = [
        'id',
        'access_token_id',
        'is_revoked',
        'expires_at',
    ];

    public $timestamps = false;


    const IS_REVOKED_NO = 0;
    const IS_REVOKED_YES = 1;

    public static array $revokedMap = [
        self::IS_REVOKED_YES => '是',
        self::IS_REVOKED_NO => '否',
    ];

    protected function revokedText(): Attribute {
        return new Attribute(function ($value) {
            return self::$revokedMap[$value] ?? '未知';
        });
    }

    public function accessToken() {
        return $this->belongsTo(AccessToken::class);
    }

    public function revoke() {
        return $this->forceFill(['is_revoked' => self::IS_REVOKED_YES])
                    ->save();
    }

    public function transient() {
        return false;
    }
}
