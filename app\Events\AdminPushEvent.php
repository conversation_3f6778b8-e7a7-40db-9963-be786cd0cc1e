<?php

namespace App\Events;

use App\Models\AdminUser;
use App\Models\Client;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminPushEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $adminUser;
    protected $clientId;
    protected $action;

    /**
     * Create a new event instance.
     */
    public function __construct(AdminUser $adminUser, $clientId, $action='access') {
        $this->adminUser = $adminUser;
        $this->clientId = $clientId;
        $this->action = $action;
    }

    public function getAdminUser() {
        return $this->adminUser;
    }

    public function getClientId() {
        return $this->clientId;
    }

    public function getAction() {
        return $this->action;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
