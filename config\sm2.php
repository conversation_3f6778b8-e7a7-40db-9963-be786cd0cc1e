<?php

return [
    'public_key' => env('SM2_PUBLIC_KEY'),
    'private_key' => env('SM2_PRIVATE_KEY'),

    'cache' => [
        'prefix' => env('SM2_CACHE_PREFIX', 'sm2_key_'),
        // minutes
        'ttl' => (int)env('SM2_CACHE_TTL', 2 * 60),
    ],

    'sens_priv_key' => env('SM2_SENS_PRIV_KEY'),
    'sens_pub_key' => env('SM2_SENS_PUB_KEY'),

    'data_priv_key' => env('SM2_DATA_PRIV_KEY'),
    'data_pub_key' => env('SM2_DATA_PUB_KEY'),

    'api_priv_key' => env('SM2_API_PRIV_KEY'),
    'api_pub_key' => env('SM2_API_PUB_KEY'),
];
