<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Http\Resources\Admins\AdminUserResource;
use App\Models\AdminDepartment;
use App\Models\AdminUser;
use App\Models\AdminOperationLog;
use App\Models\Material;
use App\Models\Permission;
use App\Models\User;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;
use Spatie\Activitylog\Facades\LogBatch;
use Spatie\Activitylog\Models\Activity;

class AdminUsersController extends AdminBaseController
{
    protected $validationMessages = [
        'username.required'              => '用户名不能为空',
        'username.string'                => '用户名必须是字符串',
        'username.min'                   => '用户名不能少于4个字符',
        'username.max'                   => '用户名不能超过20个字符',
        'username.unique'                => '用户名已存在',
        'password.required'              => '密码不能为空',
        'password.min'                   => '密码不能少于8个字符',
        'password.regex'                 => '密码必须包含大小写字母、数字和特殊符号(!@#$%^&*?-_)且最少8位',
        'true_name.required'             => '真实姓名不能为空',
        'true_name.string'               => '真实姓名必须是字符串',
        'true_name.max'                  => '真实姓名不能超过50个字符',
        'mobile.required'                => '手机号不能为空',
        'mobile.string'                  => '手机号必须是字符串',
        'mobile.isMobile'                => '请输入正确的手机号格式',
        'mobile.unique'                  => '手机号已被使用',
        'department_ids.required'        => '部门不能为空',
        'department_ids.array'           => '部门数据格式不正确',
        'department_ids.*.exists'        => '部门不存在',
        'is_department_leader.array'     => '部门负责人数据格式不正确',
        'is_department_leader.*.boolean' => '部门负责人数据格式不正确',
        'roles.array'                    => '角色数据格式不正确',
        'roles.*.exists'                 => '角色不存在',
        'permissions.array'              => '角色数据格式不正确',
        'permissions.*.exists'           => '权限不存在',
        'status.required'                => '状态不能为空',
        'status.in'                      => '状态值无效',
        'remark.required'                => '备注不能为空',
        'remark.string'                  => '备注必须是字符串',
        'remark.max'                     => '备注不能超过200个字符',
    ];

    protected const PERMISSION_MAP = [
        'index'        => '管理员.查看列表',
        'show'         => '管理员.查看详情',
        'store'        => '管理员.创建',
        'update'       => '管理员.编辑',
        'destroy'      => '管理员.删除',
        'changeStatus' => '管理员.修改状态',
        'batchChangeStatus' => '管理员.批量修改状态',
        'operationLogs' => '管理员.操作日志',
    ];

    /**
     * 管理员列表
     */
    public function index(Request $request) {
        $query = AdminUser::query()
                          ->with([
                              'roles',
                              'departments',
                              'bindUser',
                          ])
                          ->when($request->input('keyword'), function ($query, $keyword) {
                              $query->where(function ($q) use ($keyword) {
                                  $q->where('username', 'like', "%{$keyword}%")
                                    ->orWhere('true_name', 'like', "%{$keyword}%")
                                    ->orWhere('mobile', 'like', "%{$keyword}%")
                                    ->orWhere('uuid', $keyword);
                              });
                          })
                          ->when($request->input('department_id'), function ($query, $departmentId) {
                              $query->whereHas('departments', function ($q) use ($departmentId) {
                                  $q->where('admin_departments.id', $departmentId);
                              });
                          })
                          ->when($request->input('role_id'), function ($query, $roleId) {
                              $query->whereHas('roles', function ($q) use ($roleId) {
                                  $q->where('roles.id', $roleId);
                              });
                          })
                          ->when($request->filled('status'), function ($query) use ($request) {
                              $query->where('status', $request->input('status'));
                          });

        $admins = $query->orderByDesc('id')
                        ->paginate();

        return Respond::success(AdminUserResource::collection($admins));
    }

    /**
     * 获取表单选项
     */
    public function options() {
        return Respond::success([
            'status' => AdminUser::$statusMap,
        ]);
    }

    /**
     * 创建管理员
     *
     * @throws AdminException|\Throwable
     */
    public function store(Request $request) {
        $validated = $request->validate([
            'username'               => 'required|string|min:4|max:20|unique:' . AdminUser::class,
            'password'               => [
                'required',
                'min:8',
                'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
            ],
            'true_name'              => 'required|string|max:50',
            'avatar'                 => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)
                                        ->first();
                    if ($value && !$material) {
                        $fail('无效的头像资源');
                    }
                },
            ],
            'mobile'                 => 'required|string|isMobile|unique:' . AdminUser::class,
            'department_ids'         => 'required|array',
            'department_ids.*'       => 'required|exists:' . AdminDepartment::class . ',id',
            'roles'                  => 'nullable|array',
            'roles.*'                => 'sometimes|exists:' . Role::class . ',id',
            'permissions'            => 'nullable|array',
            'permissions.*'          => 'sometimes|exists:' . Permission::class . ',id',
            'is_department_leader'   => 'array',
            'is_department_leader.*' => 'boolean',
            'status'                 => 'required|in:' . AdminUser::STATUS_ENABLED . ',' . AdminUser::STATUS_DISABLED,
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            // 使用 LogBatch::withinBatch 来实现批量日志记录
            $admin = LogBatch::withinBatch(function() use ($validated) {
                $avatarMaterrial = Material::where('uuid', $validated['avatar']??'')
                                           ->first();

                $user = User::where('mobile', $validated['mobile'])->first();

                $admin = AdminUser::create([
                    'uuid'      => Str::uuid()->toString(),
                    'username'  => $validated['username'],
                    'password'  => Hash::make($validated['password']),
                    'true_name' => $validated['true_name'],
                    'avatar'    => $avatarMaterrial ? [
                        'path'     => $avatarMaterrial->path,
                        'provider' => $avatarMaterrial->provider,
                        'uuid'     => $avatarMaterrial->uuid,
                    ] : ['path' => config('uc.default_avatar'), 'provider' => 'url', 'uuid' => ''],
                    'mobile'    => $validated['mobile'],
                    'status'    => AdminUser::STATUS_ENABLED,
                    'bind_user_uuid' => $user?->uuid ?? null,
                ]);
                
                // 关联部门，设置部门负责人
                $departmentData = [];
                foreach ($validated['department_ids'] as $departmentId) {
                    $departmentData[$departmentId] = [
                        'is_leader' => $validated['is_department_leader'][$departmentId] ?? false,
                        'sort'      => $validated['is_department_leader'][$departmentId] ?? 1,
                    ];
                }
                $admin->departments()->attach($departmentData);

                // 分配角色
                if (isset($validated['roles'])) {
                    $admin->syncRoles(array_map('intval', $validated['roles']));
                }

                // 分配权限
                if (isset($validated['permissions'])) {
                    $admin->syncPermissions(array_map('intval', $validated['permissions']));
                }

                // 变更后关联
                $newDepartments = $admin->departments->pluck('name', 'id')->toArray();
                $newRoles = $admin->roles->pluck('name', 'id')->toArray();
                $newPermissions = $admin->permissions->pluck('name', 'id')->toArray();

                // 只有有任何一项有内容才记录日志
                if (empty($newDepartments) && empty($newRoles) && empty($newPermissions)) {
                    return $admin;
                }

                $relationsChanges = [
                    'departments' => [
                        'attached' => $newDepartments,
                        'detached' => [],
                    ],
                    'roles' => [
                        'attached' => $newRoles,
                        'detached' => [],
                    ],
                    'permissions' => [
                        'attached' => $newPermissions,
                        'detached' => [],
                    ]
                ];

                // 记录自定义活动日志，补充关联数据的信息
                activity()
                    ->performedOn($admin)
                    ->useLog('admin')
                    ->withProperties([
                        'custom_message' => "创建了管理员 {$admin->display_name}",
                        'admin_uuid' => $admin->uuid,
                        'admin_name' => $admin->display_name,
                        'username' => $admin->username,
                        'operation_type' => 'admin_user_create_with_relations',
                        'relations_changes' => $relationsChanges
                    ])
                    ->log('创建管理员及关联数据');
                
                return $admin;
            });

            DB::commit();

            return Respond::success(AdminUserResource::make($admin));
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 获取管理员详情
     *
     */
    public function show($uuid) {
        $admin = AdminUser::with([
            'roles',
            'departments',
            'permissions',
            'bindUser',
        ])
                          ->findOrFail($uuid);

        return Respond::success(AdminUserResource::make($admin));
    }

    /**
     * 更新管理员
     *
     * @throws AdminException|\Throwable
     */
    public function update(Request $request, $uuid) {
        $admin = AdminUser::findOrFail($uuid);

        $validated = $request->validate([
            'true_name'              => 'required|string|max:50',
            'avatar'                 => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)
                                        ->first();
                    if ($value && !$material) {
                        $fail('无效的头像资源');
                    }
                },
            ],
            'mobile'                 => [
                'nullable',
                'string',
                'isMobile',
                Rule::unique(AdminUser::class)
                    ->ignore($uuid, 'uuid'),
            ],
            'password'               => [
                'nullable',
                'min:8',
                'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
            ],
            'department_ids'         => 'required|array',
            'department_ids.*'       => 'required|exists:' . AdminDepartment::class . ',id',
            'roles'                  => 'nullable|array',
            'roles.*'                => 'sometimes|exists:' . Role::class . ',id',
            'permissions'            => 'nullable|array',
            'permissions.*'          => 'sometimes|exists:' . Permission::class . ',id',
            'is_department_leader'   => 'array',
            'is_department_leader.*' => 'boolean',
            'status'                 => 'required|in:' . AdminUser::STATUS_ENABLED . ',' . AdminUser::STATUS_DISABLED,
        ], $this->validationMessages);

        try {
            DB::beginTransaction();
            
            // 使用 LogBatch::withinBatch 来实现批量日志记录
            LogBatch::withinBatch(function() use ($admin, $validated) {
                $avatarMaterrial = Material::where('uuid', $validated['avatar']??'')
                                           ->first();

                $updateData = [
                    'true_name' => $validated['true_name'],
                    'avatar'    => $avatarMaterrial ? [
                        'path'     => $avatarMaterrial->path,
                        'provider' => $avatarMaterrial->provider,
                        'uuid'     => $avatarMaterrial->uuid,
                    ] : ['path' => config('uc.default_avatar'), 'provider' => 'url', 'uuid' => ''],
                    'mobile'    => $validated['mobile'],
                    'status'    => $validated['status'],
                ];

                if (!empty($validated['password'])) {
                    $updateData['password'] = Hash::make($validated['password']);
                }

                // 记录变更前的关联
                $oldDepartments = $admin->departments->pluck('name', 'id')->toArray();
                $oldRoles = $admin->roles->pluck('name', 'id')->toArray();
                $oldPermissions = $admin->permissions->pluck('name', 'id')->toArray();

                // 更新基本信息
                $admin->update($updateData);

                // 更新部门关联
                $departmentData = [];
                foreach ($validated['department_ids'] as $departmentId) {
                    $departmentData[$departmentId] = [
                        'is_leader' => $validated['is_department_leader'][$departmentId] ?? false,
                        'sort'      => $validated['is_department_leader'][$departmentId] ?? 1,
                    ];
                }
                $admin->departments()->sync($departmentData);

                // 更新角色
                if (isset($validated['roles'])) {
                    $admin->syncRoles(array_map('intval', $validated['roles']));
                }

                // 更新权限
                if (isset($validated['permissions'])) {
                    $admin->syncPermissions(array_map('intval', $validated['permissions']));
                }

                // 变更后关联
                $newDepartments = $admin->departments->pluck('name', 'id')->toArray();
                $newRoles = $admin->roles->pluck('name', 'id')->toArray();
                $newPermissions = $admin->permissions->pluck('name', 'id')->toArray();

                // 计算 detached 和 attached
                $detachedDepartments = array_diff_key($oldDepartments, $newDepartments);
                $attachedDepartments = array_diff_key($newDepartments, $oldDepartments);
                
                $detachedRoles = array_diff_key($oldRoles, $newRoles);
                $attachedRoles = array_diff_key($newRoles, $oldRoles);
                
                $detachedPermissions = array_diff_key($oldPermissions, $newPermissions);
                $attachedPermissions = array_diff_key($newPermissions, $oldPermissions);

                // 检查是否有变更
                $hasDepartmentChange = !empty($detachedDepartments) || !empty($attachedDepartments);
                $hasRoleChange = !empty($detachedRoles) || !empty($attachedRoles);
                $hasPermissionChange = !empty($detachedPermissions) || !empty($attachedPermissions);

                if (!$hasDepartmentChange && !$hasRoleChange && !$hasPermissionChange) {
                    return; // 没有变更则不记录日志
                }

                $relationsChanges = [
                    'departments' => [
                        'attached' => $attachedDepartments,
                        'detached' => $detachedDepartments
                    ],
                    'roles' => [
                        'attached' => $attachedRoles,
                        'detached' => $detachedRoles
                    ],
                    'permissions' => [
                        'attached' => $attachedPermissions,
                        'detached' => $detachedPermissions
                    ]
                ];

                // 记录自定义活动日志，补充关联数据的变更信息
                activity()
                    ->performedOn($admin)
                    ->useLog('admin')
                    ->withProperties([
                        'custom_message' => "更新了管理员 {$admin->display_name} 的信息及关联数据",
                        'admin_uuid' => $admin->uuid,
                        'admin_name' => $admin->display_name,
                        'operation_type' => 'admin_user_update_with_relations',
                        'relations_changes' => $relationsChanges
                    ])
                    ->log('更新管理员及关联数据');
            });

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [$e->getMessage()]);
        }
    }

    /**
     * 删除管理员
     *
     * @param string $uuid
     * @return \Illuminate\Http\JsonResponse
     * @throws AdminException|\Throwable
     */
    public function destroy($uuid)
    {
        if ($uuid === Auth::guard('admin')->id()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION, [], '不能删除自己的账号');
        }

        $admin = AdminUser::where('uuid', $uuid)->firstOrFail();

        if ($admin->id == 1) {
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [], '超级管理员无法删除');
        }

        try {
            // 使用 LogBatch::withinBatch 来实现批量日志记录
            LogBatch::withinBatch(function() use ($admin) {
                // 先获取关联数据，用于记录日志
                $oldDepartments = $admin->departments->pluck('name', 'id')->toArray();
                $oldRoles = $admin->roles->pluck('name', 'id')->toArray();
                $oldPermissions = $admin->permissions->pluck('name', 'id')->toArray();
                
                // 如果三者都为空则不记录日志
                if (empty($oldDepartments) && empty($oldRoles) && empty($oldPermissions)) {
                    DB::transaction(function () use ($admin) {
                        $admin->departments()->detach();
                        $admin->roles()->detach();
                        $admin->permissions()->detach();
                        $admin->oauthClients()->detach();
                        $admin->delete();
                    });
                    return;
                }
                
                $relationsChanges = [
                    'departments' => [
                        'attached' => [],
                        'detached' => $oldDepartments
                    ],
                    'roles' => [
                        'attached' => [],
                        'detached' => $oldRoles
                    ],
                    'permissions' => [
                        'attached' => [],
                        'detached' => $oldPermissions
                    ]
                ];
                
                DB::transaction(function () use ($admin) {
                    $admin->departments()->detach();
                    $admin->roles()->detach();
                    $admin->permissions()->detach();
                    $admin->oauthClients()->detach();
                    $admin->delete();
                });
                
                // 记录自定义活动日志
                activity()
                    ->useLog('admin') // 设置日志名称为 'admin'
                    ->withProperties([
                        'custom_message' => "删除了管理员 {$admin->display_name}",
                        'admin_uuid' => $admin->uuid,
                        'admin_name' => $admin->display_name,
                        'username' => $admin->username,
                        'operation_type' => 'admin_user_delete',
                        'relations_changes' => $relationsChanges
                    ])
                    ->log('删除管理员及其关联数据');
            });

            DB::commit();
            return Respond::success(null, '删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 修改管理员状态
     * @throws AdminException|\Throwable
     */
    public function changeStatus(Request $request, $uuid) {
        // 不能修改自己的状态
        if ($uuid === Auth::guard('admin')
                          ->id()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION, [], '不能修改自己的状态');
        }

        $validated = $request->validate([
            'status' => 'required|in:' . AdminUser::STATUS_ENABLED . ',' . AdminUser::STATUS_DISABLED,
            'remark' => 'required|string|max:200',
        ], $this->validationMessages);

        $admin = AdminUser::findOrFail($uuid);

        try {
            DB::beginTransaction();

            $admin->update(['status' => $validated['status']]);

            // 记录操作日志
            // AdminOperationLog::create([
            //     'admin_user_uuid' => Auth::guard('admin')
            //                              ->id(),
            //     'module'          => 'admin_users',
            //     'action'          => 'change_status',
            //     'status'          => AdminOperationLog::STATUS_SUCCESS,
            //     'remark'          => $validated['remark'],
            // ]);

            DB::commit();

            return Respond::success();

        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [$e->getMessage()]);
        }
    }

    /**
     * 获取管理员操作日志
     */
    public function operationLogs(Request $request, $uuid) {
        $logs = AdminOperationLog::where('admin_id', $uuid)
                                 ->when($request->input('module'), function ($query, $module) {
                                     $query->where('module', $module);
                                 })
                                 ->when($request->input('start_date'), function ($query, $startDate) {
                                     $query->where('created_at', '>=', $startDate);
                                 })
                                 ->when($request->input('end_date'), function ($query, $endDate) {
                                     $query->where('created_at', '<=', $endDate);
                                 })
                                 ->orderByDesc('id')
                                 ->paginate($request->input('per_page', 15));

        return Respond::success($logs);
    }

    /**
     * 批量修改管理员状态
     * @throws AdminException|\Throwable
     */
    public function batchChangeStatus(Request $request)
    {
        $validated = $request->validate([
            'uuids' => 'required|array',
            'uuids.*' => 'required|exists:' . AdminUser::class . ',uuid',
            'status' => 'required|in:' . AdminUser::STATUS_ENABLED . ',' . AdminUser::STATUS_DISABLED,
            'remark' => 'required|string|max:200',
        ], $this->validationMessages);

        // 不能修改自己的状态
        $currentAdminUuid = Auth::guard('admin')->id();
        if (in_array($currentAdminUuid, $validated['uuids'])) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION, [], '不能修改自己的状态');
        }

        try {
            DB::beginTransaction();

            $admins = AdminUser::whereIn('uuid', $validated['uuids'])->get();
            
            // 超级管理员不能被修改
            foreach ($admins as $admin) {
                if ($admin->id == 1) {
                    throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [], '超级管理员状态不能被修改');
                }
            }
            
            // 批量更新状态
            AdminUser::whereIn('uuid', $validated['uuids'])->update([
                'status' => $validated['status']
            ]);
            
            // 记录批量操作日志
            AdminUser::logBatchActivity(
                '批量' . ($validated['status'] == AdminUser::STATUS_ENABLED ? '启用' : '禁用') . '管理员', 
                $admins->all(),
                [
                    'status' => $validated['status'],
                    'status_text' => $validated['status'] == AdminUser::STATUS_ENABLED ? '启用' : '禁用',
                    'remark' => $validated['remark'],
                    'admin_count' => count($validated['uuids']),
                ]
            );

            DB::commit();

            return Respond::success();

        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [$e->getMessage()]);
        }
    }
}
