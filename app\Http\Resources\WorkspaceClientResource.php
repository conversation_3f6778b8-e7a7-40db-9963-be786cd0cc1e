<?php

namespace App\Http\Resources;

use App\Enums\OAuthJsApiEnum;
use App\Enums\OAuthScopeEnum;
use App\Models\Client;
use App\Utils\RedirectToBuilder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Client Resource
 * @property-read \App\Models\Client $resource
 * @mixin \App\Models\Client
 */
class WorkspaceClientResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'client_id' => $this->client_key,
            'icon' => $this->display_icon,
            'name' => $this->name,
            'description' => $this->description,
            'provider' => $this->provider,
            'client_type' => $this->client_type,
            'show_in_workspace' => $this->show_in_workspace,
            'redirect_to' => match ($this->client_type) {
                Client::CLIENT_TYPE_H5 => $this->workspace_redirect_url ? RedirectToBuilder::external($this->workspace_redirect_url,['cg_needlogin'=>1]) : null,
                Client::CLIENT_TYPE_MINIAPP => RedirectToBuilder::cgMiniApp($this->client_key,['cg_needlogin'=>1]),
                default => null,
            },
        ];
    }
}
