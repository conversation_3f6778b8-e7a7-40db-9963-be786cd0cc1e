<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Facades\AdminPermission;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\PermissionResource;
use App\Services\PermissionGroupService;
use App\Utils\Respond;
use Illuminate\Http\Request;
use App\Models\Permission;
use App\Models\Role;

class PermissionsController extends AdminBaseController
{
    protected $validationMessages = [
        'name.required'        => '权限标识不能为空',
        'name.string'          => '权限标识必须是字符串',
        'name.unique'          => '权限标识已存在',
        'name.regex'           => '权限标识格式错误,必须为: 模块.操作',
        'description.required' => '权限描述不能为空',
        'description.string'   => '权限描述必须是字符串',
        'description.max'      => '权限描述不能超过200个字符',
        'guard_name.required'  => '守卫名称不能为空',
        'guard_name.string'    => '守卫名称必须是字符串',
    ];

    protected $permissionGroupService;

    protected const PERMISSION_MAP = [
        'index'             => '权限管理.查看列表',
        'tree'              => '权限管理.查看权限树',
        'show'              => '权限管理.查看详情',
        'store'             => '权限管理.创建',
        'update'            => '权限管理.编辑',
        'destroy'           => '权限管理.删除',
        'getRolePermissions' => '权限管理.获取角色权限',
    ];

    public function __construct(PermissionGroupService $permissionGroupService) {
        $this->permissionGroupService = $permissionGroupService;
    }

    /**
     * 获取权限列表(分组展示)
     */
    public function index() {
        $groups = $this->permissionGroupService->getGroups();

        return Respond::success($groups);
    }

    /**
     * 获取权限树
     */
    public function tree() {
        $tree = $this->permissionGroupService->getPermissionTree();

        return Respond::success($tree);
    }

    public function options() {
        return Respond::success([]);
    }

    public function show($id) {
        $permission = Permission::findOrFail($id);

        return Respond::success(PermissionResource::make($permission));
    }

    /**
     * 创建权限
     */
    public function store(Request $request) {
        $validated = $request->validate([
            'name'        => 'required|string|unique:permissions',
            'description' => 'nullable|string',
        ]);

        // 验证权限名称格式
        if (!$this->permissionGroupService->validatePermissionName($validated['name'])) {
            return Respond::error('权限名称格式错误，需要包含分组信息，如：用户.create');
        }

        Permission::create([
            'name'        => $validated['name'],
            'description' => $validated['description'] ?? "",
            'guard_name'  => 'admin',
        ]);

        return Respond::success();
    }

    /**
     * 更新权限
     */
    public function update(Request $request, $id) {
        $permission = Permission::findOrFail($id);

        $validated = $request->validate([
            'name'        => 'required|string|unique:permissions,name,' . $id,
            'description' => 'nullable|string',
        ]);

        // 验证权限名称格式
        if (!$this->permissionGroupService->validatePermissionName($validated['name'])) {
            return Respond::error('权限名称格式错误，需要包含分组信息，如：用户.create');
        }

        $permission->update($validated);

        return Respond::success();
    }

    /**
     * 删除权限
     */
    public function destroy($id) {
        $permission = Permission::findOrFail($id);

        if ($permission->roles()
                       ->exists()) {
            return Respond::error('该权限已被角色使用，无法删除');
        }

        $permission->delete();

        return Respond::success();
    }

    /**
     * 获取角色的权限设置
     */
    public function getRolePermissions($roleId) {
        $tree = AdminPermission::getRolePermissions($roleId);

        return Respond::success($tree);
    }


    /**
     * 附加的权限验证规则
     */
    protected function validatePermissionName($permission) {
        if (!app(PermissionGroupService::class)->validatePermissionName($permission)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_INVALID_PERMISSION_FORMAT);
        }
    }

    /**
     * 标记选中的权限
     */
    private function markSelectedPermissions(array &$tree, array $selectedIds): void {
        foreach ($tree as &$node) {
            if (isset($node['id'])) {
                $node['selected'] = in_array($node['id'], $selectedIds);
            }
            if (isset($node['children'])) {
                $this->markSelectedPermissions($node['children'], $selectedIds);
            }
        }
    }
}
