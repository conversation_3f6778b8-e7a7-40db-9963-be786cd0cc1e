<?php

namespace App\Http\Resources;

use App\Models\PushTemplate;
use App\Utils\Tools;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

/**
 * User Message Resource
 * @property-read \App\Models\PushMessage $resource
 * @mixin \App\Models\PushMessage
 */
class PushMessageResource extends JsonResource
{
    public function toArray(Request $request) {
        return [
            'msg_key' => $this->msg_key,
            'title' => $this->parseTemplateContent($this->title, $this->template_params ?? []),
            'content' => $this->parseTemplateContent($this->content, $this->template_params ?? []),
            $this->merge(Tools::mergeDateTimeFormat('push_time', $this->push_time)),
            'template_params' => $this->template_params ?? [],
            'extend_params' => Arr::except($this->extend_params ?? [], 'redirect_to'),
            'redirect_to' => $this->extend_params['redirect_to'] ?? '',
            'show_client_info' => $this->show_client_info?? PushTemplate::SHOW_CLIENT_INFO_NO,
            'banner_url' => $this->banner_url??'',
            'client_info' => $this->whenLoaded('oauthClient', function () {
                return [
                    'name' => $this->oauthClient->name,
                    'icon' => $this->oauthClient->display_icon,
                ];
            }),
        ];
    }

    private function parseTemplateContent($templateContent, array $params) {
        foreach ($params as $key => $value) {
            $placeholder = '#{' . $key . '}#';
            $templateContent = str_replace($placeholder, $value, $templateContent);
        }

        return $templateContent;
    }
}
