<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('admin_users', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('UUID');
            $table->string('username', 50)->unique()->comment('用户名');
            $table->string('password', 100)->comment('密码');
            $table->string('avatar')->nullable()->comment('头像');
            $table->string('nickname',20)->nullable()->comment('昵称');
            $table->string('true_name',20)->nullable()->comment('真实姓名');
            $table->string('mobile', 20)->nullable()->index()->comment('手机号');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->string('last_login_ip', 45)->nullable()->comment('最后登录IP');
            $table->timestamp('last_login_time')->nullable()->comment('最后登录时间');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('admin_departments', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->comment('部门名称');
            $table->string('code', 50)->unique()->comment('部门编码');
            $table->integer('lft')->unsigned()->comment('左值');
            $table->integer('rgt')->unsigned()->comment('右值');
            $table->integer('level')->unsigned()->comment('层级');
            $table->integer('sort')->default(0)->comment('排序值');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->string('remark')->nullable()->comment('备注');
            $table->timestamps();
            $table->softDeletes();

            // 添加索引
            $table->index(['lft', 'rgt']);
            $table->index('level');
        });

        Schema::create('admin_department_admin_user', function (Blueprint $table) {
            $table->id();
            $table->uuid('admin_user_uuid')->comment('管理员UUID');
            $table->unsignedBigInteger('admin_department_id')->comment('部门ID');
            $table->tinyInteger('is_leader')->default(0)->comment('是否部门负责人:1是,0否');
            $table->integer('sort')->default(0)->comment('排序');
            $table->timestamps();

            // 添加联合唯一索引
            $table->unique(['admin_user_uuid', 'admin_department_id'],'unique_admin_user_department');
        });

        Schema::create('admin_operation_logs', function (Blueprint $table) {
            $table->id();
            $table->uuid('admin_user_uuid')->comment('管理员UUID');
            $table->string('module', 50)->comment('模块');
            $table->string('action', 50)->comment('操作');
            $table->string('url')->comment('请求地址');
            $table->string('method', 10)->comment('请求方法');
            $table->json('params')->nullable()->comment('请求参数');
            $table->string('ip', 45)->nullable()->comment('IP地址');
            $table->string('user_agent')->nullable()->comment('User Agent');
            $table->tinyInteger('status')->default(1)->comment('状态:1成功,0失败');
            $table->string('remark')->nullable()->comment('备注');
            $table->timestamps();

            // 添加索引
            $table->index('created_at');
        });


        $teams = config('permission.teams');
        $tableNames = config('permission.table_names');
        $columnNames = config('permission.column_names');
        $pivotRole = $columnNames['role_pivot_key'] ?? 'role_id';
        $pivotPermission = $columnNames['permission_pivot_key'] ?? 'permission_id';

        if (empty($tableNames)) {
            throw new \Exception('Error: config/permission.php not loaded. Run [php artisan config:clear] and try again.');
        }
        if ($teams && empty($columnNames['team_foreign_key'] ?? null)) {
            throw new \Exception('Error: team_foreign_key on config/permission.php not loaded. Run [php artisan config:clear] and try again.');
        }

        Schema::table($tableNames['permissions'], function (Blueprint $table) {
            $table->string('description')->nullable()->after('name')->comment('权限描述');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('admin_users');
        Schema::dropIfExists('admin_departments');
        Schema::dropIfExists('admin_department_admin_user');
        Schema::dropIfExists('admin_operation_logs');

        Schema::table('permissions', function (Blueprint $table) {
            $table->dropColumn('description');
        });
    }
};
