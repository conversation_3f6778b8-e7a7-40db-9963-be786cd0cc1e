<?php

namespace App\Http\Resources;

use App\Models\User;
use App\Models\UserSubscription;
use App\Utils\OAuthCacher;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 *  User Subscription Resource
 *
 * @property-read UserSubscription $resource
 * @mixin UserSubscription
 */
class SubscriptionUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array {
        return [
            'open_id' => $this->oauthBindings->first()?->open_id ?? '',
            'nickname' => $this->display_nickname,
            'avatar' => $this->display_avatar,
            // 'user_uuid' => $this->user_uuid,
            // 'status' => $this->status,
            // 'created_at' => $this->created_at,
        ];
    }
}
