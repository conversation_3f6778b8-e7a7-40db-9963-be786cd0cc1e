<?php

namespace App\Http\Resources;

use App\Enums\OAuthJsApiEnum;
use App\Enums\OAuthScopeEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Client Resource
 * @property-read \App\Models\Client $resource
 * @mixin \App\Models\Client
 */
class ClientResource extends JsonResource
{
    public function toArray(Request $request) {

        return [
            'client_id' => $this->client_key,
            'icon'=>$this->display_icon,
            'name' => $this->name,
            'description' => $this->description,
            'provider' => $this->provider,
            'client_type' => $this->client_type,
            'safe_domains' => $this->auth_safe_domains,
            $this->mergeWhen($this->allowed_scopes, [
                'allowed_scopes' => collect($this->allowed_scopes)->map(function ($scope) {
                    return [
                        'key' => $scope,
                        'value' => OAuthScopeEnum::from($scope)
                                                 ->label(),
                    ];
                }),
            ]),
            $this->mergeWhen($this->disabled_jsapis, [
                'disabled_jsapis' => collect($this->disabled_jsapis)->map(function ($jsapi) {
                    return [
                        'key' => $jsapi,
                        'value' => OAuthJsApiEnum::from($jsapi)
                                             ->label(),
                    ];
                }),
            ]),
        ];
    }
}
