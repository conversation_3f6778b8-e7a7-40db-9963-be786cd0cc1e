<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权回调</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 16px;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 24px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            margin: 12px 0;
        }

        .subtitle {
            color: #666;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #1890ff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .user-info {
            display: none;
            margin-top: 20px;
        }

        .user-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 16px;
            background-color: #f0f0f0;
        }

        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-name {
            font-size: 20px;
            font-weight: 500;
        }

        .user-detail {
            color: #666;
            font-size: 14px;
        }

        .info-row {
            display: flex;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-label {
            width: 100px;
            color: #666;
        }

        .info-value {
            flex: 1;
            word-break: break-all;
        }

        .message {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            text-align: center;
        }

        .message.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }

        .message.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }

        .token-info {
            display: none;
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 16px;
            border-radius: 8px;
        }

        .token-row {
            margin-bottom: 8px;
            word-break: break-all;
        }

        .token-label {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .token-value {
            font-family: monospace;
            font-size: 13px;
            color: #666;
        }

        .login-btn {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: #1890ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
            margin-top: 20px;
            transition: background-color 0.3s;
        }

        .login-btn:hover {
            background-color: #40a9ff;
        }

        .error-container {
            display: none;
            text-align: center;
            padding: 40px 20px;
        }

        .error-icon {
            font-size: 48px;
            color: #ff4d4f;
            margin-bottom: 16px;
        }

        .error-message {
            font-size: 18px;
            color: #333;
            font-weight: 500;
            margin-bottom: 8px;
        }
    </style>
</head>

<body>
    <div class="container" id="mainContainer">
        <div class="header">
            <h1 class="title">授权登录</h1>
            <div class="subtitle" id="headerSubtitle">正在检查环境...</div>
        </div>

        <div id="messageBox" class="message" style="display: none;"></div>

        <div id="loginView" style="display: none;">
            <button id="loginButton" class="login-btn">授权登录</button>
        </div>

        <div id="loadingView" class="loading">
            <div class="spinner"></div>
            <div style="margin-top: 16px;">环境检测中...</div>
        </div>

        <div id="userInfo" class="user-info">
            <div class="user-header">
                <div class="avatar">
                    <img id="userAvatar" src="/api/placeholder/80/80" alt="用户头像">
                </div>
                <div>
                    <div class="user-name" id="userNickname"></div>
                    <div class="user-detail" id="userGender"></div>
                </div>
            </div>

            <div class="info-row">
                <div class="info-label">用户ID</div>
                <div class="info-value" id="userOpenId"></div>
            </div>
            <div class="info-row">
                <div class="info-label">手机号码</div>
                <div class="info-value" id="userMobile"></div>
            </div>
        </div>

        <div id="tokenInfo" class="token-info">
            <div class="token-row">
                <div class="token-label">Access Token</div>
                <div class="token-value" id="accessToken"></div>
            </div>
            <div class="token-row">
                <div class="token-label">过期时间</div>
                <div class="token-value" id="expiresIn"></div>
            </div>
            <div class="token-row">
                <div class="token-label">Refresh Token</div>
                <div class="token-value" id="refreshToken"></div>
            </div>
        </div>
    </div>

    <div class="error-container" id="errorContainer">
        <div class="error-icon">⚠️</div>
        <div class="error-message">请在常观App内打开页面</div>
    </div>

    <script>
        // 环境检查
        function isCGAppWeb() {
            const ua = window.navigator.userAgent.toLowerCase();
            return ua.indexOf('changguan') !== -1;
        }

        // CGApp就绪检查
        function CGAppWebReady() {
            return new Promise((resolve, reject) => {
                if (isCGAppWeb()) {
                    if (window.cgapp) {
                        resolve();
                    } else {
                        const timeout = setTimeout(() => {
                            reject(new Error('常观App环境检测超时，未找到cgapp对象'));
                        }, 3000);

                        document.addEventListener(
                            'cgappjsbridgeready',
                            () => {
                                clearTimeout(timeout);
                                resolve();
                            },
                            false
                        );
                    }
                } else {
                    reject(new Error('请在常观App内打开页面'));
                }
            });
        }

        // 获取URL参数
        function getQueryParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 显示消息
        function showMessage(text, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = text;
            messageBox.className = `message ${type}`;
            messageBox.style.display = 'block';
        }

        // 显示用户信息
        function displayUserInfo(userInfo) {
            document.getElementById('userNickname').textContent = userInfo.nickname || '未知用户';
            document.getElementById('userGender').textContent = userInfo.gender || '未知';
            document.getElementById('userOpenId').textContent = userInfo.open_id || '';
            document.getElementById('userMobile').textContent = userInfo.mobile || '未绑定';

            // 设置头像
            if (userInfo.avatar) {
                document.getElementById('userAvatar').src = userInfo.avatar;
            }

            // 隐藏加载视图，显示用户信息
            document.getElementById('loadingView').style.display = 'none';
            document.getElementById('userInfo').style.display = 'block';
            document.getElementById('headerSubtitle').textContent = '授权成功';
        }

        // 显示令牌信息
        function displayTokenInfo(tokenInfo) {
            if (!tokenInfo) {
                console.error('Token info is undefined');
                return;
            }

            document.getElementById('accessToken').textContent = tokenInfo.access_token || '';

            // 安全处理过期时间计算
            let expiresText = '未知';
            if (tokenInfo.expires_in && !isNaN(tokenInfo.expires_in)) {
                expiresText = `${Math.floor(tokenInfo.expires_in / 86400)} 天`;
            }
            document.getElementById('expiresIn').textContent = expiresText;

            document.getElementById('refreshToken').textContent = tokenInfo.refresh_token || '';

            // 显示令牌信息区域
            document.getElementById('tokenInfo').style.display = 'block';
        }

        // 显示环境错误
        function showEnvironmentError() {
            document.getElementById('mainContainer').style.display = 'none';
            document.getElementById('errorContainer').style.display = 'block';
        }

        // 获取访问令牌
        async function getAccessToken(code) {
            return new Promise((resolve, reject) => {
                const clientId = getQueryParam('client_id');

                if (!clientId || !code) {
                    reject(new Error('缺少必要的参数'));
                    return;
                }

                window.cgapp.systemRequest({
                    client_id: 'default',
                    method: 'POST',
                    uri: 'https://uctest.xiao5.cn/api/test/access-token',
                    params: {
                        client_id: clientId,
                        code: code
                    },
                    success: (res) => {
                        if (res.statusCode == 200 && res.data && res.data.errcode == 0) {
                            resolve(res.data.data);
                        } else {
                            const errorMsg = res.data && res.data.errmsg ? res.data.errmsg : '获取令牌失败';
                            reject(new Error(errorMsg));
                        }
                    },
                    fail: (error) => {
                        reject(new Error(error || '网络请求失败'));
                    }
                });
            });
        }

        // 获取用户信息
        async function getUserInfo(accessToken, openId) {
            return new Promise((resolve, reject) => {
                const clientId = getQueryParam('client_id');

                if (!accessToken || !openId) {
                    reject(new Error('访问令牌或用户ID缺失'));
                    return;
                }

                cgapp.systemRequest({
                    client_id: 'default',
                    method: 'POST',
                    uri: 'https://uctest.xiao5.cn/api/test/user-info',
                    params: {
                        client_id: clientId,
                        open_id: openId,
                        access_token: accessToken
                    },
                    success: (res) => {
                        if (res.statusCode == 200 && res.data && res.data.errcode == 0) {
                            resolve(res.data.data);
                        } else {
                            const errorMsg = res.data && res.data.errmsg ? res.data.errmsg : '获取用户信息失败';
                            reject(new Error(errorMsg));
                        }
                    },
                    fail: (error) => {
                        reject(new Error(error || '网络请求失败'));
                    }
                });
            });
        }

        // 执行登录操作
        async function performLogin() {
            try {
                document.getElementById('loginView').style.display = 'none';
                document.getElementById('loadingView').style.display = 'block';
                document.getElementById('headerSubtitle').textContent = '正在授权...';

                const clientId = getQueryParam('client_id') || '';
                const redirectUri = getQueryParam('redirect_uri') || '';

                // 调用cgapp.login获取授权码
                cgapp.ready(() => {
                    cgapp.login({
                        client_id: clientId,
                        scopes: 'base',
                        state: 'oauth_callback',
                        redirect_uri: redirectUri,
                        success: async (res) => {
                            try {
                                // 从login返回中获取code和open_id
                                const code = res.code;

                                if (!code) {
                                    throw new Error('未获取到授权码');
                                }

                                // 获取令牌
                                const tokenInfo = await getAccessToken(code);
                                const openId = tokenInfo.open_id;
                                if (tokenInfo) {
                                    displayTokenInfo(tokenInfo);

                                    // 获取用户信息
                                    const userInfo = await getUserInfo(tokenInfo.access_token, openId);
                                    if (userInfo) {
                                        displayUserInfo(userInfo);
                                        showMessage('授权成功，用户信息已加载', 'success');
                                    }
                                }
                            } catch (error) {
                                document.getElementById('loadingView').style.display = 'none';
                                document.getElementById('loginView').style.display = 'block';
                                console.error('处理登录响应失败:', error);
                                showMessage(error.message || '授权处理失败，请重试', 'error');
                            }
                        },
                        fail: (error) => {
                            document.getElementById('loadingView').style.display = 'none';
                            document.getElementById('loginView').style.display = 'block';
                            console.error('登录失败:', error);
                            showMessage('授权登录失败：' + (error.errmsg || '未知错误'), 'error');
                        }
                    });
                })
            } catch (error) {
                document.getElementById('loadingView').style.display = 'none';
                document.getElementById('loginView').style.display = 'block';
                console.error('登录过程错误:', error);
                showMessage(error.message || '操作失败，请重试', 'error');
            }
        }

        // 初始化
        async function initialize() {
            try {
                // 检查环境
                await CGAppWebReady();

                const clientId = getQueryParam('client_id') || '';
                cgapp.config({ client_id: clientId });
                // 环境检测成功，显示登录按钮
                document.getElementById('loadingView').style.display = 'none';
                document.getElementById('loginView').style.display = 'block';
                document.getElementById('headerSubtitle').textContent = '点击下方按钮进行授权';

                // 设置登录按钮点击事件
                document.getElementById('loginButton').addEventListener('click', performLogin);

            } catch (error) {
                console.error('初始化失败:', error);
                // 环境检测失败，显示错误信息
                showEnvironmentError();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initialize);
    </script>
</body>

</html>