<?php

namespace App\Models;

use App\Services\CloudFiles\Facades\CloudFiles;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Models\Traits\AdminActivityLogTrait;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * @property int $id
 * @property string $uuid UUID
 * @property string|null $enterprise_id 所属企业ID
 * @property string|null $type 用户类型:system,enterprise
 * @property string $username 用户名
 * @property string $password 密码
 * @property array<array-key, mixed>|null $avatar 头像
 * @property string|null $nickname 昵称
 * @property string|null $true_name 真实姓名
 * @property string|null $display_name 显示名字
 * @property string|null $mobile 手机号
 * @property string|null $short_mobile 短号
 * @property string|null $office_phone 办公电话
 * @property int $status 状态:1启用,0禁用
 * @property string|null $bind_user_uuid 绑定前端用户信息
 * @property string|null $last_login_ip 最后登录IP
 * @property \Illuminate\Support\Carbon|null $last_login_time 最后登录时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Client> $accessibleOauthClients
 * @property-read int|null $accessible_oauth_clients_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Activitylog\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \App\Models\User|null $bindUser
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AdminDepartment> $departments
 * @property-read int|null $departments_count
 * @property-read mixed $display_avatar
 * @property-read \App\Models\Enterprise|null $enterprise
 * @property-read \App\Models\OAuthUserBinding|null $oAuthUserBinding
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Client> $oauthClients
 * @property-read int|null $oauth_clients_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read mixed $status_text
 * @property-read mixed $type_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereBindUserUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereDisplayName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereEnterpriseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereLastLoginIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereLastLoginTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereNickname($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereOfficePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereShortMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereTrueName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser whereUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser withoutRole($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminUser withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAdminUser
 */
class AdminUser extends Authenticatable implements JWTSubject
{
    use HasFactory, SoftDeletes, HasRoles;
    use HasUuids;
    use AdminActivityLogTrait;

    protected $guard_name = 'admin';


    protected $primaryKey = 'uuid';
    // protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'uuid',
        'username',
        'password',
        'avatar',
        'nickname',
        'true_name',
        'mobile',
        'short_mobile',
        'office_phone',
        'status',
        'bind_user_uuid',
        'last_login_ip',
        'last_login_time',
        'enterprise_id',
        'type',
    ];

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'avatar'          => 'array',
        'last_login_time' => 'datetime',
    ];

    protected $appends = [
        'display_avatar',
    ];

    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    public static array $statusMap = [
        self::STATUS_ENABLED  => '启用',
        self::STATUS_DISABLED => '禁用',
    ];

    /**
     * 日志名称
     *
     * @var string
     */
    protected $logName = 'admin_user';

    /**
     * 要记录的字段
     *
     * @var array
     */
    protected $logAttributes = [
        'username',
        'password',
        'avatar',
        'nickname',
        'true_name',
        'display_name',
        'mobile',
        'short_mobile',
        'office_phone',
        'status',
        'status_text',
        'bind_user_uuid',
        'enterprise_id',
    ];

    /**
     * 敏感字段列表
     *
     * @var array
     */
    protected $logSensitiveFields = [
        'password',
        'mobile',
        'short_mobile',
        'office_phone',
    ];

    /**
     * 模型标签
     *
     * @var string
     */
    protected $logModelLabel = '管理员';

    /**
     * 操作类型
     *
     * @var string
     */
    protected $logOperationType = 'admin_user_management';

    const TYPE_SYSTEM = 'system';
    const TYPE_ENTERPRISE = 'enterprise';

    public static array $typeMap = [
        self::TYPE_SYSTEM     => '系统',
        self::TYPE_ENTERPRISE => '企业',
    ];

    protected function typeText(): Attribute
    {
        return Attribute::make(get: fn() => self::$typeMap[$this->type] ?? '未知');
    }

    // 状态文本
    protected function statusText(): Attribute
    {
        return Attribute::make(get: fn() => self::$statusMap[$this->status] ?? '未知');
    }

    protected function displayAvatar(): Attribute
    {
        return Attribute::make(get: fn() => $this->avatar ? CloudFiles::getFileUrl($this->avatar['provider'], $this->avatar['path'], config('uc.avatar_expired_ttl')) : CloudFiles::getFileUrl('url', config('uc.default_avatar'), config('uc.avatar_expired_ttl')));
    }

    public function isActive(): int
    {
        return $this->status === self::STATUS_ENABLED;
    }

    // 与部门的多对多关联
    public function departments()
    {
        return $this->belongsToMany(AdminDepartment::class)
            ->withPivot(['is_leader', 'job_title'])
            ->withTimestamps();
    }

    // 获取管理的部门ID列表
    public function getDepartmentIds()
    {
        return $this->departments()
            ->pluck('departments.id')
            ->toArray();
    }

    // 获取作为负责人的部门
    public function getLeaderDepartments()
    {
        return $this->departments()
            ->wherePivot('is_leader', 1)
            ->get();
    }

    // 操作日志关联
    // public function operationLogs()
    // {
    //     return $this->hasMany(AdminOperationLog::class);
    // }

    public function oAuthUserBinding()
    {
        return $this->hasOne(OAuthUserBinding::class, 'user_uuid', 'uuid')
            ->where('user_type', OAuthUserBinding::USER_TYPE_ADMIN);
    }

    // OAuth Client关联
    public function oauthClients()
    {
        return $this->belongsToMany(Client::class, 'admin_oauth_client', 'admin_uuid', 'oauth_client_id')
            ->withPivot('is_owner')
            ->withTimestamps();
    }

    // 企业关联
    public function enterprise()
    {
        return $this->belongsTo(Enterprise::class, 'enterprise_id', 'id');
    }

    // 作为拥有者的OAuth Client关联
    public function ownedOauthClients()
    {
        return $this->oauthClients()
            ->wherePivot('is_owner', 1);
    }

    // 检查是否可以管理指定的OAuth Client
    public function canManageOauthClient($clientId): bool
    {
        // 超级管理员可以管理所有客户端
        if ($this->hasRole('超级管理员') || $this->id === 1) {
            return true;
        }

        // 检查是否是该客户端的管理员
        return $this->oauthClients()
            ->where('oauth_client_id', $clientId)
            ->exists();
    }

    public function accessibleOauthClients()
    {
        return $this->morphToMany(Client::class, 'accessible', 'oauth_client_accessibles', 'accessible_id', 'oauth_client_id')
            ->where('is_workspace_client', Client::IS_WORKSPACE_CLIENT_YES)
            ->withTimestamps();
    }

    public function getAllAccessibleOauthClients()
    {
        // 获取用户直接可访问的客户端IDs
        $directClientIds = $this->accessibleOauthClients()
            ->pluck('id')
            ->toArray();

        // 获取用户所属部门的所有客户端IDs
        $departmentClientIds = $this->departments()
            ->get()
            ->flatMap(function ($department) {
                return $department->getAllAccessibleOauthClients()
                    ->pluck('id');
            })
            ->unique()
            ->toArray();

        // 合并两个集合并去重
        $allClientIds = array_unique(array_merge($directClientIds, $departmentClientIds));

        // 返回完整的客户端集合
        return Client::whereIn('id', $allClientIds)
            ->where('is_revoked', Client::IS_REVOKED_NO)
            ->get();
    }

    public function canAccessOauthClient($clientId)
    {
        // TODO 检查role
        if ($this->accessibleOauthClients()
            ->where('id', $clientId)
            ->exists()
        ) {
            return true;
        }

        return $this->departments()
            ->get()
            ->contains(function ($department) use ($clientId) {
                return $department->canAccessOauthClient($clientId);
            });
    }

    public function getAllAccessibleOauthClientsWithCache()
    {
        $cacheKey = "admin_accessible_clients:{$this->uuid}";

        return cache()->remember($cacheKey, now()->addHours(6), function () {
            return $this->getAllAccessibleOauthClients();
        });
    }

    public function clearAccessibleOauthClientsCache()
    {
        cache()->forget("admin_accessible_clients:{$this->uuid}");
    }

    public function bindUser()
    {
        return $this->hasOne(User::class, 'uuid', 'bind_user_uuid');
    }

    /**
     * 检查用户是否有指定权限
     *
     * @param $permission
     * @param $arguments
     *
     * @return bool
     */
    //    public function can($permission, $arguments = []): bool {
    //        // 首先检查自身权限(会使用 acl 包的缓存)
    //        if ($this->checkPermissionTo($permission)) {
    //            return true;
    //        }
    //
    //        // 检查所属部门的权限
    //        return $this->departments()
    //                    ->get()
    //                    ->contains(function ($department) use ($permission) {
    //                        return $department->can($permission);
    //                    });
    //    }

    /**
     * 获取用户所有权限（包括从部门继承的权限）
     * 使用缓存优化性能
     */
    //    public function getAllPermissions() {
    //        $cacheKey = "user_permissions:{$this->uuid}";
    //
    //        return cache()->remember($cacheKey, now()->addHours(24), function () {
    //            $permissions = collect();
    //
    //            // 1. 获取用户直接权限
    //            $permissions = $permissions->merge($this->permissions);
    //
    //            // 2. 获取用户角色权限
    //            $permissions = $permissions->merge($this->roles->flatMap(fn($role) => $role->permissions));
    //
    //            // 3. 获取部门及其继承的权限
    //            $permissions = $permissions->merge($this->departments->flatMap(fn($dept) => $dept->getAllPermissions()));
    //
    //            return $permissions->unique('id');
    //        });
    //    }

    /**
     * 清除用户权限缓存
     */
    //    public function clearPermissionCache() {
    //        cache()->forget("user_permissions:{$this->uuid}");
    //    }

    /**
     * 重写权限检查方法以使用缓存
     *
     * @param $permission
     * @param string|null $guardName
     *
     * @return bool
     */
    //    public function hasPermissionTo($permission, ?string $guardName = null): bool {
    //        if (is_string($permission)) {
    //            $permission = Permission::findByName($permission, $this->getDefaultGuardName());
    //        }
    //
    //        return $this->getAllPermissions()
    //                    ->contains($permission);
    //    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }
}
