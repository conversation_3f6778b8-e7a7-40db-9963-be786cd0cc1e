<?php

namespace App\Models;

use App\Models\Traits\AdminActivityLogTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Support\Str;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * @property string $id
 * @property string $enterprise_id 所属企业ID
 * @property string $user_id 所属用户ID
 * @property int $status 状态:1启用,0禁用
 * @property string|null $remark 备注
 * @property string|null $created_by 创建人
 * @property string|null $updated_by 更新人
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Enterprise $enterprise
 * @property-read \App\Models\User $user
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Client> $clients
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Client> $ownedClients
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereEnterpriseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseAdmin whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperEnterpriseAdmin
 */
class EnterpriseAdmin extends Model implements JWTSubject
{
    use HasFactory, SoftDeletes, HasUuids, HasRoles, AdminActivityLogTrait;

    protected $guard_name = 'enterprise_admin';

    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'enterprise_id',
        'user_id',
        'status',
        'remark',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'status' => 'integer',
    ];

    protected $appends = [
        'status_text',
    ];

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    public static array $statusMap = [
        self::STATUS_ENABLED  => '启用',
        self::STATUS_DISABLED => '禁用',
    ];

    /**
     * 日志配置
     */
    protected $logName = 'enterprise_admin';
    protected $logModelLabel = '企业管理员';
    protected $logOperationType = 'enterprise_admin_management';
    protected $logAttributes = [
        'enterprise_id',
        'user_id',
        'status',
        'status_text',
        'remark',
    ];

    /**
     * 模型初始化事件
     */
    protected static function boot()
    {
        parent::boot();

        // 创建前生成UUID
        static::creating(function ($model) {
            $model->id = (string) Str::uuid();
        });
    }

    /**
     * 状态文本属性
     */
    public function getStatusTextAttribute(): string
    {
        return self::$statusMap[$this->status] ?? '未知';
    }

    /**
     * 关联企业
     */
    public function enterprise()
    {
        return $this->belongsTo(Enterprise::class, 'enterprise_id', 'id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'uuid');
    }

    /**
     * 关联的客户端（通过中间表）
     */
    public function clients()
    {
        return $this->belongsToMany(Client::class, 'enterprise_admin_clients', 'enterprise_admin_id', 'client_id')
            ->withPivot(['status', 'remark'])
            ->withTimestamps();
    }

    /**
     * 拥有的客户端（作为拥有者）
     */
    public function ownedClients()
    {
        return $this->belongsToMany(Client::class, 'admin_oauth_client', 'admin_uuid', 'oauth_client_id')
            ->wherePivot('is_owner', 1)
            ->withPivot(['is_owner'])
            ->withTimestamps();
    }

    /**
     * 检查是否可以管理指定的客户端
     */
    public function canManageClient($clientId): bool
    {
        return $this->clients()->where('client_id', $clientId)->exists() ||
               $this->ownedClients()->where('oauth_client_id', $clientId)->exists();
    }

    /**
     * 检查是否拥有指定的客户端
     */
    public function ownsClient($clientId): bool
    {
        return $this->ownedClients()->where('oauth_client_id', $clientId)->exists();
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 作用域：按企业筛选
     */
    public function scopeByEnterprise($query, $enterpriseId)
    {
        return $query->where('enterprise_id', $enterpriseId);
    }

    /**
     * JWT相关方法
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [
            'enterprise_id' => $this->enterprise_id,
            'user_id' => $this->user_id,
            'type' => 'enterprise_admin',
        ];
    }
}
