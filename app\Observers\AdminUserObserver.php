<?php

namespace App\Observers;

use App\Events\AdminUserStatusChangedEvent;
use App\Events\AdminUserUpdateEvent;
use App\Models\AdminUser;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class AdminUserObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the AdminUser "created" event.
     */
    public function created(AdminUser $adminUser): void {
        //
    }

    /**
     * Handle the AdminUser "updated" event.
     */
    public function updated(AdminUser $adminUser): void {
        $watchFields = [
            'avatar',
            'true_name',
            'display_name',
            'mobile',
            'shot_mobile',
            'status',
        ];

        $changes = $adminUser->getChanges();

        $changedWatchFields = array_intersect(array_keys($changes), $watchFields);

        if (!empty($changedWatchFields)) {
            // 如果status字段改变，触发状态变更事件
            if (in_array('status', $changedWatchFields)) {
                event(new AdminUserStatusChangedEvent($adminUser->getAuthIdentifier(), $adminUser->status));
            }

            // 触发用户更新事件，包含所有变更的监听字段
            foreach ($changedWatchFields as $field) {
                if ($field == 'status') {
                    continue;
                }

                event(new AdminUserUpdateEvent($adminUser, $field));
            }


        }
    }

    /**
     * Handle the AdminUser "deleted" event.
     */
    public function deleted(AdminUser $adminUser): void {
        //
    }

    /**
     * Handle the AdminUser "restored" event.
     */
    public function restored(AdminUser $adminUser): void {
        //
    }

    /**
     * Handle the AdminUser "force deleted" event.
     */
    public function forceDeleted(AdminUser $adminUser): void {
        //
    }
}
