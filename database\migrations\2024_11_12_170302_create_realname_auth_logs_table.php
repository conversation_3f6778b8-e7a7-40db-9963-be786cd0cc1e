<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('realname_auth_logs', function (Blueprint $table) {
            $table->id();
            $table->string('outer_order_no');
            $table->unsignedTinyInteger('type')
                  ->default(1);
            $table->bigInteger('user_id');
            $table->string('cert_name', 1000);
            $table->string('cert_no', 1000);

            $table->string('certify_id');
            $table->string('request_id');
            $table->string('passed')
                  ->nullable();
            $table->string('identity_info')
                  ->nullable();
            $table->string('device_token', 1000)
                  ->nullable();
            $table->text('material_info')
                  ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('realname_auth_logs');
    }
};
