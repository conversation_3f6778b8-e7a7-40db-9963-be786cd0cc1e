<?php

namespace App\Models\Traits;

use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * 活动日志记录通用 Trait
 * 用于模型自动记录变更日志
 */
trait LogsActivityTrait
{
    use LogsActivity {
        shouldLogEvent as protected traitShouldLogEvent;
    }

    /**
     * 获取活动日志选项
     *
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly($this->logAttributes ?? ['*'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * 获取活动日志属性
     *
     * @return array
     */
    public function getActivitylogProperties(): array
    {
        return [
            'log_name' => $this->getLogName(),
            'operation_type' => $this->getOperationType(),
        ];
    }

    /**
     * 获取日志名称
     *
     * @return string
     */
    protected function getLogName(): string
    {
        return property_exists($this, 'logName') ? $this->logName : 'admin';
    }

    /**
     * 获取操作类型
     *
     * @return string
     */
    protected function getOperationType(): string
    {
        return property_exists($this, 'logOperationType') 
            ? $this->logOperationType 
            : strtolower(class_basename($this)) . '_management';
    }

    /**
     * 获取描述事件的文本
     *
     * @param string $eventName
     * @return string
     */
    public function getDescriptionForEvent(string $eventName): string
    {
        $displayName = $this->getDisplayName();
        
        return match ($eventName) {
            'created' => "创建了{$this->getModelLabel()} {$displayName}",
            'updated' => "更新了{$this->getModelLabel()} {$displayName} 的信息",
            'deleted' => "删除了{$this->getModelLabel()} {$displayName}",
            default => "对{$this->getModelLabel()} {$displayName} 执行了 {$eventName} 操作",
        };
    }

    /**
     * 获取模型显示名称
     *
     * @return string
     */
    protected function getDisplayName(): string
    {
        $nameFields = ['display_name', 'name', 'title', 'username', 'id'];
        
        foreach ($nameFields as $field) {
            if (isset($this->$field)) {
                return $this->$field;
            }
        }
        
        return $this->getKey();
    }

    /**
     * 获取模型标签
     *
     * @return string
     */
    protected function getModelLabel(): string
    {
        return property_exists($this, 'logModelLabel') ? $this->logModelLabel : class_basename($this);
    }
} 