<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $mobile
 * @property string $code
 * @property int $status
 * @property string|null $ip
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode whereMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SmsCode whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperSmsCode
 */
class SmsCode extends Model
{
    const VERIFIED_NO = 0;
    const VERIFIED_YES = 1;

    protected $fillable = [
        'mobile',
        'code',
        'status',
        'ip',
    ];
}
