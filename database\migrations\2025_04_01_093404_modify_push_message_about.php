<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('push_templates', function (Blueprint $table) {
            $table->unsignedTinyInteger('show_client_info')->after('content')->nullable()->default(0)->comment('是否显示客户端信息');
        });

        Schema::table('push_messages', function (Blueprint $table) {
            $table->string('banner_url',1000)->after('content')->nullable()->comment('banner图片');
            $table->unsignedTinyInteger('show_client_info')->after('oauth_client_id')->nullable()->default(0)->comment('是否显示客户端信息');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('push_templates', function (Blueprint $table) {
            $table->dropColumn('show_client_info');
        });

        Schema::table('push_messages', function (Blueprint $table) {
            $table->dropColumn('banner_url');
            $table->dropColumn('show_client_info');
        });
    }
};
