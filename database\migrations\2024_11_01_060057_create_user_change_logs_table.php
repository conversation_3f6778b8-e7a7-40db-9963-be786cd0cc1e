<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('user_change_logs', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id');
            $table->string('change_key');
            $table->string('old_value')
                  ->nullable();
            $table->string('new_value');
            $table->string('ip');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('user_change_logs');
    }
};
