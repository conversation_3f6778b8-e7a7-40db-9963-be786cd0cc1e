<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_change_logs', function (Blueprint $table) {
            $table->unsignedTinyInteger('status')->default(0)->comment('审核状态')->after('new_value');
            $table->bigInteger('admin_user_id')->nullable()->comment('审核管理员ID')->after('status');
            $table->timestamp('audit_time')->nullable()->comment('审核时间')->after('admin_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_change_logs', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('admin_user_id');
            $table->dropColumn('audit_time');
        });
    }
};
