<?php

namespace App\Services\Push\Support;

use ArrayAccess;
use App\Services\Push\Exceptions\InvalidConfigurationException;

class Config implements ArrayAccess
{
    /**
     * 配置数组
     */
    protected array $config = [];

    /**
     * 创建新的配置实例
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     * 获取配置项
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get(string $key, mixed $default = null): mixed
    {
        if (isset($this->config[$key])) {
            return $this->config[$key];
        }

        if (str_contains($key, '.')) {
            $segments = explode('.', $key);
            $config = $this->config;

            foreach ($segments as $segment) {
                if (!isset($config[$segment])) {
                    return $default;
                }
                $config = $config[$segment];
            }

            return $config;
        }

        return $default;
    }

    /**
     * 设置配置项
     */
    public function set(string $key, mixed $value): void
    {
        if (str_contains($key, '.')) {
            $segments = explode('.', $key);
            $config = &$this->config;

            foreach ($segments as $i => $segment) {
                if ($i === count($segments) - 1) {
                    $config[$segment] = $value;
                    break;
                }

                if (!isset($config[$segment])) {
                    $config[$segment] = [];
                }

                $config = &$config[$segment];
            }
        } else {
            $this->config[$key] = $value;
        }
    }

    /**
     * 检查配置是否存在
     */
    public function has(string $key): bool
    {
        return null !== $this->get($key);
    }

    public function offsetExists($offset): bool
    {
        return array_key_exists($offset, $this->config);
    }

    public function offsetGet($offset): mixed
    {
        return $this->get($offset);
    }

    public function offsetSet($offset, $value): void
    {
        $this->set($offset, $value);
    }

    public function offsetUnset($offset): void
    {
        unset($this->config[$offset]);
    }

    public function toArray() {
        return $this->config;
    }
}
