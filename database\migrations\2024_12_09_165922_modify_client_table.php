<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->string('icon')
                  ->nullable()
                  ->after('name')
                  ->comment('图标');
            $table->unsignedTinyInteger('is_system')
                  ->default(0)
                  ->after('client_type')
                  ->comment('是否系统级');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->dropColumn('icon');
            $table->dropColumn('is_system');
        });
    }
};
