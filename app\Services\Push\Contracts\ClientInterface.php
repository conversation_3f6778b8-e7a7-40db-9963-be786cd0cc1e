<?php

namespace App\Services\Push\Contracts;

interface ClientInterface
{
    /**
     * 发送消息请求
     *
     * @param array $params 消息参数
     * @return array 响应结果
     */
    public function send(array $params): array;

    /**
     * 查询消息状态
     *
     * @param array $params 查询参数
     * @return array 响应结果
     */
    public function status(array $params): array;

    /**
     * 取消定时消息
     *
     * @param array $params 取消参数
     * @return array 响应结果
     */
    public function cancel(array $params): array;

    /**
     * 上传文件(用于批量推送)
     *
     * @param array $params 上传参数
     * @return array 响应结果
     */
    public function upload(array $params): array;

    /**
     * 获取配置信息
     *
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getConfig(string $key, mixed $default = null): mixed;
}
