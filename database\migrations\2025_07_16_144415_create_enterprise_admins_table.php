<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('enterprises', function (Blueprint $table) {
           $table->json('settings')->nullable()->comment('设置')->after('status');
        });

        Schema::create('enterprise_admins', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('enterprise_id')->comment('所属企业ID');
            $table->uuid('user_id')->comment('所属用户ID');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->string('remark')->nullable()->comment('备注');
            $table->string('created_by')->nullable()->comment('创建人');
            $table->string('updated_by')->nullable()->comment('更新人');
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('enterprise_admin_clients', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('enterprise_admin_id')->comment('所属企业管理员ID');
            $table->uuid('client_id')->comment('所属客户端ID');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->string('remark')->nullable()->comment('备注');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enterprise_admins');
    }
};
