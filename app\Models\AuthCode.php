<?php

namespace App\Models;

use App\Models\Traits\HasUser;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $id
 * @property string $user_uuid
 * @property string|null $user_type 用户类型
 * @property int $client_id
 * @property array<array-key, mixed> $scopes
 * @property array<array-key, mixed>|null $admin_scopes 管理员权限范围
 * @property int $is_revoked
 * @property \Illuminate\Support\Carbon $expires_at
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property-read \App\Models\Client|null $client
 * @property-read mixed $revoked_text
 * @property-read \App\Models\User|null $user
 * @property-read mixed $user_type_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereAdminScopes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereIsRevoked($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereScopes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereUserType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuthCode whereUserUuid($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAuthCode
 */
class AuthCode extends Model
{
    use HasFactory;
    use HasUser;

    protected $table = 'oauth_auth_codes';

    protected $keyType = 'string';

    public $incrementing = false;

    public $timestamps = false;

    protected $guarded = [];

    protected $fillable = [
        'id',
        'user_uuid',
        'user_type',
        'client_id',
        'scopes',
        'admin_scopes',
        'is_revoked',
        'expires_at',
    ];

    protected $casts = [
        'scopes' => 'json',
        'admin_scopes' => 'json',
        'expires_at' => 'datetime',
    ];


    const IS_REVOKED_NO = 0;
    const IS_REVOKED_YES = 1;

    public static array $revokedMap = [
        self::IS_REVOKED_YES => '是',
        self::IS_REVOKED_NO => '否',
    ];

    protected function revokedText(): Attribute {
        return new Attribute(function ($value) {
            return self::$revokedMap[$value] ?? '未知';
        });
    }

    public function client() {
        return $this->belongsTo(Client::class);
    }

//    public function user() {
//        return $this->belongsTo(User::class, 'user_uuid', 'uuid');
//    }

}
