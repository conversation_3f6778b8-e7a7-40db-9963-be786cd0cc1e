<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Models\Casts\Sm2EncryptedCast;
use App\Models\Traits\AdminActivityLogTrait;
use App\Services\CloudFiles\Facades\CloudFiles;
use App\Utils\GmSm;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Laravel\Passport\HasApiTokens;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * @property int $id
 * @property string $uuid
 * @property string $username
 * @property string $password
 * @property int|null $password_secret_version
 * @property string|null $nickname
 * @property int $nickname_confirmed
 * @property int|null $gender
 * @property int $status
 * @property string|null $mobile
 * @property int|null $mobile_confirmed
 * @property string|null $email
 * @property int $email_confirmed
 * @property array<array-key, mixed>|null $avatar
 * @property int $avatar_confirmed
 * @property string|null $birthday
 * @property string|null $wx_unionid
 * @property array<array-key, mixed>|null $wx_openids
 * @property string|null $ali_openid
 * @property string|null $apple_openid
 * @property string|null $dcloud_appid
 * @property string|null $comment
 * @property array<array-key, mixed>|null $third_party
 * @property string|null $register_env_appid
 * @property string|null $register_env_uni_platform
 * @property string|null $register_env_os_name
 * @property string|null $register_env_app_name
 * @property string|null $register_env_app_version
 * @property string|null $register_env_app_version_code
 * @property string|null $register_env_channel
 * @property string|null $register_env_client_ip
 * @property int|null $realname_auth_confirmed
 * @property mixed|null $realname_auth_data
 * @property string|null $register_date
 * @property string|null $register_ip
 * @property string|null $last_login_date
 * @property string|null $last_login_ip
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $identity_hash 用户标识哈希
 * @property int|null $comment_confirmed 评论是否确认
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AccessToken> $accessTokens
 * @property-read int|null $access_tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Activitylog\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read mixed $avatar_confirmed_txt
 * @property-read mixed $base_info
 * @property-read \App\Models\AdminUser|null $bindAdminUser
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\UserChangeLog> $changeLogs
 * @property-read int|null $change_logs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Passport\Client> $clients
 * @property-read int|null $clients_count
 * @property-read mixed $comment_confirmed_txt
 * @property-read mixed $display_avatar
 * @property-read mixed $display_comment
 * @property-read mixed $display_nickname
 * @property-read mixed $email_confirmed_txt
 * @property mixed $encrypt_base_info
 * @property-read mixed $gender_txt
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Group> $groups
 * @property-read int|null $groups_count
 * @property-read mixed $masked_sensitive_info
 * @property-read mixed $mobile_confirmed_txt
 * @property-read mixed $new_avatar
 * @property-read mixed $nickname_confirmed_txt
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OAuthUserAuthorization> $oauthAuthorizations
 * @property-read int|null $oauth_authorizations_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OAuthUserBinding> $oauthBindings
 * @property-read int|null $oauth_bindings_count
 * @property-read mixed $realname_auth_txt
 * @property-read mixed $register_env_os_txt
 * @property-read mixed $register_env_platform_txt
 * @property-read mixed $status_txt
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Tag> $tags
 * @property-read int|null $tags_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Passport\Token> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\UserSubscription> $userSubscriptions
 * @property-read int|null $user_subscriptions_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAliOpenid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAppleOpenid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAvatarConfirmed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereBirthday($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereComment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCommentConfirmed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDcloudAppid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailConfirmed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIdentityHash($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLastLoginDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereLastLoginIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereMobileConfirmed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereNickname($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereNicknameConfirmed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePasswordSecretVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRealnameAuthConfirmed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRealnameAuthData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterEnvAppName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterEnvAppVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterEnvAppVersionCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterEnvAppid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterEnvChannel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterEnvClientIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterEnvOsName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterEnvUniPlatform($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRegisterIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereThirdParty($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereWxOpenids($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereWxUnionid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperUser
 */
//#[ObservedBy(UserObserver::class)]
class User extends Authenticatable implements JWTSubject
{

    use HasFactory, Notifiable, SoftDeletes;
    use HasUuids;
    use HasApiTokens;
    use AdminActivityLogTrait;

    protected $primaryKey = 'uuid';
    // protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'uuid',
        'username',
        'password',
        'password_secret_version',
        'nickname',
        'nickname_confirmed',
        'avatar_confirmed',
        'gender',
        'status',
        'mobile',
        'mobile_confirmed',
        'email',
        'email_confirmed',
        'avatar',
        'wx_unionid',
        'wx_openids',
        'ali_openid',
        'apple_openid',
        'dcloud_appid',
        'comment',
        'comment_confirmed',
        'third_party',
        'register_env_appid',
        'register_env_uni_platform',
        'register_env_os_name',
        'register_env_app_name',
        'register_env_app_version',
        'register_env_app_version_code',
        'register_env_channel',
        'register_env_client_ip',
        'realname_auth_confirmed',
        'realname_auth_data',
        'register_date',
        'register_ip',
        'last_login_date',
        'last_login_ip',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'mobile',
        'realname_auth_data',
        'third_party',
    ];

    // Gender 常量
    const GENDER_UNKNOWN = 0;
    const GENDER_MALE = 1;
    const GENDER_FEMALE = 2;

    public static array $genderMap = [
        self::GENDER_MALE    => '男',
        self::GENDER_FEMALE  => '女',
        self::GENDER_UNKNOWN => '保密',
    ];

    // Status 常量
    const STATUS_INACTIVE = 0;    // 未激活
    const STATUS_ACTIVE = 1;      // 正常
    const STATUS_DISABLED = 2;    // 禁用
    const STATUS_LOCKED = 3;      // 锁定

    public static array $statusMap = [
        self::STATUS_ACTIVE   => '正常',
        self::STATUS_DISABLED => '禁用',
        self::STATUS_LOCKED   => '锁定',
        self::STATUS_INACTIVE => '未激活',
    ];

    // 确认状态常量
    const CONFIRMED_NO = 0;
    const CONFIRMED_YES = 1;

    public static array $confirmedMap = [
        self::CONFIRMED_YES => '已确认',
        self::CONFIRMED_NO  => '未确认',
    ];


    // 微信平台类型
    const WX_PLATFORM_APP = 'app';
    const WX_PLATFORM_MP = 'mp';
    const WX_PLATFORM_H5 = 'h5';
    const WX_PLATFORM_WEB = 'web';

    public static array $wxPlatformMap = [
        self::WX_PLATFORM_APP => 'APP',
        self::WX_PLATFORM_MP  => '小程序',
        self::WX_PLATFORM_H5  => 'H5',
        self::WX_PLATFORM_WEB => '网页',
    ];

    const REGISTER_ENV_PLATFORM_APP = 'app';
    const REGISTER_ENV_PLATFORM_H5 = 'h5';
    const REGISTER_ENV_PLATFORM_WEB = 'web';
    const REGISTER_ENV_PLATFORM_MP = 'mp';
    const REGISTER_ENV_PLATFORM_MINI = 'mini';
    const REGISTER_ENV_PLATFORM_ADMIN = 'admin';

    public static array $registerEnvPlatformMap = [
        self::REGISTER_ENV_PLATFORM_APP   => 'APP',
        self::REGISTER_ENV_PLATFORM_H5    => 'H5',
        self::REGISTER_ENV_PLATFORM_WEB   => 'WEB',
        self::REGISTER_ENV_PLATFORM_MP    => '公众号',
        self::REGISTER_ENV_PLATFORM_MINI  => '小程序',
        self::REGISTER_ENV_PLATFORM_ADMIN => '后台',
    ];

    const REGISTER_ENV_OS_ANDROID = 'android';
    const REGISTER_ENV_OS_IOS = 'ios';
    const REGISTER_ENV_OS_WINDOWS = 'windows';
    const REGISTER_ENV_OS_MACOS = 'macos';
    const REGISTER_ENV_OS_LINUX = 'linux';

    public static array $registerEnvOsMap = [
        self::REGISTER_ENV_OS_ANDROID => 'Android',
        self::REGISTER_ENV_OS_IOS     => 'iOS',
        self::REGISTER_ENV_OS_WINDOWS => 'Windows',
        self::REGISTER_ENV_OS_MACOS   => 'macOS',
        self::REGISTER_ENV_OS_LINUX   => 'Linux',
    ];

    // 实名认证状态
    const REALNAME_AUTH_NO = 0;
    const REALNAME_AUTH_PASSED = 1;
    const REALNAME_AUTH_REJECTED = 2;

    public static array $realnameAuthMap = [
        self::REALNAME_AUTH_NO       => '未认证',
        self::REALNAME_AUTH_PASSED   => '认证通过',
        self::REALNAME_AUTH_REJECTED => '认证失败',
    ];

    const REALNAME_AUTH_NAME = 'name';
    const REALNAME_AUTH_IDENTITY = 'identity';
    const REALNAME_AUTH_DATE = 'date';

    const THIRD_PLATFORM_WECHAT = 'wechat';
    //    const THIRD_PLATFORM_QQ = 'qq';
    //    const THIRD_PLATFORM_WEIBO = 'weibo';
    //    const THIRD_PLATFORM_ALIPAY = 'alipay';
    //    const THIRD_PLATFORM_DOUYIN = 'douyin';

    public static array $thirdPlatformMap = [
        self::THIRD_PLATFORM_WECHAT => '微信',
        //        self::THIRD_PLATFORM_QQ => 'QQ',
        //        self::THIRD_PLATFORM_WEIBO => '微博',
        //        self::THIRD_PLATFORM_ALIPAY => '支付宝',
        //        self::THIRD_PLATFORM_DOUYIN => '抖音',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'avatar'             => 'array',
            'wx_openids'         => 'json',
            'third_party'        => 'json',
            'realname_auth_data' => Sm2EncryptedCast::array(),
        ];
    }

    protected $appends = [
        'display_avatar',
        'display_nickname',
        'display_comment',
        'gender_txt',
        'status_txt',
        'mobile_confirmed_txt',
        'realname_auth_txt',
        'nickname_confirmed_txt',
        'avatar_confirmed_txt',
        'comment_confirmed_txt',
    ];


    /**
     * 生成状态相关的访问器
     */
    protected function genderTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$genderMap[$this->gender] ?? self::$genderMap[self::GENDER_UNKNOWN]);
    }

    protected function statusTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$statusMap[$this->status] ?? self::$statusMap[self::STATUS_INACTIVE]);
    }

    protected function mobileConfirmedTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$confirmedMap[$this->mobile_confirmed] ?? self::$confirmedMap[self::CONFIRMED_NO]);
    }

    protected function emailConfirmedTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$confirmedMap[$this->email_confirmed] ?? self::$confirmedMap[self::CONFIRMED_NO]);
    }

    protected function nicknameConfirmedTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$confirmedMap[$this->nickname_confirmed] ?? self::$confirmedMap[self::CONFIRMED_NO]);
    }

    protected function avatarConfirmedTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$confirmedMap[$this->avatar_confirmed] ?? self::$confirmedMap[self::CONFIRMED_NO]);
    }

    protected function commentConfirmedTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$confirmedMap[$this->comment_confirmed] ?? self::$confirmedMap[self::CONFIRMED_NO]);
    }

    protected function realnameAuthTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$realnameAuthMap[$this->realname_auth_confirmed] ?? self::$realnameAuthMap[self::REALNAME_AUTH_NO]);
    }

    protected function registerEnvPlatformTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$registerEnvPlatformMap[$this->register_env_uni_platform] ?? '未知');
    }

    protected function registerEnvOsTxt(): Attribute
    {
        return Attribute::make(get: fn() => self::$registerEnvOsMap[$this->register_env_os_name] ?? '未知');
    }

    public function isActive(): int
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function isLocked(): int
    {
        return $this->status === self::STATUS_LOCKED;
    }

    public function isDisabled(): int
    {
        return $this->status === self::STATUS_DISABLED;
    }

    public function isRealnameAuthConfirmed(): int
    {
        return $this->realname_auth_confirmed === self::REALNAME_AUTH_PASSED;
    }

    public function isMobileConfirmed(): int
    {
        return $this->mobile_confirmed === self::CONFIRMED_YES;
    }

    public function isEmailConfirmed(): int
    {
        return $this->email_confirmed === self::CONFIRMED_YES;
    }

    protected function maskedSensitiveInfo(): Attribute
    {
        return Attribute::make(get: function () {
            $maskedMobile = $this->mobile ? str_repeat('*', strlen($this->mobile) - 3) . substr($this->mobile, -3) : '';

            $maskedIdentity = '';
            $maskedRealname = '';
            if (!empty($this->realname_auth_data[self::REALNAME_AUTH_IDENTITY])) {
                $identity = $this->realname_auth_data[self::REALNAME_AUTH_IDENTITY];
                $maskedIdentity = substr($identity, 0, 3) . str_repeat('*', strlen($identity) - 6) . substr($identity, -3);
            }

            if (!empty($this->realname_auth_data[self::REALNAME_AUTH_NAME])) {
                $name = $this->realname_auth_data[self::REALNAME_AUTH_NAME];

                $maskedRealname = str_repeat('*', mb_strlen($name, 'UTF-8') - 1) . mb_substr($name, -1, 1, 'UTF-8');
            }

            return [
                'mobile'   => $maskedMobile,
                'identity' => $maskedIdentity,
                'realname' => $maskedRealname,
            ];
        });
    }

    /**
     * 获取用于显示的昵称 (始终返回当前 users 表的值)
     */
    protected function displayNickname(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $attributes['nickname'] ?? '常友_' . substr($attributes['uuid'], -4)
        );
    }

    /**
     * 获取用于显示的评论/签名 (始终返回当前 users 表的值)
     */
    protected function displayComment(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $attributes['comment'] ?? ''
        );
    }

    /**
     * 获取用于显示的头像 (始终返回当前 users 表的值)
     */
    protected function displayAvatar(): Attribute
    {
        return Attribute::make(
            get: function ($value, $attributes) {
                return CloudFiles::getFileUrl($this->avatar['provider'] ?? 'url', $this->avatar['path'] ?? config('uc.default_avatar'), config('uc.avatar_expired_ttl'));
            }
        );
    }

    protected function newAvatar(): Attribute
    {
        return Attribute::make(get: function () {
            // 查询changeLog表中有待审核的avatar记录
            $pendingChange = UserChangeLog::where('user_id', $this->id)
                ->where('change_key', 'avatar')
                ->where('status', UserChangeLog::STATUS_PENDING)
                ->first();

            if ($pendingChange) {
                return CloudFiles::getFileUrl($pendingChange->new_value['provider'] ?? 'url', $pendingChange->new_value['path'] ?? config('uc.default_avatar'), config('uc.avatar_expired_ttl'));
            }

            return CloudFiles::getFileUrl($this->avatar['provider'] ?? 'url', $this->avatar['path'] ?? config('uc.default_avatar'), config('uc.avatar_expired_ttl'));
        });
    }

    protected function baseInfo(): Attribute
    {
        return Attribute::make(get: function () {
            return [
                'uuid'               => $this->uuid,
                'username'           => $this->username,
                'nickname'           => $this->nickname,
                'avatar'             => $this->display_avatar,
                'mobile'             => $this->mobile,
                'mobile_confirmed'   => $this->isMobileConfirmed(),
                'realname_confirmed' => $this->isRealnameAuthConfirmed(),
                'status'             => $this->status,
                'is_active'          => $this->isActive(),
            ];
        });
    }

    protected function encryptBaseInfo(): Attribute
    {
        return Attribute::make(get: function ($value) {
            if ($value) {
                return $value;
            }

            $encryptData = json_encode($this->base_info);

            $cacheKey = config('sm2.cache.prefix') . ":" . md5($encryptData);

            if ($cached = Cache::get($cacheKey)) {
                return $cached;
            }

            GmSm::configure(publicKey: config('sm2.sens_pub_key'));

            $encrypted = GmSm::sm2Encrypt($encryptData);
            $result = 'sm2:' . base64_encode($encrypted);

            Cache::put($cacheKey, $result, now()->addMinutes(Config::get('sm2.cache.ttl')));

            return $result;
        }, set: function ($value) {
            return $value;
        });
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array<string, mixed>
     */
    public function getJWTCustomClaims(): array
    {
        return [
            "baseinfo" => $this->encrypt_base_info,
        ];
    }


    public function groups()
    {
        return $this->belongsToMany(Group::class, 'user_groups', 'user_id', 'group_id', 'id', 'id');
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'user_tags', 'user_id', 'tag_id', 'id', 'id');
    }

    public function oauthBindings()
    {
        return $this->hasMany(OAuthUserBinding::class, 'user_uuid', 'uuid');
    }

    public function oauthAuthorizations()
    {
        return $this->hasMany(OAuthUserAuthorization::class, 'user_uuid', 'uuid');
    }

    public function userSubscriptions()
    {
        return $this->hasMany(UserSubscription::class, 'user_uuid', 'uuid');
    }

    public function accessTokens()
    {
        return $this->hasMany(AccessToken::class, 'user_uuid', 'uuid');
    }

    public function getActiveAuthorizations()
    {
        return $this->oauthAuthorizations()
            ->with('client')
            ->active()
            ->get();
    }

    public function bindAdminUser()
    {
        return $this->hasOne(AdminUser::class, 'bind_user_uuid', 'uuid');
    }

    /**
     * 关联企业管理员
     */
    public function enterpriseAdmins()
    {
        return $this->hasMany(EnterpriseAdmin::class, 'user_id', 'uuid');
    }

    /**
     * 关联启用的企业管理员
     */
    public function activeEnterpriseAdmins()
    {
        return $this->enterpriseAdmins()->enabled();
    }

    /**
     * 检查用户是否为企业管理员
     */
    public function isEnterpriseAdmin(): bool
    {
        return $this->enterpriseAdmins()->exists();
    }

    /**
     * 检查用户是否为指定企业的管理员
     */
    public function isAdminOfEnterprise($enterpriseId): bool
    {
        return $this->enterpriseAdmins()
            ->where('enterprise_id', $enterpriseId)
            ->enabled()
            ->exists();
    }

    // 新增关联关系
    public function changeLogs()
    {
        return $this->hasMany(UserChangeLog::class, 'user_id', 'id');
    }

    // 获取指定字段最新的待审核记录
    public function latestPendingLog($changeKey)
    {
        return $this->hasOne(UserChangeLog::class, 'user_id', 'id')
            ->ofMany(['id' => 'max'], function ($query) use ($changeKey) {
                $query->where('status', UserChangeLog::STATUS_PENDING)
                    ->where('change_key', $changeKey);
            }); // 只获取最新的一条
    }

    // 为每个需要审核的字段定义单独的关联，方便预加载
    public function latestPendingNicknameLog()
    {
        return $this->latestPendingLog('nickname');
    }

    public function latestPendingAvatarLog()
    {
        return $this->latestPendingLog('avatar');
    }

    public function latestPendingCommentLog()
    {
        return $this->latestPendingLog('comment');
    }

    public function latestPendingUsernameLog()
    {
        return $this->latestPendingLog('username');
    }
    // 结束新增关联关系
    // 日志相关配置
    protected $logName = 'user';
    protected $logModelLabel = '用户';
    protected $logOperationType = 'user_management';
    protected $logAttributes = [
        'username',
        'password',
        'nickname',
        'nickname_confirmed',
        'avatar_confirmed',
        'gender',
        'status',
        'mobile',
        'mobile_confirmed',
        'display_avatar',
        'comment',
        'comment_confirmed',
        'status_txt',
        'mobile_confirmed_txt',
        'email_confirmed_txt',
        'nickname_confirmed_txt',
        'avatar_confirmed_txt',
        'comment_confirmed_txt',
        'realname_auth_txt',
    ];

    protected $logSensitiveFields = [
        'password',
        'mobile',
        // 'email',
    ];

}
