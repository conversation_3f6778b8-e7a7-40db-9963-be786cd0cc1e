<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\EnterprisePermissionSeeder;

class InitEnterprisePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'enterprise:init-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化企业权限和角色';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始初始化企业权限和角色...');
        
        $seeder = new EnterprisePermissionSeeder();
        $seeder->setCommand($this);
        $seeder->run();
        
        $this->info('企业权限和角色初始化完成！');
        
        return Command::SUCCESS;
    }
}
