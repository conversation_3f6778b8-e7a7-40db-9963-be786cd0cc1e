<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Defaults
    |--------------------------------------------------------------------------
    |
    | This option defines the default authentication "guard" and password
    | reset "broker" for your application. You may change these values
    | as required, but they're a perfect start for most applications.
    |
    */

    'defaults' => [
        'guard' => env('AUTH_GUARD', 'api'),
        'passwords' => env('AUTH_PASSWORD_BROKER', 'users'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication Guards
    |--------------------------------------------------------------------------
    |
    | Next, you may define every authentication guard for your application.
    | Of course, a great default configuration has been defined for you
    | which utilizes session storage plus the Eloquent user provider.
    |
    | All authentication guards have a user provider, which defines how the
    | users are actually retrieved out of your database or other storage
    | system used by the application. Typically, Eloquent is utilized.
    |
    | Supported: "session"
    |
    */

    'guards' => [
        'web' => [
            'driver' => 'session',
            'provider' => 'users',
        ],
        'api' => [
            'driver' => 'uc',
            'provider' => 'users',
        ],
        'admin' => [
            'driver' => 'ucadmin',
            'provider' => 'admins',
        ],
        'enterprise' => [
            'driver' => 'enterprise',
            'provider' => 'enterprises',
        ],
        'enterprise_admin' => [
            'driver' => 'enterprise_user',
            'provider' => 'enterprise_admins',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Providers
    |--------------------------------------------------------------------------
    |
    | All authentication guards have a user provider, which defines how the
    | users are actually retrieved out of your database or other storage
    | system used by the application. Typically, Eloquent is utilized.
    |
    | If you have multiple user tables or models you may configure multiple
    | providers to represent the model / table. These providers may then
    | be assigned to any extra authentication guards you have defined.
    |
    | Supported: "database", "eloquent"
    |
    */

    'providers' => [
        'users' => [
            'driver' => 'eloquent',
            'model' => env('AUTH_MODEL', App\Models\User::class),
        ],

        'admins' => [
            'driver' => 'eloquent',
            'model' => App\Models\AdminUser::class,
        ],

        'enterprises' => [
            'driver' => 'eloquent',
            'model' => App\Models\AdminUser::class,
        ],

        'enterprise_admins' => [
            'driver' => 'eloquent',
            'model' => App\Models\EnterpriseAdmin::class,
        ],

        // 'users' => [
        //     'driver' => 'database',
        //     'table' => 'users',
        // ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Resetting Passwords
    |--------------------------------------------------------------------------
    |
    | These configuration options specify the behavior of Laravel's password
    | reset functionality, including the table utilized for token storage
    | and the user provider that is invoked to actually retrieve users.
    |
    | The expiry time is the number of minutes that each reset token will be
    | considered valid. This security feature keeps tokens short-lived so
    | they have less time to be guessed. You may change this as needed.
    |
    | The throttle setting is the number of seconds a user must wait before
    | generating more password reset tokens. This prevents the user from
    | quickly generating a very large amount of password reset tokens.
    |
    */

    'passwords' => [
        'users' => [
            'provider' => 'users',
            'table' => env('AUTH_PASSWORD_RESET_TOKEN_TABLE', 'password_reset_tokens'),
            'expire' => 60,
            'throttle' => 60,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Confirmation Timeout
    |--------------------------------------------------------------------------
    |
    | Here you may define the amount of seconds before a password confirmation
    | window expires and users are asked to re-enter their password via the
    | confirmation screen. By default, the timeout lasts for three hours.
    |
    */

    'password_timeout' => env('AUTH_PASSWORD_TIMEOUT', 10800),

    /*
    |--------------------------------------------------------------------------
    | JWT Drivers Configuration
    |--------------------------------------------------------------------------
    |
    | Here you can configure JWT drivers that will be automatically registered.
    | Each driver configuration includes the algorithm, keys, TTL and other
    | JWT-specific settings. The system will automatically create the
    | corresponding auth guards based on these configurations.
    |
    */

    'drivers' => [
        'uc' => [
            'provider_class' => \App\Services\JwtWithSm2\SMProvider::class,
            'algorithm' => env('AUTH_UC_ALGO', \App\Services\JwtWithSm2\SMProvider::ALGO_SM2),
            'secret' => env('AUTH_UC_SECRET',env('JWT_SECRET')),
            'keys' => [
                'public' => env('AUTH_UC_PUBLIC_KEY'),
                'private' => env('AUTH_UC_PRIVATE_KEY'),
                'with_key' => env('AUTH_UC_WITH_KEY', true),
            ],
            'ttl' => (int)env('AUTH_UC_TTL', env('AUTH_TTL', 60)),
            'refresh_ttl' => (int)env('AUTH_UC_REFRESH_TTL', env('AUTH_REFRESH_TTL', 20160)),
            'blacklist_enabled' => env('AUTH_UC_BLACKLIST_ENABLED', true),
            'lock_subject' => true,
            'persistent_claims' => [],
        ],
        'ucadmin' => [
            'provider_class' => \App\Services\JwtWithSm2\SMProvider::class,
            'algorithm' => env('AUTH_UC_ADMIN_ALGO', \App\Services\JwtWithSm2\SMProvider::ALGO_SM2),
            'secret' => env('AUTH_UC_ADMIN_SECRET',env('JWT_SECRET')),
            'keys' => [
                'public' => env('AUTH_UC_ADMIN_PUBLIC_KEY'),
                'private' => env('AUTH_UC_ADMIN_PRIVATE_KEY'),
                'with_key' => env('AUTH_UC_ADMIN_WITH_KEY', true),
            ],
            'ttl' => (int)env('AUTH_UC_ADMIN_TTL', env('AUTH_TTL', 60)),
            'refresh_ttl' => (int)env('AUTH_UC_ADMIN_REFRESH_TTL', env('AUTH_REFRESH_TTL', 20160)),
            'blacklist_enabled' => env('AUTH_UC_ADMIN_BLACKLIST_ENABLED', true),
            'lock_subject' => true,
            'persistent_claims' => [],
        ],
        'enterprise' => [
            'provider_class' => \App\Services\JwtWithSm2\SMProvider::class,
            'algorithm' => env('AUTH_ENTERPRISE_ALGO', \App\Services\JwtWithSm2\SMProvider::ALGO_SM2),
            'secret' => env('AUTH_ENTERPRISE_SECRET',env('JWT_SECRET')),
            'keys' => [
                'public' => env('AUTH_ENTERPRISE_PUBLIC_KEY'),
                'private' => env('AUTH_ENTERPRISE_PRIVATE_KEY'),
                'with_key' => env('AUTH_ENTERPRISE_WITH_KEY', true),
            ],
            'ttl' => (int)env('AUTH_ENTERPRISE_TTL', env('AUTH_TTL', 60)),
            'refresh_ttl' => (int)env('AUTH_ENTERPRISE_REFRESH_TTL', env('AUTH_REFRESH_TTL', 20160)),
            'blacklist_enabled' => env('AUTH_ENTERPRISE_BLACKLIST_ENABLED', true),
            'lock_subject' => true,
            'persistent_claims' => [],
        ],
        'enterprise_admin' => [
            'provider_class' => \App\Services\JwtWithSm2\SMProvider::class,
            'algorithm' => env('AUTH_ENTERPRISE_ADMIN_ALGO', \App\Services\JwtWithSm2\SMProvider::ALGO_SM2),
            'secret' => env('AUTH_ENTERPRISE_ADMIN_SECRET',env('JWT_SECRET')),
            'keys' => [
                'public' => env('AUTH_ENTERPRISE_ADMIN_PUBLIC_KEY'),
                'private' => env('AUTH_ENTERPRISE_ADMIN_PRIVATE_KEY'),
                'with_key' => env('AUTH_ENTERPRISE_ADMIN_WITH_KEY', true),
            ],
            'ttl' => (int)env('AUTH_ENTERPRISE_ADMIN_TTL', env('AUTH_TTL', 60)),
            'refresh_ttl' => (int)env('AUTH_ENTERPRISE_ADMIN_REFRESH_TTL', env('AUTH_REFRESH_TTL', 20160)),
            'blacklist_enabled' => env('AUTH_ENTERPRISE_ADMIN_BLACKLIST_ENABLED', true),
            'lock_subject' => true,
            'persistent_claims' => [],
        ],
    ],

];
