<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\ModelTree;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property string $id
 * @property string $name 路由名称
 * @property string $path 路由路径
 * @property string|null $icon 路由图标
 * @property int|null $parent_id 父级路由ID, 0表示顶级路由
 * @property int|null $sort 排序
 * @property int|null $hidden 是否隐藏
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, AdminRoute> $children
 * @property-read int|null $children_count
 * @property-read AdminRoute|null $parent
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Role> $roles
 * @property-read int|null $roles_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute whereHidden($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminRoute withoutRole($roles, $guard = null)
 * @mixin \Eloquent
 * @mixin IdeHelperAdminRoute
 */
class AdminRoute extends Model
{
    use HasFactory;
    use HasRoles;
    use ModelTree{
        ModelTree::boot as treeBoot;
    }
    
    protected $guard_name = 'admin';
    protected $keyType = 'string';
    public $incrementing = true;

    protected $fillable = ['name', 'path', 'icon', 'parent_id', 'sort', 'hidden'];

    public function children()
    {
        return $this->hasMany(AdminRoute::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(AdminRoute::class, 'parent_id');
    }
}
