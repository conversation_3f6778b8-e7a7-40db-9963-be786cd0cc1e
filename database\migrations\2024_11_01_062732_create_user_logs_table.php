<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('user_logs', function (Blueprint $table) {
            $table->id();
            $table->string('device_id')
                  ->nullable();
            $table->ipAddress('ip')
                  ->nullable();
            $table->unsignedTinyInteger('state')
                  ->default(1);
            $table->string('type')
                  ->nullable();
            $table->string('ua')
                  ->nullable();
            $table->string('user_id')
                  ->nullable();
            $table->string('username')
                  ->nullable();
            $table->string('mobile')
                  ->nullable();
            $table->string('openid')
                  ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('user_logs');
    }
};
