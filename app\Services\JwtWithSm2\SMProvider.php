<?php

namespace App\Services\JwtWithSm2;

use App\Services\JwtWithSm2\Signers\SM2Signer;
use Illuminate\Support\Collection;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Configuration;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenInvalidException;
use <PERSON><PERSON>\JWTAuth\Providers\JWT\Lcobucci;

class SMProvider extends Lcobucci
{
    /**
     * SM algorithm constants
     */
    const ALGO_SM2 = 'SM2';
    const ALGO_SM3 = 'SM3';

    protected $algo;
    protected $privateKey;
    protected $publicKey;
    protected $keys;
    protected $withKey;

    public function __construct($secret, $algo, array $keys, $config = null, $withKey = false) {
        $this->keys = $keys;
        $this->algo = $algo;
        $this->withKey = $withKey;
        parent::__construct($secret, $algo, $keys, $config);
    }

    // 创建JSON Web Token
    public function encode(array $payload) {
        try {
            $builder = $this->getBuilderFromClaims($payload);

            // 获取私钥文件路径
//            $privateKeyPath = $this->keys['private'];
            // 获取用户ID
            $userId = $payload['sub'];

            // 将签名后的token返回
            return $builder->getToken(new SM2Signer($userId, $this->withKey), $this->config->signingKey())
                           ->toString();

        } catch (\Exception $e) {
            throw new JWTException('Could not create token: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    // 解码JSON Web Token
    public function decode($token) {
        try {
            $token = $this->config->parser()
                                  ->parse($token);
        } catch (\Exception $e) {
            throw new TokenInvalidException('Could not decode token: ' . $e->getMessage(), $e->getCode(), $e);
        }

        if (!$this->config->validator()
                          ->validate($token, ...$this->config->validationConstraints())) {
            throw new TokenInvalidException('Token Signature could not be verified.');
        }

        return Collection::wrap($token->claims()
                                      ->all())
                         ->map(function ($claim) {
                             if ($claim instanceof \DateTimeInterface) {
                                 return $claim->getTimestamp();
                             }

                             return is_object($claim) && method_exists($claim, 'getValue') ? $claim->getValue() : $claim;
                         })
                         ->toArray();
    }


    // 解析token载荷
    protected function parseTokenPayload($token) {
        // 拆分token
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return [];
        }

        // 解析token并返回载荷数组
        return json_decode(base64_decode($parts[1]), true);
    }

    /**
     * 判断是否为非对称算法
     *
     * @return bool
     */
    protected function isAsymmetric() {
        return true;
    }

    public function getWithKey() {
        return $this->withKey;
    }

    public function getSigner() {
        return new SM2Signer(withKey: $this->getWithKey());
    }

    /**
     * @throws JWTException
     */
    protected function buildConfig(): Configuration {
        $config = $this->isAsymmetric() ? Configuration::forAsymmetricSigner($this->signer, $this->getSigningKey(), $this->getVerificationKey()) : Configuration::forSymmetricSigner($this->signer, $this->getSigningKey());

        $config->setValidationConstraints(new SMSignerWith($this->signer, $this->getVerificationKey()));

        return $config;
    }

//    protected function loadKeys($keys): void
//    {
//        $publicKeyPath = $keys['public'];
//        $privateKeyPath = $keys['private'];
//
//        if (empty($publicKeyPath) || empty($privateKeyPath)) {
//            throw new \RuntimeException('SM2 key paths not configured');
//        }
//
//        try {
//            $decodedPrivKey = MyAsn1::decode_file($privateKeyPath);
//            $this->privateKey = $decodedPrivKey[1];
//
//            $decodedPubKey = MyAsn1::decode_file($publicKeyPath);
//            $this->publicKey = $decodedPubKey[1];
//        } catch (\Exception $e) {
//            throw new \RuntimeException('Failed to load SM2 keys: ' . $e->getMessage());
//        }
//    }
}
