<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('push_messages',function(Blueprint $table){
            $table->boolean('is_silent')->default(false)->after('status')->comment('是否静默推送 0否 1是');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('push_messages', function (Blueprint $table) {
            $table->dropColumn('is_silent');
        });
    }
};
