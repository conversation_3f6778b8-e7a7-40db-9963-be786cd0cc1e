<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $user_uuid
 * @property string $device_id
 * @property string $device_token
 * @property int $platform_type 1:android,2:ios,7:鸿蒙
 * @property string|null $device_type
 * @property string|null $app_id
 * @property string|null $client_type
 * @property string|null $client_version
 * @property string|null $device_os
 * @property string|null $device_os_version
 * @property int $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereAppId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereClientType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereClientVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereDeviceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereDeviceOs($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereDeviceOsVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereDeviceToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereDeviceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice wherePlatformType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDevice whereUserUuid($value)
 * @mixin \Eloquent
 * @mixin IdeHelperUserDevice
 */
class UserDevice extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_uuid',
        'device_id',
        'device_token',
        'platform_type',
        'device_type',
        'app_id',
        'client_type',
        'client_version',
        'device_os',
        'device_os_version',
        'is_active',
    ];

    const IS_ACTIVE_NO = 0;
    const IS_ACTIVE_YES = 1;

    public static array $isActiveMap = [
        self::IS_ACTIVE_YES => '是',
        self::IS_ACTIVE_NO => '否',
    ];
}
