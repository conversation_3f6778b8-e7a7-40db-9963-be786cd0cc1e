<?php

namespace App\Models;

use App\Models\Traits\HasUser;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $id
 * @property string $user_uuid
 * @property string|null $user_type 用户类型
 * @property int $client_id
 * @property array<array-key, mixed> $scopes
 * @property array<array-key, mixed>|null $admin_scopes 管理员权限范围
 * @property int $is_revoked
 * @property \Illuminate\Support\Carbon $expires_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Client|null $client
 * @property-read \App\Models\RefreshToken|null $refreshToken
 * @property-read mixed $revoked_text
 * @property-read \App\Models\User|null $user
 * @property-read mixed $user_type_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereAdminScopes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereIsRevoked($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereScopes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereUserType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccessToken whereUserUuid($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAccessToken
 */
class AccessToken extends Model
{
    use HasFactory;
    use HasUser;

    protected $table = 'oauth_access_tokens';

    protected $keyType = 'string';

    public $incrementing = false;

    protected $fillable = [
        'id',
        'user_uuid',
        'user_type',
        'client_id',
        'scopes',
        'admin_scopes',
        'is_revoked',
        'expires_at',
    ];

    protected $casts = [
        'scopes'       => 'json',
        'admin_scopes' => 'json',
        'expires_at'   => 'datetime',
    ];

    const IS_REVOKED_NO = 0;
    const IS_REVOKED_YES = 1;

    public static array $revokedMap = [
        self::IS_REVOKED_YES => '是',
        self::IS_REVOKED_NO  => '否',
    ];

    protected function revokedText(): Attribute {
        return new Attribute(function ($value) {
            return self::$revokedMap[$value] ?? '未知';
        });
    }

    public function client() {
        return $this->belongsTo(Client::class);
    }

    public function refreshToken() {
        return $this->hasOne(RefreshToken::class, 'access_token_id', 'id');
    }

    //    public function user() {
    //        return $this->belongsTo(User::class, 'user_uuid', 'uuid');
    //    }

    public function can($scope) {
        if (in_array('*', $this->scopes)) {
            return true;
        }

        $scopes = [$scope];

        foreach ($scopes as $scope) {
            if (in_array($scope, $this->scopes)) {
                return true;
            }
        }

        return false;
    }

    public function cant($scope) {
        return !$this->can($scope);
    }


    public function revoke() {
        return $this->forceFill(['is_revoked' => self::IS_REVOKED_YES])
                    ->save();
    }

    public function transient() {
        return false;
    }

}
