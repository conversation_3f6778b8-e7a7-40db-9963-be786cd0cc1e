<?php

namespace App\Services;

use App\Enums\ErrorCodeEnum;
use App\Enums\PushMessageCategoryEnum;
use App\Exceptions\PushException;
use App\Jobs\ProcessPushMessageJob;
use App\Jobs\ProcessSubscriptionPushJob;
use App\Models\AdminUser;
use App\Models\Client;
use App\Models\OauthClientSubscription;
use App\Models\OauthClientTemplate;
use App\Models\PushMessage;
use App\Models\PushTemplate;
use App\Models\UserMessage;
use App\Utils\OAuthCacher;
use App\Utils\RedirectToBuilder;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class PushMessageService
{
    /**
     * 推送批次大小
     */
    const BATCH_SIZE = 20;

    /**
     * 系统内部推送给多个用户
     * 
     * @param string $templateCode 模板代码
     * @param array $toUserIds 目标用户ID数组（UUID或open_id）
     * @param array|null $templateParams 模板参数
     * @param array|null $extendParams 扩展参数
     * @param string|null $redirectTo 重定向链接
     * @return PushMessage 创建的推送消息
     * @throws PushException|ValidationException
     */
    public function pushToMultipleUsers(
        string $templateCode,
        array $toUserIds,
        ?array $templateParams = null,
        ?array $extendParams = null,
        ?string $redirectTo = null
    ): PushMessage {
        // 系统内部调用，使用默认client_id
        return $this->pushToMultipleUsersWithClient(
            $templateCode,
            $toUserIds,
            null,
            $templateParams,
            $extendParams,
            $redirectTo
        );
    }

    /**
     * 为特定应用推送给多个用户
     * 
     * @param string $templateCode 模板代码
     * @param array $toUserIds 目标用户ID数组（UUID或open_id）
     * @param string|null $clientId 应用ID
     * @param array|null $templateParams 模板参数
     * @param array|null $extendParams 扩展参数
     * @param string|null $redirectTo 重定向链接
     * @return PushMessage 创建的推送消息
     * @throws PushException|ValidationException
     */
    public function pushToMultipleUsersWithClient(
        string $templateCode,
        array $toUserIds,
        ?string $clientId = null,
        ?array $templateParams = null,
        ?array $extendParams = null,
        ?string $redirectTo = null
    ): PushMessage {
        // 如果没有指定clientId，使用系统默认client
        $currentClientId = $clientId ?? config('uc.system_client_id');
        
        // 检查是否为系统默认client_id
        $isSystemDefaultClient = (string)$currentClientId === (string)config('uc.system_client_id');

        // 初始化变量
        $userUuids = collect();
        $client = null;
        
        // 如果不是系统默认client_id，需要验证client
        if (!$isSystemDefaultClient) {
            // 获取应用信息
            $client = OAuthCacher::getClientByKey($currentClientId);
            if (!$client) {
                throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
            }

            // 根据客户端类型处理用户ID
            if ($client->is_system === Client::IS_SYSTEM_YES) {
                // 系统客户端：直接使用UUID
                foreach ($toUserIds as $userId) {
                    if (!Str::isUuid($userId)) {
                        throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
                    }
                    $userUuids->push($userId);
                }
            } else if ($client->is_workspace_client === Client::IS_WORKSPACE_CLIENT_YES) {
                // 工作区客户端：使用adminUser绑定的user_uuid
                foreach ($toUserIds as $userId) {
                    if (Str::isUuid($userId)) {
                        $userUuid = AdminUser::where('uuid', $userId)
                            ->where('status', AdminUser::STATUS_ENABLED)
                            ->value('bind_user_uuid');
                        
                        if ($userUuid) {
                            $userUuids->push($userUuid);
                        }
                    } else {
                        throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
                    }
                }
            } else {
                // 常规客户端
                foreach ($toUserIds as $userId) {
                    if (Str::isUuid($userId)) {
                        // 验证该UUID是否已授权此客户端
                        $isAuthorized = OAuthCacher::isUserAuthorized($userId, $client->id);
                        
                        if ($isAuthorized) {
                            $userUuids->push($userId);
                        }
                    } else {
                        // 传入的是open_id：通过open_id获取UUID
                        $userUuid = OAuthCacher::getUserUuidByOpenId($userId, $client->id);
                        
                        if ($userUuid) {
                            $userUuids->push($userUuid);
                        }
                    }
                }
            }
        } else {
            // 系统默认客户端：直接使用UUID
            foreach ($toUserIds as $userId) {
                if (!Str::isUuid($userId)) {
                    throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
                }
                $userUuids->push($userId);
            }
        }

        // 如果没有有效的UUID，抛出异常
        if ($userUuids->isEmpty()) {
            throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        // 变量初始化
        $template = null;
        $filteredTemplateParams = [];
        $filteredExtendParams = [];

        // 如果是系统默认client_id，可以跳过模板验证
        if ($isSystemDefaultClient) {
            // 尝试获取模板信息，但不强制验证
            try {
                $template = $this->getAndValidateTemplate($templateCode, PushMessage::PUSH_TYPE_PERSONAL, $currentClientId);
                
                // 处理模板参数
                $filteredTemplateParams = $this->filterTemplateParams(
                    $template->allowed_params ?? [],
                    $templateParams ?? []
                );
                
                // 处理扩展参数
                $filteredExtendParams = $this->filterExtendParams(
                    $template->allowed_extend_params ?? [],
                    $extendParams ?? []
                );
            } catch (\Exception $e) {
                // 对于系统默认client，如果模板验证失败，直接使用原始参数
                $template = PushTemplate::where('template_code', $templateCode)->first();
                $filteredTemplateParams = $templateParams ?? [];
                $filteredExtendParams = $extendParams ?? [];
            }
        } else {
            // 正常验证模板
            $template = $this->getAndValidateTemplate($templateCode, PushMessage::PUSH_TYPE_PERSONAL, $currentClientId);
            
            // 处理模板参数
            $filteredTemplateParams = $this->filterTemplateParams(
                $template->allowed_params ?? [],
                $templateParams ?? []
            );
            
            // 处理扩展参数
            $filteredExtendParams = $this->filterExtendParams(
                $template->allowed_extend_params ?? [],
                $extendParams ?? []
            );
        }

        // 如果模板仍然不存在，抛出异常
        if (!$template) {
            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }

        // 处理重定向链接
        if ($redirectTo) {
            if (RedirectToBuilder::isValidRedirectTo($redirectTo)) {
                $filteredExtendParams['redirect_to'] = $redirectTo;
            }
        }

        // 准备参数
        $params = [
            'template_params' => $filteredTemplateParams,
            'extend_params' => $filteredExtendParams,
        ];

        // 创建消息记录 - 设置为群发类型
        $message = $this->createPushMessage(
            template: $template,
            pushType: PushMessage::PUSH_TYPE_GROUP,
            params: $params,
            oauthClientId: $currentClientId
        );

        // 创建用户消息记录
        $this->createUserMessages($message->id, $userUuids);

        // 使用普通推送Job
        ProcessPushMessageJob::dispatch($message);

        return $message;
    }

    /**
     * 系统内部推送给指定用户
     * 
     * @param string $templateCode 模板代码
     * @param string $toUserId 目标用户ID（UUID或open_id）
     * @param array|null $templateParams 模板参数
     * @param array|null $extendParams 扩展参数
     * @param string|null $redirectTo 重定向链接
     * @return PushMessage 创建的推送消息
     * @throws PushException|ValidationException
     */
    public function pushToUser(
        string $templateCode,
        string $toUserId,
        ?array $templateParams = null,
        ?array $extendParams = null,
        ?string $redirectTo = null
    ): PushMessage {
        // 系统内部调用，使用默认client_id
        return $this->pushToUserWithClient(
            $templateCode,
            $toUserId,
            null,
            $templateParams,
            $extendParams,
            $redirectTo
        );
    }

    /**
     * 为特定应用推送给指定用户
     * 
     * @param string $templateCode 模板代码
     * @param string $toUserId 目标用户ID（UUID或open_id）
     * @param string $clientId 应用ID
     * @param array|null $templateParams 模板参数
     * @param array|null $extendParams 扩展参数
     * @param string|null $redirectTo 重定向链接
     * @return PushMessage 创建的推送消息
     * @throws PushException|ValidationException
     */
    public function pushToUserWithClient(
        string $templateCode,
        string $toUserId,
        ?string $clientId = null,
        ?array $templateParams = null,
        ?array $extendParams = null,
        ?string $redirectTo = null
    ): PushMessage {
        // 如果没有指定clientId，使用系统默认client
        $currentClientId = $clientId ?? config('uc.system_client_id');
        
        // 检查是否为系统默认client_id
        $isSystemDefaultClient = (string)$currentClientId === (string)config('uc.system_client_id');

        // 初始化变量
        $userUuid = null;
        $client = null;
        
        // 如果不是系统默认client_id，需要验证client
        if (!$isSystemDefaultClient) {
            // 获取应用信息
            $client = OAuthCacher::getClientByKey($currentClientId);
            if (!$client) {
                throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
            }

            // 根据客户端类型处理用户ID
            if ($client->is_system === Client::IS_SYSTEM_YES) {
                // 系统客户端：直接使用UUID
                $userUuid = $toUserId;
                if (!Str::isUuid($userUuid)) {
                    throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
                }
            } else if ($client->is_workspace_client === Client::IS_WORKSPACE_CLIENT_YES) {
                // 工作区客户端：使用adminUser绑定的user_uuid
                if (Str::isUuid($toUserId)) {
                    $userUuid = AdminUser::where('uuid', $toUserId)
                        ->where('status', AdminUser::STATUS_ENABLED)
                        ->value('bind_user_uuid');
                } else {
                    throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
                }
            } else {
                // 常规客户端
                if (Str::isUuid($toUserId)) {
                    // 授权验证通过，使用UUID
                    $userUuid = $toUserId;
                } else {
                    // 传入的是open_id：通过open_id获取UUID
                    $userUuid = OAuthCacher::getUserUuidByOpenId($toUserId, $client->id);
                    
                    if (!$userUuid) {
                        throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
                    }
                }
                
                // 传入的是UUID：验证该UUID是否已授权此客户端
                $isAuthorized = OAuthCacher::isUserAuthorized($toUserId, $client->id);
                
                if (!$isAuthorized) {
                    throw new PushException(ErrorCodeEnum::OAUTH_USER_UNAUTHORIZED);
                }
            }
        } else {
            // 系统默认客户端：直接使用UUID
            $userUuid = $toUserId;
            if (!Str::isUuid($userUuid)) {
                throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
            }
        }

        // 如果未能获取有效的UUID，抛出异常
        if (!$userUuid) {
            throw new PushException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        // 变量初始化
        $template = null;
        $filteredTemplateParams = [];
        $filteredExtendParams = [];

        // 如果是系统默认client_id，可以跳过模板验证
        if ($isSystemDefaultClient) {
            // 尝试获取模板信息，但不强制验证
            try {
                $template = $this->getAndValidateTemplate($templateCode, PushMessage::PUSH_TYPE_PERSONAL, $currentClientId);
                
                // 处理模板参数
                $filteredTemplateParams = $this->filterTemplateParams(
                    $template->allowed_params ?? [],
                    $templateParams ?? []
                );
                
                // 处理扩展参数
                $filteredExtendParams = $this->filterExtendParams(
                    $template->allowed_extend_params ?? [],
                    $extendParams ?? []
                );
            } catch (\Exception $e) {
                // 对于系统默认client，如果模板验证失败，直接使用原始参数
                $template = PushTemplate::where('template_code', $templateCode)->first();
                $filteredTemplateParams = $templateParams ?? [];
                $filteredExtendParams = $extendParams ?? [];
            }
        } else {
            // 正常验证模板
            $template = $this->getAndValidateTemplate($templateCode, PushMessage::PUSH_TYPE_PERSONAL, $currentClientId);
            
            // 处理模板参数
            $filteredTemplateParams = $this->filterTemplateParams(
                $template->allowed_params ?? [],
                $templateParams ?? []
            );
            
            // 处理扩展参数
            $filteredExtendParams = $this->filterExtendParams(
                $template->allowed_extend_params ?? [],
                $extendParams ?? []
            );
        }

        // 如果模板仍然不存在，抛出异常
        if (!$template) {
            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }

        // 处理重定向链接
        if ($redirectTo) {
            if (RedirectToBuilder::isValidRedirectTo($redirectTo)) {
                $filteredExtendParams['redirect_to'] = $redirectTo;
            }
        }

        // 准备参数
        $params = [
            'template_params' => $filteredTemplateParams,
            'extend_params' => $filteredExtendParams,
        ];

        // 创建消息记录
        $message = $this->createPushMessage(
            template: $template,
            pushType: PushMessage::PUSH_TYPE_PERSONAL,
            params: $params,
            oauthClientId: $currentClientId,
            userUuid: $userUuid
        );

        // 创建用户消息记录
        $this->createUserMessages($message->id, collect([$userUuid]));

        // 使用普通推送Job
        ProcessPushMessageJob::dispatch($message);

        return $message;
    }

    /**
     * 系统内部触发订阅消息推送
     * 
     * @param string $subscriptionCode 订阅代码
     * @param array|null $templateParams 模板参数
     * @param array|null $extendParams 扩展参数
     * @param string|null $redirectTo 重定向链接
     * @return PushMessage 创建的推送消息
     * @throws PushException|ValidationException
     */
    public function triggerSubscription(
        string $subscriptionCode,
        ?array $templateParams = null,
        ?array $extendParams = null,
        ?string $redirectTo = null
    ): PushMessage {
        // 系统内部调用，使用默认client_id
        return $this->triggerSubscriptionWithClient(
            $subscriptionCode,
            null,
            $templateParams,
            $extendParams,
            $redirectTo
        );
    }

    /**
     * 为特定应用触发订阅消息推送
     * 
     * @param string $subscriptionCode 订阅代码
     * @param string|null $clientId 应用ID
     * @param array|null $templateParams 模板参数
     * @param array|null $extendParams 扩展参数
     * @param string|null $redirectTo 重定向链接
     * @return PushMessage 创建的推送消息
     * @throws PushException|ValidationException
     */
    public function triggerSubscriptionWithClient(
        string $subscriptionCode,
        ?string $clientId = null,
        ?array $templateParams = null,
        ?array $extendParams = null,
        ?string $redirectTo = null
    ): PushMessage {
        // 如果没有指定clientId，使用系统默认client
        $currentClientId = $clientId ?? config('uc.system_client_id');
        
        // 检查是否为系统默认client_id
        $isSystemDefaultClient = (string)$currentClientId === (string)config('uc.system_client_id');
        
        // 系统默认客户端不支持订阅推送，改用广播
        if ($isSystemDefaultClient) {
            return $this->broadcastWithClient(
                $subscriptionCode, // 使用订阅代码作为模板代码
                $currentClientId,
                $templateParams,
                $extendParams,
                $redirectTo
            );
        }

        // 获取应用信息
        $client = OAuthCacher::getClientByKey($currentClientId);
        if (!$client) {
            throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        // 获取订阅配置
        $subscription = OauthClientSubscription::where('oauth_client_id', $client->id)
            ->where('subscription_code', $subscriptionCode)
            ->where('status', OauthClientSubscription::STATUS_ENABLE)
            ->firstOrFail();

        $template = $subscription->pushTemplate;

        // 处理模板参数
        $filteredTemplateParams = $this->filterTemplateParams(
            $template->allowed_params ?? [],
            $templateParams ?? []
        );

        // 处理扩展参数
        $filteredExtendParams = $this->filterExtendParams(
            $template->allowed_extend_params ?? [],
            $extendParams ?? []
        );

        // 处理重定向链接
        if ($redirectTo) {
            if (RedirectToBuilder::isValidRedirectTo($redirectTo)) {
                $filteredExtendParams['redirect_to'] = $redirectTo;
            }
        }

        // 准备参数
        $params = [
            'template_params' => $filteredTemplateParams,
            'extend_params' => $filteredExtendParams,
        ];

        // 创建消息记录
        $message = $this->createPushMessage(
            template: $template,
            pushType: PushMessage::PUSH_TYPE_PERSONAL,
            params: $params,
            oauthClientId: $currentClientId,
            userUuid: null,
            subscriptionId: $subscription->id
        );

        // 使用订阅推送Job
        ProcessSubscriptionPushJob::dispatch($message, $subscription);

        return $message;
    }

    /**
     * 系统内部广播消息
     * 
     * @param string $templateCode 模板代码
     * @param array|null $templateParams 模板参数
     * @param array|null $extendParams 扩展参数
     * @param string|null $redirectTo 重定向链接
     * @return PushMessage 创建的推送消息
     * @throws PushException|ValidationException
     */
    public function broadcast(
        string $templateCode,
        ?array $templateParams = null,
        ?array $extendParams = null,
        ?string $redirectTo = null
    ): PushMessage {
        // 系统内部调用，使用默认client_id
        return $this->broadcastWithClient(
            $templateCode,
            null,
            $templateParams,
            $extendParams,
            $redirectTo
        );
    }

    /**
     * 为特定应用广播消息
     * 
     * @param string $templateCode 模板代码
     * @param string|null $clientId 应用ID
     * @param array|null $templateParams 模板参数
     * @param array|null $extendParams 扩展参数
     * @param string|null $redirectTo 重定向链接
     * @return PushMessage 创建的推送消息
     * @throws PushException|ValidationException
     */
    public function broadcastWithClient(
        string $templateCode,
        ?string $clientId = null,
        ?array $templateParams = null,
        ?array $extendParams = null,
        ?string $redirectTo = null
    ): PushMessage {
        // 如果没有指定clientId，使用系统默认client
        $currentClientId = $clientId ?? config('uc.system_client_id');
        
        // 检查是否为系统默认client_id
        $isSystemDefaultClient = (string)$currentClientId === (string)config('uc.system_client_id');

        // 如果不是系统默认client_id，需要验证客户端
        if (!$isSystemDefaultClient) {
            // 获取应用信息
            $client = OAuthCacher::getClientByKey($currentClientId);
            if (!$client) {
                throw new PushException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
            }
        }

        // 变量初始化
        $template = null;
        $filteredTemplateParams = [];
        $filteredExtendParams = [];

        // 如果是系统默认client_id，可以跳过模板验证
        if ($isSystemDefaultClient) {
            // 尝试获取模板信息，但不强制验证
            try {
                $template = $this->getAndValidateTemplate($templateCode, PushMessage::PUSH_TYPE_BROADCAST, $currentClientId);
                
                // 处理模板参数
                $filteredTemplateParams = $this->filterTemplateParams(
                    $template->allowed_params ?? [],
                    $templateParams ?? []
                );
                
                // 处理扩展参数
                $filteredExtendParams = $this->filterExtendParams(
                    $template->allowed_extend_params ?? [],
                    $extendParams ?? []
                );
            } catch (\Exception $e) {
                // 对于系统默认client，如果模板验证失败，直接使用原始参数
                $template = PushTemplate::where('template_code', $templateCode)->first();
                $filteredTemplateParams = $templateParams ?? [];
                $filteredExtendParams = $extendParams ?? [];
            }
        } else {
            // 正常验证模板
            $template = $this->getAndValidateTemplate($templateCode, PushMessage::PUSH_TYPE_BROADCAST, $currentClientId);
            
            // 处理模板参数
            $filteredTemplateParams = $this->filterTemplateParams(
                $template->allowed_params ?? [],
                $templateParams ?? []
            );
            
            // 处理扩展参数
            $filteredExtendParams = $this->filterExtendParams(
                $template->allowed_extend_params ?? [],
                $extendParams ?? []
            );
        }

        // 如果模板仍然不存在，抛出异常
        if (!$template) {
            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }

        // 处理重定向链接
        if ($redirectTo) {
            if (RedirectToBuilder::isValidRedirectTo($redirectTo)) {
                $filteredExtendParams['redirect_to'] = $redirectTo;
            }
        }

        // 准备参数
        $params = [
            'template_params' => $filteredTemplateParams,
            'extend_params' => $filteredExtendParams,
        ];

        // 创建消息记录
        $message = $this->createPushMessage(
            template: $template,
            pushType: PushMessage::PUSH_TYPE_BROADCAST,
            params: $params,
            oauthClientId: $currentClientId
        );
        
        // 使用普通推送Job
        ProcessPushMessageJob::dispatch($message);

        return $message;
    }

    /**
     * 创建推送消息记录
     */
    private function createPushMessage(
        PushTemplate $template,
        int $pushType,
        array $params,
        string $oauthClientId,
        ?string $userUuid = null,
        ?int $subscriptionId = null
    ): PushMessage {
        $message = new PushMessage([
            'msg_key' => Str::uuid()->toString(),
            'push_template_id' => $template->id,
            'oauth_client_id' => $oauthClientId,
            'oauth_client_subscription_id' => $subscriptionId,
            'title' => $template->title,
            'content' => $template->content,
            'category' => $template->category,
            'delivery_type' => $template->delivery_type,
            'push_type' => $pushType,
            'target_id' => $userUuid ?? null,
            'expired_seconds' => 300,
            'template_params' => $params['template_params'] ?? null,
            'extend_params' => $params['extend_params'] ?? null,
            'status' => PushMessage::STATUS_PENDING,
            'push_time' => Carbon::now(),
            'is_silent' => $template->is_silent,
            'show_client_info' => $template->show_client_info,
        ]);

        $message->save();

        return $message;
    }

    /**
     * 批量创建用户消息记录
     */
    private function createUserMessages(int $messageId, Collection $userUuids): void
    {
        $records = $userUuids->map(function ($userUuid) use ($messageId) {
            return [
                'user_uuid' => $userUuid,
                'push_message_id' => $messageId,
                'is_read' => UserMessage::IS_READ_NO,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        })->toArray();

        UserMessage::insert($records);
    }

    /**
     * 获取并验证推送模板
     * @throws PushException
     */
    private function getAndValidateTemplate(string $templateCode, int $pushType, string $clientId): PushTemplate
    {
        // 检查是否为系统默认client_id
        $isSystemDefaultClient = (string)$clientId === (string)config('uc.system_client_id');

        // 如果是系统默认client_id，直接查询全局模板
        if ($isSystemDefaultClient) {
            $template = PushTemplate::where('code', $templateCode)
                ->where('status', PushTemplate::STATUS_ENABLE)
                ->first();

            if (!$template) {
                throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
            }

            return $template;
        }

        // 先尝试查找客户端模板
        $clientTemplate = OauthClientTemplate::where('template_code', $templateCode)
            ->where('oauth_client_id', $clientId)
            ->first();

        if ($clientTemplate) {
            return $clientTemplate->pushTemplate;
        }

        // 如果没有找到客户端模板，直接查询全局模板
        $template = PushTemplate::where('code', $templateCode)
            ->where('status', PushTemplate::STATUS_ENABLE)
            ->first();

        if (!$template) {
            throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }

        // 验证推送类型与模板匹配
        if ($pushType === PushMessage::PUSH_TYPE_BROADCAST) {
            if ($template->category !== PushMessageCategoryEnum::CATEGORY_SYSTEM->value) {
                throw new PushException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
            }
        }

        return $template;
    }

    /**
     * 筛选并验证模板参数
     * @throws ValidationException
     */
    private function filterTemplateParams(array $allowedParams, array $inputParams): array
    {
        // 筛选允许的参数
        $filteredParams = [];
        foreach ($allowedParams as $key => $desc) {
            if (key_exists($key, $inputParams)) {
                $filteredParams[$key] = $inputParams[$key];
            } else {
                // 验证必填参数
                throw ValidationException::withMessages([
                    "template_params.{$key}" => "缺少必要参数: {$desc}",
                ]);
            }
        }

        return $filteredParams;
    }

    /**
     * 筛选扩展参数
     */
    private function filterExtendParams(array $allowedParams, array $inputParams): array
    {
        $filteredParams = [];
        foreach ($allowedParams as $key => $desc) {
            $filteredParams[$key] = $inputParams[$key] ?? '';
        }

        return $filteredParams;
    }
}
