<?php

namespace App\Events;

use App\Models\AdminDepartment;
use App\Models\Client;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminDepartmentUpdateEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $department;

    /**
     * Create a new event instance.
     */
    public function __construct(AdminDepartment $adminDepartment) {
        $this->department = $adminDepartment;
    }

    public function getDepartment(): AdminDepartment {
        return $this->department;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
