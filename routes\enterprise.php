<?php

use App\Http\Enterprise\{
    EnterpriseController,
    EnterpriseDepartmentController,
    EnterpriseContactController,
    EnterpriseClientController,
    EnterpriseAdminController,
    EnterpriseRoleController,
    EnterpriseAuthController,
};

/*
|--------------------------------------------------------------------------
| 企业认证路由（无需认证）
|--------------------------------------------------------------------------
*/
Route::prefix('auth')->group(function () {
    // 企业拥有者登录
    Route::post('enterprise/login', [EnterpriseAuthController::class, 'enterpriseLogin']);
    Route::post('enterprise/refresh', [EnterpriseAuthController::class, 'enterpriseRefresh']);

    // 企业管理员登录
    Route::post('enterprise-admin/login', [EnterpriseAuthController::class, 'enterpriseAdminLogin']);
    Route::post('enterprise-admin/refresh', [EnterpriseAuthController::class, 'enterpriseAdminRefresh']);
});

/*
|--------------------------------------------------------------------------
| 企业拥有者路由（需要enterprise guard认证）
|--------------------------------------------------------------------------
*/
Route::middleware(['auth:enterprise'])->prefix('enterprise')->group(function () {
    // 认证相关
    Route::post('logout', [EnterpriseAuthController::class, 'enterpriseLogout']);
    Route::get('profile', [EnterpriseAuthController::class, 'enterpriseProfile']);
    Route::put('change-password', [EnterpriseAuthController::class, 'enterpriseChangePassword']);

    // 企业管理员管理
    Route::prefix('admins')->group(function () {
        Route::get('/', [EnterpriseAdminController::class, 'index']);
        Route::post('/', [EnterpriseAdminController::class, 'store']);
        Route::get('{id}', [EnterpriseAdminController::class, 'show']);
        Route::put('{id}', [EnterpriseAdminController::class, 'update']);
        Route::delete('{id}', [EnterpriseAdminController::class, 'destroy']);
        Route::post('bind-user', [EnterpriseAdminController::class, 'bindUser']);
        Route::delete('{id}/unbind', [EnterpriseAdminController::class, 'unbindUser']);
        Route::get('available-users', [EnterpriseAdminController::class, 'availableUsers']);
    });

    // 企业角色管理
    Route::prefix('roles')->group(function () {
        Route::get('/', [EnterpriseRoleController::class, 'index']);
        Route::post('/', [EnterpriseRoleController::class, 'store']);
        Route::get('all', [EnterpriseRoleController::class, 'all']);
        Route::get('{id}', [EnterpriseRoleController::class, 'show']);
        Route::put('{id}', [EnterpriseRoleController::class, 'update']);
        Route::delete('{id}', [EnterpriseRoleController::class, 'destroy']);
        Route::post('assign', [EnterpriseRoleController::class, 'assignRole']);
        Route::post('revoke', [EnterpriseRoleController::class, 'revokeRole']);
        Route::get('permissions/list', [EnterpriseRoleController::class, 'permissions']);
        Route::get('{id}/permissions', [EnterpriseRoleController::class, 'rolePermissions']);
    });
});

/*
|--------------------------------------------------------------------------
| 企业管理员路由（需要enterprise_admin guard认证）
|--------------------------------------------------------------------------
*/
Route::middleware(['auth:enterprise_admin'])->prefix('enterprise-admin')->group(function () {
    // 认证相关
    Route::post('logout', [EnterpriseAuthController::class, 'enterpriseAdminLogout']);
    Route::get('profile', [EnterpriseAuthController::class, 'enterpriseAdminProfile']);
    Route::put('change-password', [EnterpriseAuthController::class, 'enterpriseAdminChangePassword']);
});

/*
|--------------------------------------------------------------------------
| 企业管理路由（系统管理员访问）
|--------------------------------------------------------------------------
*/
Route::prefix('enterprises')
    ->group(function () {
        Route::get('/', [EnterpriseController::class, 'index']);                 // 获取企业列表
        Route::post('/', [EnterpriseController::class, 'store']);                // 创建企业
        Route::get('{id}', [EnterpriseController::class, 'show']);              // 获取企业详情
        Route::put('{id}', [EnterpriseController::class, 'update']);            // 更新企业信息
        Route::delete('{id}', [EnterpriseController::class, 'destroy']);        // 删除企业

        // 企业部门管理
        Route::prefix('{enterpriseId}/departments')
            ->group(function () {
                Route::get('/', [EnterpriseDepartmentController::class, 'index']);            // 获取部门列表
                Route::get('/tree', [EnterpriseDepartmentController::class, 'tree']);         // 获取部门树形结构
                Route::post('/', [EnterpriseDepartmentController::class, 'store']);           // 创建部门
                Route::get('{id}', [EnterpriseDepartmentController::class, 'show']);         // 获取部门详情
                Route::put('{id}', [EnterpriseDepartmentController::class, 'update']);       // 更新部门信息
                Route::delete('{id}', [EnterpriseDepartmentController::class, 'destroy']);   // 删除部门
                Route::put('{id}/move', [EnterpriseDepartmentController::class, 'move']);    // 移动部门
                Route::get('{id}/contacts', [EnterpriseDepartmentController::class, 'contacts']); // 获取部门联系人
            });

        // 企业联系人管理
        Route::prefix('{enterpriseId}/contacts')
            ->group(function () {
                Route::get('/', [EnterpriseContactController::class, 'index']);                 // 获取联系人列表
                Route::post('/', [EnterpriseContactController::class, 'store']);                // 创建联系人
                Route::post('/batch-import', [EnterpriseContactController::class, 'batchImport']); // 批量导入联系人
                Route::get('{id}', [EnterpriseContactController::class, 'show']);              // 获取联系人详情
                Route::put('{id}', [EnterpriseContactController::class, 'update']);            // 更新联系人信息
                Route::delete('{id}', [EnterpriseContactController::class, 'destroy']);        // 删除联系人
                Route::put('{id}/toggle-leader', [EnterpriseContactController::class, 'toggleLeader']); // 设置/取消部门负责人
            });

        // 企业应用管理
        Route::prefix('{enterpriseId}/clients')
            ->group(function () {
                Route::get('/', [EnterpriseClientController::class, 'index']);                 // 获取应用列表
                Route::post('/', [EnterpriseClientController::class, 'store']);                // 创建应用
                Route::get('options', [EnterpriseClientController::class, 'options']);         // 获取选项
                Route::get('pending', [EnterpriseClientController::class, 'pending']);         // 获取待审核应用
                Route::get('{id}', [EnterpriseClientController::class, 'show']);              // 获取应用详情
                Route::put('{id}', [EnterpriseClientController::class, 'update']);            // 更新应用信息
                Route::delete('{id}', [EnterpriseClientController::class, 'destroy']);        // 删除应用
                Route::post('{id}/approve', [EnterpriseClientController::class, 'approve']);   // 审核通过应用
                Route::post('{id}/reject', [EnterpriseClientController::class, 'reject']);     // 审核拒绝应用
            });
    });