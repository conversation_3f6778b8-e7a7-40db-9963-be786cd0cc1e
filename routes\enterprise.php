<?php

use App\Http\Enterprise\{
    EnterpriseController,
    EnterpriseDepartmentController,
    EnterpriseContactController,
    EnterpriseClientController,
};

/*
|--------------------------------------------------------------------------
| 企业管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('enterprises')
    ->group(function () {
        Route::get('/', [EnterpriseController::class, 'index']);                 // 获取企业列表
        Route::post('/', [EnterpriseController::class, 'store']);                // 创建企业
        Route::get('{id}', [EnterpriseController::class, 'show']);              // 获取企业详情
        Route::put('{id}', [EnterpriseController::class, 'update']);            // 更新企业信息
        Route::delete('{id}', [EnterpriseController::class, 'destroy']);        // 删除企业

        // 企业部门管理
        Route::prefix('{enterpriseId}/departments')
            ->group(function () {
                Route::get('/', [EnterpriseDepartmentController::class, 'index']);            // 获取部门列表
                Route::get('/tree', [EnterpriseDepartmentController::class, 'tree']);         // 获取部门树形结构
                Route::post('/', [EnterpriseDepartmentController::class, 'store']);           // 创建部门
                Route::get('{id}', [EnterpriseDepartmentController::class, 'show']);         // 获取部门详情
                Route::put('{id}', [EnterpriseDepartmentController::class, 'update']);       // 更新部门信息
                Route::delete('{id}', [EnterpriseDepartmentController::class, 'destroy']);   // 删除部门
                Route::put('{id}/move', [EnterpriseDepartmentController::class, 'move']);    // 移动部门
                Route::get('{id}/contacts', [EnterpriseDepartmentController::class, 'contacts']); // 获取部门联系人
            });

        // 企业联系人管理
        Route::prefix('{enterpriseId}/contacts')
            ->group(function () {
                Route::get('/', [EnterpriseContactController::class, 'index']);                 // 获取联系人列表
                Route::post('/', [EnterpriseContactController::class, 'store']);                // 创建联系人
                Route::post('/batch-import', [EnterpriseContactController::class, 'batchImport']); // 批量导入联系人
                Route::get('{id}', [EnterpriseContactController::class, 'show']);              // 获取联系人详情
                Route::put('{id}', [EnterpriseContactController::class, 'update']);            // 更新联系人信息
                Route::delete('{id}', [EnterpriseContactController::class, 'destroy']);        // 删除联系人
                Route::put('{id}/toggle-leader', [EnterpriseContactController::class, 'toggleLeader']); // 设置/取消部门负责人
            });
    });