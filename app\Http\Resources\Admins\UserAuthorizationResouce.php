<?php

namespace App\Http\Resources\Admins;

use App\Enums\OAuthScopeEnum;
use App\Models\OAuthUserAuthorization;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\OAuthUserAuthorization
 */
class UserAuthorizationResouce extends JsonResource
{
    public function toArray(Request $request) {
        return [
            'id'             => $this->id,
            //            'user_uuid'      => $this->user_uuid,
            'nickname'       => $this->user_type == OAuthUserAuthorization::USER_TYPE_USER? $this->user?->nickname : $this->user?->true_name,
            'avatar'         => $this->user_type == OAuthUserAuthorization::USER_TYPE_USER? $this->user?->display_avatar : $this->user?->display_avatar,
            'granted_scopes' => collect($this->granted_scopes)->map(function ($scope) {
                return [
                    'key'   => $scope,
                    'value' => OAuthScopeEnum::from($scope)
                                             ->label(),
                ];
            }),
            'is_revoked'     => $this->is_revoked,
            'revoked_text'   => $this->revoked_text,
            'client'         => ClientResource::make($this->whenLoaded('client')),
        ];
    }
}
