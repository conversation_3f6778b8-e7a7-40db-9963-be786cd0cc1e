<?php

namespace App\Observers;

use App\Events\UserStatusChangedEvent;
use App\Events\UserUpdateEvent;
use App\Models\User;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class UserObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void {
        //
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void {
        $watchFields = [
            'status',
            'nickname',
            // 'nickname_confirmed',
            'avatar',
            // 'avatar_confirmed',
            'mobile',
            'gender',
            'realname_auth_confirmed',
            'comment',
            // 'comment_confirmed',
        ];

        $changes = $user->getChanges();
        $original = $user->getOriginal();

        $changedWatchFields = array_intersect(array_keys($changes), $watchFields);

        if (!empty($changedWatchFields)) {
            // 如果status字段改变，触发状态变更事件
            if (in_array('status', $changedWatchFields)) {
                event(new UserStatusChangedEvent($user->getAuthIdentifier(), $user->status));
            }

            // 触发用户更新事件，包含所有变更的监听字段
            foreach ($changedWatchFields as $field) {
                if ($field == 'status') {
                    continue;
                }

                event(new UserUpdateEvent($user, $field));
            }


        }
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void {
        event(new UserStatusChangedEvent($user, $user->status));
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void {
        //
    }
}
