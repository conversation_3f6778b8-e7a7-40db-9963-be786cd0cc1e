<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $msg_key 消息唯一标识
 * @property int $push_template_id 关联的模板ID
 * @property string|null $oauth_client_id 来源应用ID
 * @property int|null $show_client_info 是否显示客户端信息
 * @property int|null $oauth_client_subscription_id 关联的订阅ID
 * @property int|null $push_schedule_id 关联的定时任务ID
 * @property int|null $push_schedule_batch_id 关联的批次ID
 * @property string $title 消息标题
 * @property string $content 消息内容
 * @property string|null $banner_url banner图片
 * @property int $category 消息分类
 * @property int $delivery_type 目标类型:1安卓设备,2iOS设备,3用户,5实时活动pushToken,6实时活动activityId,7鸿蒙设备
 * @property int $push_type 推送类型 1:普通 2:群发
 * @property string|null $target_id 推送目标ID
 * @property int $status 状态:0待发送,1发送成功,2发送失败
 * @property int $is_silent 是否静默推送 0否 1是
 * @property \Illuminate\Support\Carbon|null $push_time 推送时间
 * @property int|null $expired_seconds 有效期(秒)
 * @property array<array-key, mixed>|null $template_params 模板参数
 * @property array<array-key, mixed>|null $extend_params 扩展参数
 * @property string|null $error_msg 失败原因
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read mixed $is_silent_text
 * @property-read \App\Models\Client|null $oauthClient
 * @property-read \App\Models\OauthClientSubscription|null $oauthClientSubscription
 * @property-read \App\Models\PushSchedule|null $pushSchedule
 * @property-read \App\Models\PushScheduleBatch|null $pushScheduleBatch
 * @property-read \App\Models\PushTemplate|null $pushTemplate
 * @property-read mixed $push_type_text
 * @property-read mixed $status_text
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\UserMessage> $userMessages
 * @property-read int|null $user_messages_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereBannerUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereDeliveryType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereErrorMsg($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereExpiredSeconds($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereExtendParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereIsSilent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereMsgKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereOauthClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereOauthClientSubscriptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage wherePushScheduleBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage wherePushScheduleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage wherePushTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage wherePushTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage wherePushType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereShowClientInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereTargetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereTemplateParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushMessage withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperPushMessage
 */
class PushMessage extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'msg_key',
        'push_template_id',
        'oauth_client_id',
        'oauth_client_subscription_id',
        'push_schedule_id',
        'push_schedule_batch_id',
        'title',
        'content',
        'category',
        'delivery_type',
        'push_type',
        'target_id',
        'status',
        'push_time',
        'expired_seconds',
        'template_params',
        'extend_params',
        'error_msg',
        'is_silent',
        'show_client_info',
        // 'banner_url',
    ];

    protected $casts = [
        'template_params' => 'array',
        'extend_params' => 'array',
        'push_time' => 'datetime',
    ];


    // 未执行 0, 已执行1 , 执行失败 2
    const STATUS_PENDING = 0;
    const STATUS_PROCESSING = 1;
    const STATUS_EXECUTED = 2;
    const STATUS_FAILED = 3;

    public static array $statusMap = [
        self::STATUS_EXECUTED => '已执行',
        self::STATUS_PENDING => '未执行',
        self::STATUS_PROCESSING => '执行中',
        self::STATUS_FAILED => '执行失败',
    ];

    const PUSH_TYPE_PERSONAL = 1;
    const PUSH_TYPE_BROADCAST = 2;
    const PUSH_TYPE_GROUP = 3;

    public static array $pushTypeMap = [
        self::PUSH_TYPE_PERSONAL => '个人推送',
        self::PUSH_TYPE_BROADCAST => '广播推送',
        self::PUSH_TYPE_GROUP => '群发推送',
    ];


    const IS_SILENT_NO = 0;
    const IS_SILENT_YES = 1;

    public static array $isSilentMap = [
        self::IS_SILENT_YES => '是',
        self::IS_SILENT_NO => '否',
    ];

    protected function isSilentText(): Attribute {
        return Attribute::make(get: fn() => self::$isSilentMap[$this->is_silent] ?? '未知');
    }

    protected function statusText(): Attribute {
        return Attribute::make(get: fn($value) => self::$statusMap[$value] ?? '未知');
    }

    protected function pushTypeText(): Attribute {
        return Attribute::make(get: fn($value) => self::$pushTypeMap[$value] ?? '未知');
    }

    public function pushTemplate(): BelongsTo {
        return $this->belongsTo(PushTemplate::class);
    }

    public function pushSchedule(): BelongsTo {
        return $this->belongsTo(PushSchedule::class);
    }

    public function pushScheduleBatch(): BelongsTo {
        return $this->belongsTo(PushScheduleBatch::class);
    }

    public function oauthClientSubscription(): BelongsTo {
        return $this->belongsTo(OauthClientSubscription::class);
    }

    public function userMessages(): HasMany {
        return $this->hasMany(UserMessage::class);
    }

    public function oauthClient(): BelongsTo {
        return $this->belongsTo(Client::class, 'oauth_client_id', 'client_key');
    }

    public function isPending(): bool {
        return $this->status === self::STATUS_PENDING;
    }

    public function isSuccess(): bool {
        return $this->status === self::STATUS_EXECUTED;
    }

    public function isFailed(): bool {
        return $this->status === self::STATUS_FAILED;
    }
}
