<?php

namespace App\Services\Push\Messages;

use App\Services\Push\Contracts\MessageInterface;

abstract class AbstractMessage implements MessageInterface
{
    /**
     * 消息标题
     */
    protected string $title;

    /**
     * 消息内容
     */
    protected string $content;

    /**
     * 推送目标
     */
    protected array|string $target;

    /**
     * 目标类型
     */
    protected string $targetType = 'alias';

    /**
     * 消息过期时间(秒)
     */
    protected int $expireTime = 86400;

    /**
     * 额外参数
     */
    protected array $extras = [];

    /**
     * 通知类型 notification/message
     */
    protected string $type = 'notification';

    /**
     * 消息ID
     */
    protected ?string $messageId = null;

    /**
     * 通知栏消息点击行为
     */
    protected ?string $afterOpen = 'go_custom';

    /**
     * 自定义提示音
     */
    protected ?string $sound = null;

    /**
     * 角标设置
     */
    protected ?string $badge = null;

    /**
     * 点击跳转URL
     */
    protected ?string $url = null;

    /**
     * 点击打开Activity
     */
    protected ?string $activity = null;

    /**
     * 自定义消息内容
     */
    protected mixed $custom = null;

    /**
     * 大文本内容
     */
    protected ?string $bigBody = null;

    /**
     * 消息重弹配置
     */
    protected ?int $rePop = null;

    /**
     * 通知栏图标
     */
    protected ?string $icon = null;

    /**
     * 通知栏大图标URL
     */
    protected ?string $img = null;

    /**
     * 消息展开大图URL
     */
    protected ?string $expandImage = null;

    /**
     * 通知栏样式ID
     */
    protected ?int $builderId = null;

    /**
     * 消息类别
     */
    protected ?int $category = null;

    // 实现 getter 方法
    public function getTitle(): string
    {
        return $this->title;
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function getTarget(): array|string
    {
        return $this->target;
    }

    public function getTargetType(): string
    {
        return $this->targetType;
    }

    public function getExpireTime(): int
    {
        return $this->expireTime;
    }

    public function getExtras(): array
    {
        return $this->extras;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getMessageId(): ?string
    {
        return $this->messageId ?? uniqid('msg_');
    }

    public function getAfterOpen(): ?string
    {
        return $this->afterOpen;
    }

    public function getSound(): ?string
    {
        return $this->sound;
    }

    public function getBadge(): ?string
    {
        return $this->badge;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function getActivity(): ?string
    {
        return $this->activity;
    }

    public function getCustom(): mixed
    {
        return $this->custom;
    }

    public function getBigBody(): ?string
    {
        return $this->bigBody;
    }

    public function getRePop(): ?int
    {
        return $this->rePop;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function getImg(): ?string
    {
        return $this->img;
    }

    public function getExpandImage(): ?string
    {
        return $this->expandImage;
    }

    public function getBuilderId(): ?int
    {
        return $this->builderId;
    }

    public function getCategory(): ?int
    {
        return $this->category;
    }

    // 实现 setter 方法
    public function setTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;
        return $this;
    }

    public function setTarget(array|string $target): self
    {
        $this->target = $target;
        return $this;
    }

    public function setTargetType(string $targetType): self
    {
        $this->targetType = $targetType;
        return $this;
    }

    public function setExpireTime(int $expireTime): self
    {
        $this->expireTime = $expireTime;
        return $this;
    }

    public function setExtras(array $extras): self
    {
        $this->extras = $extras;
        return $this;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function setMessageId(string $messageId): self
    {
        $this->messageId = $messageId;
        return $this;
    }

    public function setAfterOpen(string $afterOpen): self
    {
        $this->afterOpen = $afterOpen;
        return $this;
    }

    public function setSound(string $sound): self
    {
        $this->sound = $sound;
        return $this;
    }

    public function setBadge(string $badge): self
    {
        $this->badge = $badge;
        return $this;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;
        return $this;
    }

    public function setActivity(string $activity): self
    {
        $this->activity = $activity;
        return $this;
    }

    public function setCustom(mixed $custom): self
    {
        $this->custom = $custom;
        return $this;
    }

    public function setBigBody(string $bigBody): self
    {
        $this->bigBody = $bigBody;
        return $this;
    }

    public function setRePop(int $rePop): self
    {
        $this->rePop = $rePop;
        return $this;
    }

    public function setIcon(string $icon): self
    {
        $this->icon = $icon;
        return $this;
    }

    public function setImg(string $img): self
    {
        $this->img = $img;
        return $this;
    }

    public function setExpandImage(string $expandImage): self
    {
        $this->expandImage = $expandImage;
        return $this;
    }

    public function setBuilderId(int $builderId): self
    {
        $this->builderId = $builderId;
        return $this;
    }

    public function setCategory(int $category): self
    {
        $this->category = $category;
        return $this;
    }
}
