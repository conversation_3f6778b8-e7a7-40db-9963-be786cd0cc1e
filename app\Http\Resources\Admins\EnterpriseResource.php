<?php

namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Admin User Resource
 * @property-read \App\Models\Enterprise $resource
 * @mixin \App\Models\Enterprise
 */
class EnterpriseResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'contact_person' => $this->contact_person,
            'contact_phone' => $this->contact_phone,
            'address' => $this->address,
        ];
    }
}
