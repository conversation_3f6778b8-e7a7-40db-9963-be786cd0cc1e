<?php

namespace App\Exceptions;

class AuthException extends BaseException
{
    // 增加错误代码: 无此用户, 用户状态异常, 用户密码错误, 用户已存在, 用户未登录
//    const USER_NOT_FOUND = 110001;
//    const USER_STATUS_ERROR = 110002;
//    const USER_PASSWORD_ERROR = 110003;
//    const USER_EXIST = 110004;
//    const USER_NOT_LOGIN = 110005;
//    // 三方授权失败
//    const THIRD_AUTH_FAILED = 110006;
//    // 一键登录获取手机号失败
//    const ONECLICK_GET_PHONE_FAILED = 110007;
//    // 绑定手机号超时
//    const BIND_PHONE_TIMEOUT = 110008;
//    // 手机已绑定
//    const PHONE_ALREADY_BIND = 110009;
//
//    public static function message($code) {
//        $msgArr = [
//            static::USER_NOT_FOUND => '无此用户',
//            static::USER_STATUS_ERROR => '用户状态异常',
//            static::USER_PASSWORD_ERROR => '用户密码错误',
//            static::USER_EXIST => '用户已存在',
//            static::USER_NOT_LOGIN => '用户未登录',
//            static::THIRD_AUTH_FAILED => '三方授权失败',
//            static::ONECLICK_GET_PHONE_FAILED => '一键登录获取手机号失败',
//            static::BIND_PHONE_TIMEOUT => '绑定手机号超时',
//            static::PHONE_ALREADY_BIND => '手机已绑定',
//        ];
//
//        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
//    }
}
