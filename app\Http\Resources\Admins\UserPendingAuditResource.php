<?php

namespace App\Http\Resources\Admins;

use App\Models\User;
use App\Services\CloudFiles\Facades\CloudFiles;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserPendingAuditResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var User $this */
        $avatar_pending = json_decode($this->latestPendingAvatarLog?->new_value, true);

        return [
            'uuid' => $this->uuid,
            'username' => $this->username,
            'nickname' => $this->nickname,
            'nickname_confirmed' => $this->nickname_confirmed,
            'avatar' => $this->display_avatar,
            'avatar_confirmed' => $this->avatar_confirmed,
            'comment' => $this->comment,
            'comment_confirmed' => $this->comment_confirmed,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'nickname_pending' => $this->latestPendingNicknameLog?->new_value,
            'avatar_pending' => $avatar_pending ? CloudFiles::getFileUrl($avatar_pending['provider'], $avatar_pending['path']) : null,
            'comment_pending' => $this->latestPendingCommentLog?->new_value,
            // 'username_pending' => $this->latestPendingUsernameLog?->new_value,
        ];
    }
}
