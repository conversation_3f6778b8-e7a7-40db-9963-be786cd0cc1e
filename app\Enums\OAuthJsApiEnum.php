<?php

namespace App\Enums;

enum OAuthJsApiEnum: string
{
    // ============== 系统基础 ==============
    case ALL = '*';
    case NONE = 'none';
    case SYSTEM_APPLICATION_LOGIN = 'systemLogin';
    case SYSTEM_REQUEST = 'systemRequest';
    case APPLICATION_LOGIN = 'login';
    case CHECK_SESSION = 'checkSession';
    case SET_DATA = 'setData';
    case SET_SHARE_DATA = 'setShareData';
    case SHARE = 'share';
    case NAVIGATE_TO = 'navigateTo';
    case NAVIGATE_BACK = 'navigateBack';
    case SWITCH_TAB = 'switchTab';
    case TOAST = 'toast';
    case SHOW_LOADING = 'showLoading';
    case HIDE_LOADING = 'hideLoading';
    case SHOW_MODAL = 'showModal';
    case IMPACT_FEEDBACK = 'impactFeedback';
    case VIBRATE = 'vibrate';
    case ON_KEYBOARD_HEIGHT_CHANGE = 'onKeyboardHeightChange';
    case OFF_KEYBOARD_HEIGHT_CHANGE = 'offKeyboardHeightChange';
    case GET_SYSTEM_INFO = 'getSystemInfo';
    case SET_WEB_VIEW_STYLE = 'setWebViewStyle';
    case SET_NAVIGATION_BAR_TITLE = 'setNavigationBarTitle';
    case SET_NAVIGATION_BAR_COLOR = 'setNavigationBarColor';
    case SET_NAVIGATION_BAR_STYLE = 'setNavigationBarStyle';
    case SET_NAVIGATION_BAR_RIGHT_ITEMS = 'setNavigationBarRightItems';
    case ALIPAY = 'alipay';
    case WXPAY = 'wxpay';
    case SAVE_IMAGE_TO_PHOTOS_ALBUM = 'saveImageToPhotosAlbum';
    case OPEN_APP = 'openApp';
    case SCAN_CODE = 'scanCode';
    case SHOW_COMMENT_CONTROL = 'showCommentControl';
    case START_SPEAKING = 'startSpeeking';
    case STOP_SPEAKING = 'stopSpeeking';
    case NAVIGATE_TO_REAL_NAME_AUTH_PAGE = 'navigateToRealNameAuthPage';
    case INIT_FACE_VERIFY = 'initFaceVerify';
    case UNBIND_FACE_VERIFY = 'unbindFaceVerify';
    case CHECK_BLOG_LOGIN = 'checkBlogLogin';
    case OPEN_CHAT = 'openChat';
    case ANSWER_AI = 'answerAI';
    case START_ASR_RECORD = 'startAsrRecord';
    case STOP_ASR_RECORD = 'stopAsrRecord';
    case INIT_CHAT_SPEAKING = 'initChatSpeaking';
    case START_CHAT_SPEAKING = 'startChatSpeaking';
    case STOP_CHAT_SPEAKING = 'stopChatSpeaking';

    // ============== 基础API ==============
    // ------ UI导航 ------
    // case GET_TITLE_COLOR = 'getTitleColor';
    // case HIDE_BACK_HOME = 'hideBackHome';
    // case HIDE_NAVIGATION_BAR_LOADING = 'hideNavigationBarLoading';
    // case SHOW_NAVIGATION_BAR_LOADING = 'showNavigationBarLoading';
    // case SET_NAVIGATION_BAR = 'setNavigationBar';

    // // ------ TabBar ------
    // case HIDE_TAB_BAR = 'hideTabBar';
    // case HIDE_TAB_BAR_RED_DOT = 'hideTabBarRedDot';
    // case REMOVE_TAB_BAR_BADGE = 'removeTabBarBadge';
    // case SET_TAB_BAR_BADGE = 'setTabBarBadge';
    // case SET_TAB_BAR_ITEM = 'setTabBarItem';
    // case SET_TAB_BAR_STYLE = 'setTabBarStyle';
    // case SHOW_TAB_BAR = 'showTabBar';
    // case SHOW_TAB_BAR_RED_DOT = 'showTabBarRedDot';

    // // ------ 路由 ------
    // case SWITCH_TAB = 'switchTab';
    // case RELAUNCH = 'reLaunch';
    // case REDIRECT_TO = 'redirectTo';
    // case NAVIGATE_TO = 'navigateTo';
    // case NAVIGATE_BACK = 'navigateBack';

    // // ------ 交互反馈 ------
    // case ALERT = 'alert';
    // case CONFIRM = 'confirm';
    // case PROMPT = 'prompt';
    // case SHOW_TOAST = 'showToast';
    // case HIDE_TOAST = 'hideToast';
    // case SHOW_LOADING = 'showLoading';
    // case HIDE_LOADING = 'hideLoading';
    // case SHOW_ACTION_SHEET = 'showActionSheet';

    // // ------ 下拉刷新 ------
    // case STOP_PULL_DOWN_REFRESH = 'stopPullDownRefresh';
    // case START_PULL_DOWN_REFRESH = 'startPullDownRefresh';
    // case SET_CAN_PULL_DOWN = 'setCanPullDown';

    // // ------ UI组件 ------
    // case DATE_PICKER = 'datePicker';
    // case CREATE_ANIMATION = 'createAnimation';
    // case CREATE_CANVAS_CONTEXT = 'createCanvasContext';
    // case HIDE_KEYBOARD = 'hideKeyboard';
    // case PAGE_SCROLL_TO = 'pageScrollTo';
    // case CREATE_SELECTOR_QUERY = 'createSelectorQuery';
    // case OPTIONS_SELECT = 'optionsSelect';
    // case MULTI_LEVEL_SELECT = 'multiLevelSelect';
    // case SET_BACKGROUND_COLOR = 'setBackgroundColor';
    // case SET_BACKGROUND_TEXT_STYLE = 'setBackgroundTextStyle';
    // case SET_OPTION_MENU = 'setOptionMenu';

    // // ------ 网络请求 ------
    // case REQUEST = 'request';
    // case CONNECT_SOCKET = 'connectSocket';
    // case SEND_SOCKET_MESSAGE = 'sendSocketMessage';
    // case CLOSE_SOCKET = 'closeSocket';

    // // ------ 系统信息 ------
    // case CAN_I_USE = 'canIUse';
    // case GET_SYSTEM_INFO = 'getSystemInfo';
    // case GET_SYSTEM_INFO_SYNC = 'getSystemInfoSync';
    // case GET_NETWORK_TYPE = 'getNetworkType';

    // // ------ 分享 ------
    // case HIDE_SHARE_MENU = 'hideShareMenu';
    // case GET_RUN_SCENE = 'getRunScene';
    // case REPORT_ANALYTICS = 'reportAnalytics';

    // // ============== 特权API（需要额外权限） ==============
    // // ------ 文件操作权限 ------
    // case UPLOAD_FILE = 'uploadFile';
    // case DOWNLOAD_FILE = 'downloadFile';
    // case SAVE_FILE = 'saveFile';
    // case GET_FILE_INFO = 'getFileInfo';
    // case GET_SAVED_FILE_INFO = 'getSavedFileInfo';
    // case GET_SAVED_FILE_LIST = 'getSavedFileList';
    // case REMOVE_SAVED_FILE = 'removeSavedFile';

    // // ------ 存储权限 ------
    // case SET_STORAGE = 'setStorage';
    // case SET_STORAGE_SYNC = 'setStorageSync';
    // case GET_STORAGE = 'getStorage';
    // case GET_STORAGE_SYNC = 'getStorageSync';
    // case REMOVE_STORAGE = 'removeStorage';
    // case REMOVE_STORAGE_SYNC = 'removeStorageSync';
    // case CLEAR_STORAGE = 'clearStorage';
    // case CLEAR_STORAGE_SYNC = 'clearStorageSync';
    // case GET_STORAGE_INFO = 'getStorageInfo';
    // case GET_STORAGE_INFO_SYNC = 'getStorageInfoSync';

    // // ------ 位置权限 ------
    // case CHOOSE_LOCATION = 'chooseLocation';
    // case GET_LOCATION = 'getLocation';
    // case OPEN_LOCATION = 'openLocation';
    // case CHOOSE_CITY = 'chooseCity';
    // case SET_LOCATED_CITY = 'setLocatedCity';

    // // ------ 媒体权限 ------
    // case CHOOSE_IMAGE = 'chooseImage';
    // case PREVIEW_IMAGE = 'previewImage';
    // case SAVE_IMAGE = 'saveImage';
    // case COMPRESS_IMAGE = 'compressImage';
    // case GET_IMAGE_INFO = 'getImageInfo';

    // // ------ 设备权限 ------
    // case GET_CLIPBOARD = 'getClipboard';
    // case SET_CLIPBOARD = 'setClipboard';
    // case WATCH_SHAKE = 'watchShake';
    // case VIBRATE = 'vibrate';
    // case VIBRATE_LONG = 'vibrateLong';
    // case VIBRATE_SHORT = 'vibrateShort';
    // case MAKE_PHONE_CALL = 'makePhoneCall';
    // case SET_KEEP_SCREEN_ON = 'setKeepScreenOn';
    // case GET_SCREEN_BRIGHTNESS = 'getScreenBrightness';
    // case SET_SCREEN_BRIGHTNESS = 'setScreenBrightness';

    // // ------ 通讯录权限 ------
    // case CHOOSE_PHONE_CONTACT = 'choosePhoneContact';
    // case ADD_PHONE_CONTACT = 'addPhoneContact';

    // // ------ 扫码权限 ------
    // case SCAN = 'scan';

    // // ------ 小程序跳转权限 ------
    // case NAVIGATE_TO_MINI_PROGRAM = 'navigateToMiniProgram';
    // case NAVIGATE_BACK_MINI_PROGRAM = 'navigateBackMiniProgram';

    // // ------ 安全 ------
    // case RSA = 'rsa';

    // // ------ WebView ------
    // case CREATE_WEB_VIEW_CONTEXT = 'createWebViewContext';

    public function label(): string
    {
        return match ($this) {
            // 系统基础
            self::NONE => '无禁用',
            self::ALL => '所有API',
            self::APPLICATION_LOGIN => '授权登录',
            self::SYSTEM_APPLICATION_LOGIN => '系统应用登录',
            self::SYSTEM_REQUEST => '系统级请求',
            self::CHECK_SESSION => '检查会话状态',
            self::SET_DATA => '设置数据',
            self::SET_SHARE_DATA => '设置分享数据',
            self::SHARE => '分享',
            self::NAVIGATE_TO => '页面跳转',
            self::NAVIGATE_BACK => '页面返回',
            self::SWITCH_TAB => '切换标签页',
            self::TOAST => '提示框',
            self::SHOW_LOADING => '显示加载',
            self::HIDE_LOADING => '隐藏加载',
            self::SHOW_MODAL => '显示模态框',
            self::IMPACT_FEEDBACK => '震动反馈',
            self::VIBRATE => '震动',
            self::ON_KEYBOARD_HEIGHT_CHANGE => '监听键盘高度变化',
            self::OFF_KEYBOARD_HEIGHT_CHANGE => '取消监听键盘高度变化',
            self::GET_SYSTEM_INFO => '获取系统信息',
            self::SET_WEB_VIEW_STYLE => '设置WebView样式',
            self::SET_NAVIGATION_BAR_TITLE => '设置导航栏标题',
            self::SET_NAVIGATION_BAR_COLOR => '设置导航栏颜色',
            self::SET_NAVIGATION_BAR_STYLE => '设置导航栏样式',
            self::SET_NAVIGATION_BAR_RIGHT_ITEMS => '设置导航栏右侧按钮',
            self::ALIPAY => '支付宝支付',
            self::WXPAY => '微信支付',
            self::SAVE_IMAGE_TO_PHOTOS_ALBUM => '保存图片到相册',
            self::OPEN_APP => '打开应用',
            self::SCAN_CODE => '扫码',
            self::SHOW_COMMENT_CONTROL => '显示评论控件',
            self::START_SPEAKING => '开始语音',
            self::STOP_SPEAKING => '停止语音',
            self::NAVIGATE_TO_REAL_NAME_AUTH_PAGE => '跳转实名认证页面',
            self::INIT_FACE_VERIFY => '初始化人脸验证',
            self::UNBIND_FACE_VERIFY => '解绑人脸验证',
            self::CHECK_BLOG_LOGIN => '检查博客登录状态',
            self::OPEN_CHAT => '打开聊天',
            self::ANSWER_AI => 'AI回答',
            self::START_ASR_RECORD => '开始语音识别录音',
            self::STOP_ASR_RECORD => '停止语音识别录音',
            self::INIT_CHAT_SPEAKING => '初始化聊天语音',
            self::START_CHAT_SPEAKING => '开始聊天语音',
            self::STOP_CHAT_SPEAKING => '停止聊天语音',


            // UI导航
            // self::GET_TITLE_COLOR => '获取导航栏背景色',
            // self::HIDE_BACK_HOME => '隐藏标题栏上的"返回首页"图标',
            // self::HIDE_NAVIGATION_BAR_LOADING => '隐藏导航条的加载动画',
            // self::SHOW_NAVIGATION_BAR_LOADING => '显示导航条的加载动画',
            // self::SET_NAVIGATION_BAR => '设置导航栏样式',

            // // TabBar
            // self::HIDE_TAB_BAR => '隐藏标签页',
            // self::HIDE_TAB_BAR_RED_DOT => '隐藏标签页红点',
            // self::REMOVE_TAB_BAR_BADGE => '移除标签页角标',
            // self::SET_TAB_BAR_BADGE => '设置标签页角标',
            // self::SET_TAB_BAR_ITEM => '设置标签页项内容',
            // self::SET_TAB_BAR_STYLE => '设置标签页样式',
            // self::SHOW_TAB_BAR => '显示标签页',
            // self::SHOW_TAB_BAR_RED_DOT => '显示标签页红点',

            // // 路由
            // self::SWITCH_TAB => '切换标签页',
            // self::RELAUNCH => '重启应用',
            // self::REDIRECT_TO => '页面重定向',
            // self::NAVIGATE_TO => '页面跳转',
            // self::NAVIGATE_BACK => '页面返回',

            // // 交互反馈
            // self::ALERT => '警告框',
            // self::CONFIRM => '确认框',
            // self::PROMPT => '输入框',
            // self::SHOW_TOAST => '显示提示',
            // self::HIDE_TOAST => '隐藏提示',
            // self::SHOW_LOADING => '显示加载',
            // self::HIDE_LOADING => '隐藏加载',
            // self::SHOW_ACTION_SHEET => '显示操作菜单',

            // // 下拉刷新
            // self::STOP_PULL_DOWN_REFRESH => '停止下拉刷新',
            // self::START_PULL_DOWN_REFRESH => '开始下拉刷新',
            // self::SET_CAN_PULL_DOWN => '设置下拉刷新',

            // // UI组件
            // self::DATE_PICKER => '日期选择器',
            // self::CREATE_ANIMATION => '创建动画',
            // self::CREATE_CANVAS_CONTEXT => '创建画布上下文',
            // self::HIDE_KEYBOARD => '隐藏键盘',
            // self::PAGE_SCROLL_TO => '页面滚动',
            // self::CREATE_SELECTOR_QUERY => '创建节点查询',
            // self::OPTIONS_SELECT => '选项选择器',
            // self::MULTI_LEVEL_SELECT => '级联选择器',
            // self::SET_BACKGROUND_COLOR => '设置背景色',
            // self::SET_BACKGROUND_TEXT_STYLE => '设置背景文本样式',
            // self::SET_OPTION_MENU => '设置选项菜单',

            // // 网络请求
            // self::REQUEST => '发送请求',
            // self::CONNECT_SOCKET => '连接Socket',
            // self::SEND_SOCKET_MESSAGE => '发送Socket消息',
            // self::CLOSE_SOCKET => '关闭Socket',

            // // 系统信息
            // self::CAN_I_USE => '功能检测',
            // self::GET_SYSTEM_INFO => '获取系统信息',
            // self::GET_SYSTEM_INFO_SYNC => '同步获取系统信息',
            // self::GET_NETWORK_TYPE => '获取网络类型',

            // // 分享
            // self::HIDE_SHARE_MENU => '隐藏分享菜单',
            // self::GET_RUN_SCENE => '获取运行场景',
            // self::REPORT_ANALYTICS => '上报分析数据',

            // // 文件操作权限
            // self::UPLOAD_FILE => '上传文件',
            // self::DOWNLOAD_FILE => '下载文件',
            // self::SAVE_FILE => '保存文件',
            // self::GET_FILE_INFO => '获取文件信息',
            // self::GET_SAVED_FILE_INFO => '获取已保存文件信息',
            // self::GET_SAVED_FILE_LIST => '获取已保存文件列表',
            // self::REMOVE_SAVED_FILE => '删除已保存文件',

            // // 存储权限
            // self::SET_STORAGE => '设置存储',
            // self::SET_STORAGE_SYNC => '同步设置存储',
            // self::GET_STORAGE => '获取存储',
            // self::GET_STORAGE_SYNC => '同步获取存储',
            // self::REMOVE_STORAGE => '移除存储',
            // self::REMOVE_STORAGE_SYNC => '同步移除存储',
            // self::CLEAR_STORAGE => '清除存储',
            // self::CLEAR_STORAGE_SYNC => '同步清除存储',
            // self::GET_STORAGE_INFO => '获取存储信息',
            // self::GET_STORAGE_INFO_SYNC => '同步获取存储信息',

            // // 位置权限
            // self::CHOOSE_LOCATION => '选择位置',
            // self::GET_LOCATION => '获取位置',
            // self::OPEN_LOCATION => '打开位置',
            // self::CHOOSE_CITY => '选择城市',
            // self::SET_LOCATED_CITY => '设置定位城市',

            // // 媒体权限
            // self::CHOOSE_IMAGE => '选择图片',
            // self::PREVIEW_IMAGE => '预览图片',
            // self::SAVE_IMAGE => '保存图片',
            // self::COMPRESS_IMAGE => '压缩图片',
            // self::GET_IMAGE_INFO => '获取图片信息',

            // // 设备权限
            // self::GET_CLIPBOARD => '获取剪贴板',
            // self::SET_CLIPBOARD => '设置剪贴板',
            // self::WATCH_SHAKE => '摇一摇',
            // self::VIBRATE => '振动',
            // self::VIBRATE_LONG => '长振动',
            // self::VIBRATE_SHORT => '短振动',
            // self::MAKE_PHONE_CALL => '拨打电话',
            // self::SET_KEEP_SCREEN_ON => '设置屏幕常亮',
            // self::GET_SCREEN_BRIGHTNESS => '获取屏幕亮度',
            // self::SET_SCREEN_BRIGHTNESS => '设置屏幕亮度',

            // // 通讯录权限
            // self::CHOOSE_PHONE_CONTACT => '选择手机联系人',
            // self::ADD_PHONE_CONTACT => '添加手机联系人',

            // // 扫码权限
            // self::SCAN => '扫码',

            // // 小程序跳转权限
            // self::NAVIGATE_TO_MINI_PROGRAM => '跳转到其他小程序',
            // self::NAVIGATE_BACK_MINI_PROGRAM => '返回上一个小程序',

            // // 安全
            // self::RSA => 'RSA加密',

            // // WebView
            // self::CREATE_WEB_VIEW_CONTEXT => '创建WebView上下文',

            default => '未知API',
        };
    }

    // 获取分组后的API选项
    public static function groupedOptions(): array
    {
        return [
            '基础API' => [
                '系统' => self::systemOptions(),
                'UI导航' => self::navigationOptions(),
                'TabBar' => self::tabBarOptions(),
                '路由' => self::routeOptions(),
                '交互反馈' => self::interactionOptions(),
                'UI组件' => self::uiComponentOptions(),
                '网络请求' => self::networkOptions(),
            ],
            '特权API' => [
                '文件操作' => self::fileOptions(),
                '数据存储' => self::storageOptions(),
                '位置服务' => self::locationOptions(),
                '媒体功能' => self::mediaOptions(),
                '设备功能' => self::deviceOptions(),
                '通讯录' => self::contactOptions(),
                '小程序跳转' => self::miniProgramOptions(),
            ],
        ];
    }

    // 获取所有API选项
    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取系统基础API选项
    public static function systemOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                self::NONE,
                self::ALL,
                self::SYSTEM_APPLICATION_LOGIN,
                self::SYSTEM_REQUEST,
                self::APPLICATION_LOGIN,
                self::CHECK_SESSION,
                self::SET_DATA,
                self::SET_SHARE_DATA,
                self::SHARE,
                self::NAVIGATE_TO,
                self::NAVIGATE_BACK,
                self::SWITCH_TAB,
                self::TOAST,
                self::SHOW_LOADING,
                self::HIDE_LOADING,
                self::SHOW_MODAL,
                self::IMPACT_FEEDBACK,
                self::VIBRATE,
                self::ON_KEYBOARD_HEIGHT_CHANGE,
                self::OFF_KEYBOARD_HEIGHT_CHANGE,
                self::GET_SYSTEM_INFO,
                self::SET_WEB_VIEW_STYLE,
                self::SET_NAVIGATION_BAR_TITLE,
                self::SET_NAVIGATION_BAR_COLOR,
                self::SET_NAVIGATION_BAR_STYLE,
                self::SET_NAVIGATION_BAR_RIGHT_ITEMS,
                self::ALIPAY,
                self::WXPAY,
                self::SAVE_IMAGE_TO_PHOTOS_ALBUM,
                self::OPEN_APP,
                self::SCAN_CODE,
                self::SHOW_COMMENT_CONTROL,
                self::START_SPEAKING,
                self::STOP_SPEAKING,
                self::NAVIGATE_TO_REAL_NAME_AUTH_PAGE,
                self::INIT_FACE_VERIFY,
                self::UNBIND_FACE_VERIFY,
                self::CHECK_BLOG_LOGIN,
                self::OPEN_CHAT,
                self::ANSWER_AI,
                self::START_ASR_RECORD,
                self::STOP_ASR_RECORD,
                self::INIT_CHAT_SPEAKING,
                self::START_CHAT_SPEAKING,
                self::STOP_CHAT_SPEAKING,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取UI导航API选项
    public static function navigationOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::GET_TITLE_COLOR,
                // self::HIDE_BACK_HOME,
                // self::HIDE_NAVIGATION_BAR_LOADING,
                // self::SHOW_NAVIGATION_BAR_LOADING,
                // self::SET_NAVIGATION_BAR,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取TabBar API选项
    public static function tabBarOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::HIDE_TAB_BAR,
                // self::HIDE_TAB_BAR_RED_DOT,
                // self::REMOVE_TAB_BAR_BADGE,
                // self::SET_TAB_BAR_BADGE,
                // self::SET_TAB_BAR_ITEM,
                // self::SET_TAB_BAR_STYLE,
                // self::SHOW_TAB_BAR,
                // self::SHOW_TAB_BAR_RED_DOT,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取路由API选项
    public static function routeOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::SWITCH_TAB,
                // self::RELAUNCH,
                // self::REDIRECT_TO,
                // self::NAVIGATE_TO,
                // self::NAVIGATE_BACK,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取交互反馈API选项
    public static function interactionOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::ALERT,
                // self::CONFIRM,
                // self::PROMPT,
                // self::SHOW_TOAST,
                // self::HIDE_TOAST,
                // self::SHOW_LOADING,
                // self::HIDE_LOADING,
                // self::SHOW_ACTION_SHEET,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取UI组件API选项
    public static function uiComponentOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::DATE_PICKER,
                // self::CREATE_ANIMATION,
                // self::CREATE_CANVAS_CONTEXT,
                // self::HIDE_KEYBOARD,
                // self::PAGE_SCROLL_TO,
                // self::CREATE_SELECTOR_QUERY,
                // self::OPTIONS_SELECT,
                // self::MULTI_LEVEL_SELECT,
                // self::SET_BACKGROUND_COLOR,
                // self::SET_BACKGROUND_TEXT_STYLE,
                // self::SET_OPTION_MENU,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取网络请求API选项
    public static function networkOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::REQUEST,
                // self::CONNECT_SOCKET,
                // self::SEND_SOCKET_MESSAGE,
                // self::CLOSE_SOCKET,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取文件操作API选项
    public static function fileOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::UPLOAD_FILE,
                // self::DOWNLOAD_FILE,
                // self::SAVE_FILE,
                // self::GET_FILE_INFO,
                // self::GET_SAVED_FILE_INFO,
                // self::GET_SAVED_FILE_LIST,
                // self::REMOVE_SAVED_FILE,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取存储API选项
    public static function storageOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::SET_STORAGE,
                // self::SET_STORAGE_SYNC,
                // self::GET_STORAGE,
                // self::GET_STORAGE_SYNC,
                // self::REMOVE_STORAGE,
                // self::REMOVE_STORAGE_SYNC,
                // self::CLEAR_STORAGE,
                // self::CLEAR_STORAGE_SYNC,
                // self::GET_STORAGE_INFO,
                // self::GET_STORAGE_INFO_SYNC,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取位置API选项
    public static function locationOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::CHOOSE_LOCATION,
                // self::GET_LOCATION,
                // self::OPEN_LOCATION,
                // self::CHOOSE_CITY,
                // self::SET_LOCATED_CITY,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取媒体API选项
    public static function mediaOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::CHOOSE_IMAGE,
                // self::PREVIEW_IMAGE,
                // self::SAVE_IMAGE,
                // self::COMPRESS_IMAGE,
                // self::GET_IMAGE_INFO,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取设备API选项
    public static function deviceOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::GET_CLIPBOARD,
                // self::SET_CLIPBOARD,
                // self::WATCH_SHAKE,
                // self::VIBRATE,
                // self::VIBRATE_LONG,
                // self::VIBRATE_SHORT,
                // self::MAKE_PHONE_CALL,
                // self::SET_KEEP_SCREEN_ON,
                // self::GET_SCREEN_BRIGHTNESS,
                // self::SET_SCREEN_BRIGHTNESS,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取通讯录API选项
    public static function contactOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::CHOOSE_PHONE_CONTACT,
                // self::ADD_PHONE_CONTACT,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取小程序API选项
    public static function miniProgramOptions(): array
    {
        return collect(self::cases())
            ->filter(fn($item) => in_array($item, [
                // self::NAVIGATE_TO_MINI_PROGRAM,
                // self::NAVIGATE_BACK_MINI_PROGRAM,
            ]))
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }

    // 获取特权API选项（所有需要额外权限的API）
    public static function privilegeOptions(): array
    {
        return array_merge(self::fileOptions(), self::storageOptions(), self::locationOptions(), self::mediaOptions(), self::deviceOptions(), self::contactOptions(), self::miniProgramOptions());
    }

    // 获取基础API选项（不需要额外权限的API）
    public static function baseOptions(): array
    {
        return array_merge(self::systemOptions(), self::navigationOptions(), self::tabBarOptions(), self::routeOptions(), self::interactionOptions(), self::uiComponentOptions(), self::networkOptions());
    }
}
