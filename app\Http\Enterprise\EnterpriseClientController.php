<?php

namespace App\Http\Enterprise;

use App\Enums\ErrorCodeEnum;
use App\Enums\OAuthJsApiEnum;
use App\Enums\OAuthScopeEnum;
use App\Exceptions\AdminException;
use App\Http\Resources\Admins\ClientResource;
use App\Models\Client;
use App\Models\Enterprise;
use App\Models\Material;
use App\Utils\OAuthCacher;
use App\Utils\Respond;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Rtgm\sm\RtSm2;

class EnterpriseClientController extends EnterpriseBaseController {

    protected const PERMISSION_MAP = [
        'index'     => '企业应用管理.查看列表',
        'show'      => '企业应用管理.查看详情',
        'store'     => '企业应用管理.创建',
        'update'    => '企业应用管理.编辑',
        'destroy'   => '企业应用管理.删除',
        'approve'   => '企业应用管理.审核通过',
        'reject'    => '企业应用管理.审核拒绝',
        'pending'   => '企业应用管理.查看待审核',
    ];

    protected $validationMessages = [
        'icon.required'           => '应用图标不能为空',
        'icon.string'             => '应用图标格式错误',
        'name.required'           => '应用名称不能为空',
        'name.string'             => '应用名称必须是字符串',
        'name.max'                => '应用名称不能超过32个字符',
        'description.string'      => '应用描述必须是字符串',
        'auth_safe_domains.array' => '安全域名数据格式无效',
        'auth_safe_domains.*.url' => '安全域名必须是有效的地址',
        'client_type.required'    => '客户端类型不能为空',
        'client_type.in'          => '无效的客户端类型',
        'is_workspace_client.required' => '是否为工作台应用不能为空',
        'is_workspace_client.boolean' => '是否为工作台应用必须是布尔值',
        'callback_url.url'        => '回调地址必须是有效的URL',
    ];

    public function index(Request $request, $enterpriseId) {
        $enterprise = Enterprise::findOrFail($enterpriseId);

        $query = Client::byEnterprise($enterpriseId)
            ->when($request->input('name'), function ($query, $name) {
                $query->where('name', 'like', "%$name%");
            })
            ->when($request->filled('is_workspace_client'), function ($query) use ($request) {
                $query->where('is_workspace_client', $request->input('is_workspace_client'));
            })
            ->when($request->input('client_type'), function ($query, $client_type) {
                $query->where('client_type', $client_type);
            })
            ->when($request->filled('is_revoked'), function ($query) use ($request) {
                $query->where('is_revoked', $request->input('is_revoked'));
            })
            ->orderBy('is_revoked')
            ->orderByDesc('id')
            ->paginate();

        return Respond::success(ClientResource::collection($query));
    }

    public function store(Request $request, $enterpriseId) {
        $enterprise = Enterprise::findOrFail($enterpriseId);

        $validator = Validator::make($request->all(), [
            'icon'                 => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)->first();
                    if ($value && !$material) {
                        $fail('无效的图像资源');
                    }
                },
            ],
            'name'                 => 'required|string|max:32',
            'description'          => 'nullable|string',
            'auth_safe_domains'    => 'nullable|array',
            'auth_safe_domains.*'  => '',
            'callback_url'         => 'nullable|url',
            'client_type'          => [
                'required',
                Rule::in(array_keys(Client::$clientTypeMap)),
            ],
            'is_workspace_client'  => 'required|boolean',
        ], $this->validationMessages);

        if ($validator->fails()) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, $validator->errors()->first());
        }

        $validated = $validator->validated();

        // 设置图标
        $avatarMaterial = Material::where('uuid', $validated['icon'] ?? '')->first();
        $validated['icon'] = [
            'path'     => $avatarMaterial->path,
            'provider' => $avatarMaterial->provider,
            'uuid'     => $avatarMaterial->uuid,
        ];

        // 设置企业ID
        $validated['enterprise_id'] = $enterpriseId;
        
        // 生成客户端标识
        $validated['client_key'] = Carbon::now()->format('YmdHis') . random_int(11, 99);
        
        // 设置默认值
        $validated['is_revoked'] = Client::IS_REVOKED_NO;
        
        // 根据是否为工作台应用设置默认值
        if ($validated['is_workspace_client']) {
            $validated['allowed_scopes'] = ['admin_base'];
            $validated['show_in_workspace'] = Client::SHOW_IN_WORKSPACE_YES;
        } else {
            $validated['allowed_scopes'] = ['base'];
            $validated['show_in_workspace'] = Client::SHOW_IN_WORKSPACE_NO;
        }
        
        // 设置默认禁用的JSAPI
        $validated['disabled_jsapis'] = [
            OAuthJsApiEnum::SYSTEM_APPLICATION_LOGIN,
            OAuthJsApiEnum::SYSTEM_REQUEST
        ];
        
        // 设置数组字段的默认值
        $arrayFields = [
            'auth_safe_domains',
            'allowed_jsapis',
        ];

        foreach ($arrayFields as $field) {
            $validated[$field] = empty($validated[$field]) ? [] : $validated[$field];
        }

        try {
            DB::beginTransaction();
            
            $client = new Client($validated);

            // 生成SM2密钥对
            $sm2 = new RtSm2();
            [
                $privateKey,
                $publicKey,
            ] = $sm2->generatekey();

            $client->client_access_key = $publicKey;
            $client->client_access_secret = $privateKey;

            // 企业创建的应用默认为待审核状态
            $client->status = Client::AUDIT_STATUS_PENDING;
            $client->created_by = auth('enterprise')->id() ?? auth('enterprise_admin')->id();

            $client->save();
            
            // 预加载客户端缓存
            OAuthCacher::preloadClients([$client->client_key], [$client->id]);
            
            DB::commit();
            
            return Respond::success(ClientResource::make($client));
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '创建应用失败: ' . $e->getMessage());
        }
    }

    public function show($enterpriseId, $id) {
        $enterprise = Enterprise::findOrFail($enterpriseId);

        $client = Client::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);

        return Respond::success(ClientResource::make($client));
    }

    public function update(Request $request, $enterpriseId, $id) {
        $enterprise = Enterprise::findOrFail($enterpriseId);

        $client = Client::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'icon'                 => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)->first();
                    if ($value && !$material) {
                        $fail('无效的图像资源');
                    }
                },
            ],
            'name'                 => 'required|string|max:32',
            'description'          => 'nullable|string',
            'auth_safe_domains'    => 'nullable|array',
            'auth_safe_domains.*'  => '',
            'callback_url'         => 'nullable|url',
        ], $this->validationMessages);

        if ($validator->fails()) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, $validator->errors()->first());
        }

        $validated = $validator->validated();

        // 设置图标
        $avatarMaterial = Material::where('uuid', $validated['icon'] ?? '')->first();
        $validated['icon'] = [
            'path'     => $avatarMaterial->path,
            'provider' => $avatarMaterial->provider,
            'uuid'     => $avatarMaterial->uuid,
        ];

        // 设置数组字段的默认值
        $arrayFields = [
            'auth_safe_domains',
        ];

        foreach ($arrayFields as $field) {
            $validated[$field] = empty($validated[$field]) ? [] : $validated[$field];
        }

        try {
            DB::beginTransaction();
            
            $client->update($validated);
            
            // 清除客户端缓存
            OAuthCacher::forgetClient($client->client_key);
            
            DB::commit();
            
            return Respond::success(ClientResource::make($client));
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '更新应用失败: ' . $e->getMessage());
        }
    }

    public function destroy($enterpriseId, $id) {
        $enterprise = Enterprise::findOrFail($enterpriseId);    

        $client = Client::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);    

        try {
            DB::beginTransaction();
            
            // 清除客户端缓存
            OAuthCacher::forgetClient($client->client_key);
            
            $client->delete();
            
            DB::commit();
            
            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '删除应用失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取待审核的应用列表
     */
    public function pending(Request $request, $enterpriseId) {
        $enterprise = Enterprise::findOrFail($enterpriseId);

        $query = Client::byEnterprise($enterpriseId)
            ->pending()
            ->when($request->input('name'), function ($query, $name) {
                return $query->where('name', 'like', "%{$name}%");
            });

        $clients = $query->orderByDesc('created_at')
            ->paginate($request->get('per_page', 15));

        return Respond::success(ClientResource::collection($clients));
    }

    /**
     * 审核通过应用
     */
    public function approve(Request $request, $enterpriseId, $id) {
        $enterprise = Enterprise::findOrFail($enterpriseId);

        $client = Client::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);

        $validated = $request->validate([
            'remark' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            $reviewBy = auth('admin')->id() ?? auth('enterprise')->id();
            $client->approve($reviewBy, $validated['remark'] ?? null);

            // 预加载客户端缓存
            OAuthCacher::preloadClients([$client->client_key], [$client->id]);

            DB::commit();

            return Respond::success(ClientResource::make($client));
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '审核失败: ' . $e->getMessage());
        }
    }

    /**
     * 审核拒绝应用
     */
    public function reject(Request $request, $enterpriseId, $id) {
        $enterprise = Enterprise::findOrFail($enterpriseId);

        $client = Client::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);

        $validated = $request->validate([
            'remark' => 'required|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            $reviewBy = auth('admin')->id() ?? auth('enterprise')->id();
            $client->reject($reviewBy, $validated['remark']);

            DB::commit();

            return Respond::success(ClientResource::make($client));
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '审核失败: ' . $e->getMessage());
        }
    }

    public function options() {
        return Respond::success([
            'client_type' => Client::$clientTypeMap,
            'is_workspace_client' => Client::$isWorkspaceClientMap,
            'is_revoked' => Client::$revokedMap,
            'audit_status' => Client::$auditStatusMap,
        ]);
    }
}
