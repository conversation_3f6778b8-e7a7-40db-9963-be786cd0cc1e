<?php

namespace App\Services\Push\Contracts;

interface MessageInterface
{
    /**
     * 获取推送标题
     */
    public function getTitle(): string;

    /**
     * 获取推送内容
     */
    public function getContent(): string;

    /**
     * 获取推送目标
     * @return array|string user_uuid或user_uuid数组
     */
    public function getTarget(): array|string;

    /**
     * 获取目标类型
     * 'alias' - 使用别名推送（默认，用于user_uuid）
     * 'device' - 使用设备token推送
     */
    public function getTargetType(): string;

    /**
     * 获取过期时间(秒)
     */
    public function getExpireTime(): int;

    /**
     * 获取额外参数
     */
    public function getExtras(): array;

    /**
     * 获取通知类型
     * notification: 通知栏消息
     * message: 透传消息
     */
    public function getType(): string;

    /**
     * 获取消息ID
     * 用于消息追踪和幂等控制
     */
    public function getMessageId(): ?string;

    /**
     * 获取通知栏消息点击行为
     * go_app: 打开应用
     * go_url: 跳转到URL
     * go_activity: 打开特定的activity
     * go_custom: 用户自定义内容
     */
    public function getAfterOpen(): ?string;

    /**
     * 获取通知栏提示音配置
     */
    public function getSound(): ?string;

    /**
     * 获取角标设置
     * 取值为N: 设置角标为N
     * 取值为+N: 角标+N
     * 取值为-N: 角标-N
     * 空字符串: 清空角标
     */
    public function getBadge(): ?string;

    /**
     * 获取点击打开URL
     * 当after_open为go_url时使用
     */
    public function getUrl(): ?string;

    /**
     * 获取点击打开Activity
     * 当after_open为go_activity时使用
     */
    public function getActivity(): ?string;

    /**
     * 获取自定义消息内容
     * 当display_type为message时使用
     * 或当after_open为go_custom时使用
     */
    public function getCustom(): mixed;

    /**
     * 获取大文本内容
     * 最多120个字符
     */
    public function getBigBody(): ?string;

    /**
     * 获取消息重弹设置
     * 0: 不重弹
     * 1: 重弹
     */
    public function getRePop(): ?int;

    /**
     * 获取通知图标(仅Android)
     * 图片需24*24dp或24*24px
     */
    public function getIcon(): ?string;

    /**
     * 获取通知栏大图标URL
     */
    public function getImg(): ?string;

    /**
     * 获取消息展开大图URL
     * 仅支持小米通道,图片尺寸876*324px
     */
    public function getExpandImage(): ?string;

    /**
     * 获取通知栏样式ID
     */
    public function getBuilderId(): ?int;

    /**
     * 获取消息分类
     * 0: 运营消息
     * 1: 系统消息
     */
    public function getCategory(): ?int;

    /**
     * 设置相关方法
     */
    public function setTitle(string $title): self;
    public function setContent(string $content): self;
    public function setTarget(array|string $target): self;
    public function setTargetType(string $targetType): self;
    public function setExpireTime(int $expireTime): self;
    public function setExtras(array $extras): self;
    public function setType(string $type): self;
    public function setMessageId(string $messageId): self;
    public function setAfterOpen(string $afterOpen): self;
    public function setSound(string $sound): self;
    public function setBadge(string $badge): self;
    public function setUrl(string $url): self;
    public function setActivity(string $activity): self;
    public function setCustom(mixed $custom): self;
    public function setBigBody(string $bigBody): self;
    public function setRePop(int $rePop): self;
    public function setIcon(string $icon): self;
    public function setImg(string $img): self;
    public function setExpandImage(string $expandImage): self;
    public function setBuilderId(int $builderId): self;
    public function setCategory(int $category): self;
}
