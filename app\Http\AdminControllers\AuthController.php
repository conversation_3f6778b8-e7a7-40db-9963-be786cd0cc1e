<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use App\Models\AdminQrLogin;
use App\Models\User;
use App\Utils\Tools;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    protected $validationMessages = [
        'username.required'                      => '用户名不能为空',
        'password.required'                      => '密码不能为空',
        'old_password.required'                  => '原密码不能为空',
        'password.regex'                         => '密码必须包含大小写字母、数字和特殊符号(!@#$%^&*?-_)且最少8位',
        'captcha_scene.required'                 => '验证场景不能为空',
        'captcha_verify_param.required'          => '验证参数不能为空',
        'captcha_verify_param.check_robot_analyze' => '安全验证错误',
    ];

    /**
     * 管理员登录
     *
     * @throws AdminException
     */
    public function login(Request $request)
    {
        $this->validate($request, [
            'captcha_scene' => 'required',
            'captcha_verify_param' => 'required|checkRobotAnalyze',
        ], $this->validationMessages);

        $validated = $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $admin = AdminUser::where('username', $request->username)->orWhere('mobile', $request->username)
            ->first();

        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NOT_FOUND);
        }

        if (!$admin->isActive()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_STATUS_ERROR);
        }

        if (!Hash::check($validated['password'], $admin->password)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_PASSWORD_ERROR);
        }

        // 生成token
        $token = Auth::guard('admin')
            ->fromUser($admin);

        // 更新登录信息
        $admin->update([
            'last_login_ip'   => Tools::getClientIp(),
            'last_login_time' => now(),
        ]);

        return Respond::success([
            'access_token' => $token,
            'token_type'   => 'bearer',
            'expires_in'   => auth('admin')
                ->factory()
                ->getTTL() * 60,
            'admin'        => [
                'uuid'        => $admin->uuid,
                'username'    => $admin->username,
                'true_name'   => $admin->true_name,
                'avatar'      => $admin->display_avatar,
                'roles'       => $admin->getRoleNames(),
                'permissions' => $admin->getAllPermissions()
                    ->pluck('name'),
            ],
        ]);
    }

    public function smsLogin(Request $request)
    {
        // $this->validate($request, [
        //     'captcha_scene' => 'required',
        //     'captcha_verify_param' => 'required|checkRobotAnalyze',
        // ], $this->validationMessages);

        $this->validate($request, [
            'mobile' => 'required|isMobile',
            'code'   => 'required|checkCode:' . $request->input('mobile'),
        ], $this->validationMessages);

        $admin = AdminUser::where('mobile', $request->input('mobile'))->first();

        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NOT_FOUND);
        }

        if (!$admin->isActive()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_STATUS_ERROR);
        }

        // 生成token
        $token = Auth::guard('admin')
            ->fromUser($admin);


        return Respond::success([
            'access_token' => $token,
            'token_type'   => 'bearer',
            'expires_in'   => auth('admin')
                ->factory()
                ->getTTL() * 60,
            'admin'        => [
                'uuid'        => $admin->uuid,
                'username'    => $admin->username,
                'true_name'   => $admin->true_name,
                'avatar'      => $admin->display_avatar,
                'roles'       => $admin->getRoleNames(),
                'permissions' => $admin->getAllPermissions()
                    ->pluck('name'),
            ],
        ]);
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        auth('admin')->logout(true);

        return Respond::success();
    }

    /**
     * 获取当前登录管理员信息
     */
    public function info()
    {
        $admin = auth('admin')->user();

        return Respond::success([
            'id'          => $admin->id,
            'username'    => $admin->username,
            'true_name'   => $admin->true_name,
            'avatar'      => $admin->display_avatar,
            'mobile'      => $admin->mobile,
            'roles'       => $admin->getRoleNames(),
            'permissions' => $admin->getAllPermissions()
                ->pluck('name'),
            'departments' => $admin->departments->map(function ($department) {
                return [
                    'id'        => $department->id,
                    'name'      => $department->name,
                    'is_leader' => $department->pivot->is_leader,
                ];
            }),
        ]);
    }

    /**
     * 刷新token
     */
    public function refresh()
    {
        return Respond::success([
            'access_token' => auth('admin')->refresh(),
            'token_type'   => 'bearer',
            'expires_in'   => auth('admin')
                ->factory()
                ->getTTL() * 60,
        ]);
    }

    /**
     * 修改密码
     *
     * @throws AdminException
     */
    public function changePassword(Request $request)
    {
        $validated = $request->validate([
            'old_password' => [
                'required',
            ],
            'password'     => [
                'required',
                'min:8',
                'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
            ],
        ], [
            'password.regex' => '密码必须包含大小写字母,数字和特殊符号(!@#$%^&*?-_)且最少8位',
        ]);

        $admin = auth('admin')->user();

        if (!Hash::check($validated['old_password'], $admin->password)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_PASSWORD_ERROR);
        }

        $admin->update([
            'password' => Hash::make($validated['password']),
        ]);

        return Respond::success();
    }

    /**
     * 生成扫码登录的二维码数据
     */
    public function generateQrCode(Request $request)
    {
        // 获取客户端信息
        $clientIp = Tools::getClientIp();
        $userAgent = Tools::getUserAgent();
        $deviceId = Tools::getDeviceId();

        // 如果没有设备ID，不返回二维码
        if (!$deviceId) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_DEVICE_ID_REQUIRED);
        }

        // 生成唯一的二维码标识
        $code = Str::random(32);

        // 如果是同一设备，使之前的二维码失效
        AdminQrLogin::where('device_id', $deviceId)
            ->update(['status' => AdminQrLogin::STATUS_EXPIRED]);

        // 创建二维码记录，设置5分钟过期
        $qrLogin = AdminQrLogin::create([
            'code' => $code,
            'status' => AdminQrLogin::STATUS_PENDING,
            'client_ip' => $clientIp,
            'user_agent' => $userAgent,
            'device_id' => $deviceId,
            'expired_at' => now()->addMinutes(5),
        ]);

        // 构建前端需要的二维码数据（APP扫码页面URL）
        $qrCodeUrl = route('qr-confirm', ['code' => $code]);

        return Respond::success([
            'code' => $code,
            'qr_url' => $qrCodeUrl,
            'expired_at' => $qrLogin->expired_at->timestamp,
        ]);
    }

    /**
     * 查询扫码状态
     * 
     * @throws AdminException
     */
    public function checkQrStatus(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string',
        ]);

        $deviceId = Tools::getDeviceId();

        // 如果没有设备ID，不返回二维码
        if (!$deviceId) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_DEVICE_ID_REQUIRED);
        }

        $query = AdminQrLogin::where('code', $validated['code'])->where('device_id', $deviceId);

        $qrLogin = $query->first();

        if (!$qrLogin) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_INVALID);
        }

        // 检查是否过期
        if ($qrLogin->isExpired()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_EXPIRED);
        }

        // 检查状态
        if ($qrLogin->status === AdminQrLogin::STATUS_CONFIRMED) {
            // 如果已确认,返回管理员登录数据并将状态改为已使用
            $admin = AdminUser::where('id', $qrLogin->admin_id)->first();

            // 生成token
            $token = Auth::guard('admin')
                ->fromUser($admin);

            // 更新登录信息
            $admin->update([
                'last_login_ip'   => Tools::getClientIp(),
                'last_login_time' => now(),
            ]);

            // 更新状态为已使用,避免重复使用
            $qrLogin->update(['status' => AdminQrLogin::STATUS_USED]);

            return Respond::success([
                'status' => AdminQrLogin::STATUS_CONFIRMED,
                'status_text' => $qrLogin->status_text,
                'access_token' => $token,
                'token_type'   => 'bearer',
                'expires_in'   => auth('admin')
                    ->factory()
                    ->getTTL() * 60,
                'admin'        => [
                    'uuid'        => $admin->uuid,
                    'username'    => $admin->username,
                    'true_name'   => $admin->true_name,
                    'avatar'      => $admin->display_avatar,
                    'roles'       => $admin->getRoleNames(),
                    'permissions' => $admin->getAllPermissions()
                        ->pluck('name'),
                ],
            ]);
        }

        return Respond::success([
            'status' => $qrLogin->status,
            'status_text' => $qrLogin->status_text,
        ]);
    }

    /**
     * 前端用户APP扫码
     * 
     * @throws AdminException
     */
    public function scanQrCode(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string',
        ]);


        $qrLogin = AdminQrLogin::where('code', $validated['code'])->first();

        if (!$qrLogin) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_INVALID);
        }

        // 检查是否过期
        if ($qrLogin->isExpired()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_EXPIRED);
        }

        // 检查状态是否为未扫码
        if ($qrLogin->status !== AdminQrLogin::STATUS_PENDING) {
            if ($qrLogin->status === AdminQrLogin::STATUS_CONFIRMED) {
                throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_CONFIRMED);
            } elseif ($qrLogin->status === AdminQrLogin::STATUS_CANCELED) {
                throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_CANCELED);
            } else {
                throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_INVALID);
            }
        }

        // 验证前端用户是否已登录
        $user = auth()->guard('api')->user();
        // 查找该用户绑定的管理员账号
        $admin = AdminUser::where('bind_user_uuid', $user->uuid)->first();

        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::OAUTH_USER_NO_ADMIN_BINDING);
        }

        // 检查管理员状态
        if (!$admin->isActive()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_STATUS_ERROR);
        }

        // 更新为已扫码状态
        $qrLogin->update([
            'status' => AdminQrLogin::STATUS_SCANNED,
            'user_uuid' => $user->uuid,
            'admin_id' => $admin->id,
            'scanned_at' => now(),
        ]);

        return Respond::success([
            'code' => $qrLogin->code,
            'nickname' => $admin->true_name,
        ]);
    }

    public function qrConfirmView()
    {
        return view('qrConfirm');
    }

    /**
     * 前端用户确认登录
     * 
     * @throws AdminException
     */
    public function confirmQrLogin(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string',
            'confirm' => 'required',
        ]);

        // 验证前端用户是否已登录
        $user = auth()->guard('api')->user();
        if (!$user) {
            throw new AdminException(ErrorCodeEnum::USER_NOT_LOGIN);
        }

        $qrLogin = AdminQrLogin::where('code', $validated['code'])
            ->where('user_uuid', $user->uuid)
            ->first();

        if (!$qrLogin) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_INVALID);
        }

        // 检查是否过期
        if ($qrLogin->isExpired()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_EXPIRED);
        }

        // 检查是否可以确认
        if (!$qrLogin->canConfirm()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_QR_CODE_INVALID);
        }

        // 根据确认结果更新状态
        if ($validated['confirm']) {
            $qrLogin->update([
                'status' => AdminQrLogin::STATUS_CONFIRMED,
                'confirmed_at' => now(),
            ]);
            return Respond::success(['status' => 'confirmed']);
        } else {
            $qrLogin->update([
                'status' => AdminQrLogin::STATUS_CANCELED,
            ]);
            return Respond::success(['status' => 'canceled']);
        }
    }

    /**
     * 取消扫码登录
     */
    public function cancelQrLogin(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string',
        ]);

        $qrLogin = AdminQrLogin::where('code', $validated['code'])->first();

        if ($qrLogin && $qrLogin->status < AdminQrLogin::STATUS_CONFIRMED) {
            $qrLogin->update([
                'status' => AdminQrLogin::STATUS_CANCELED,
            ]);
        }

        return Respond::success();
    }
}
