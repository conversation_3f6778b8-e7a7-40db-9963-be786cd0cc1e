<?php

namespace App\Http\Resources\Admins;

use App\Enums\PushMessageCategoryEnum;
use App\Enums\PushMessageDeliveryEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class ClientTemplateResource
 *
 * @property-read \App\Models\OauthClientTemplate $resource
 * @mixin \App\Models\OauthClientTemplate
 */
class ClientTemplateResource extends JsonResource
{
    public function toArray(Request $request): array {
        return [
            'id'          => $this->id,
            'status'      => $this->status,
            'status_text' => $this->status_text,
            'template'    => PushTemplateSimpleResource::make($this->whenLoaded('pushTemplate')),
        ];
    }
}
