<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admin_users', function (Blueprint $table) {
            $table->string('short_mobile')->after('mobile')->nullable()->comment('短号');
        });
        Schema::table('admin_departments', function (Blueprint $table) {
            $table->string('phone')->after('name')->nullable()->comment('电话');
        });
        Schema::table('admin_department_admin_user', function (Blueprint $table) {
            $table->dropColumn('duties');
            $table->string('job_title')->after('is_leader')->nullable()->comment('职务');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_users', function (Blueprint $table) {
            $table->dropColumn('short_mobile');
        });
        Schema::table('admin_departments', function (Blueprint $table) {
            $table->dropColumn('phone');
        });
        Schema::table('admin_department_admin_user', function (Blueprint $table) {
            $table->dropColumn('job_title');
            $table->string('duties')->after('is_leader')->nullable()->comment('职务');
        });
    }
};
