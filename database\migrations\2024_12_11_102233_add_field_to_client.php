<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->string('client_access_key')
                  ->after('client_key');
            $table->renameColumn('client_secret', 'client_access_secret');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->dropColumn('client_access_key');
            $table->renameColumn('client_access_secret', 'client_secret');
        });
    }
};
