<?php

namespace App\Http\Enterprise;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use App\Models\EnterpriseAdmin;
use App\Models\User;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class EnterpriseAuthController extends Controller
{
    /**
     * 企业拥有者登录
     */
    public function enterpriseLogin(Request $request)
    {
        $validated = $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        try {
            // 查找企业拥有者
            $admin = AdminUser::where('username', $validated['username'])
                             ->where('type', AdminUser::TYPE_ENTERPRISE)
                             ->where('status', AdminUser::STATUS_ENABLED)
                             ->first();

            if (!$admin || !Hash::check($validated['password'], $admin->password)) {
                throw ValidationException::withMessages([
                    'username' => ['用户名或密码错误'],
                ]);
            }

            // 检查企业状态
            if (!$admin->enterprise || !$admin->enterprise->isActive()) {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '所属企业已被禁用');
            }

            // 生成JWT token
            $token = Auth::guard('enterprise')->login($admin);

            // 更新登录信息
            $admin->update([
                'last_login_ip' => $request->ip(),
                'last_login_time' => now(),
            ]);

            return Respond::success([
                'token' => $token,
                'user' => $admin->load('enterprise'),
                'expires_in' => Auth::guard('enterprise')->factory()->getTTL() * 60,
            ]);
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            if ($e instanceof AdminException) {
                throw $e;
            }
            return Respond::error('登录失败: ' . $e->getMessage());
        }
    }

    /**
     * 企业管理员登录
     */
    public function enterpriseAdminLogin(Request $request)
    {
        $validated = $request->validate([
            'mobile' => 'required|string',
            'password' => 'required|string',
        ]);

        try {
            // 查找用户
            $user = User::where('mobile', $validated['mobile'])
                       ->where('status', User::STATUS_ACTIVE)
                       ->first();

            if (!$user || !Hash::check($validated['password'], $user->password)) {
                throw ValidationException::withMessages([
                    'mobile' => ['手机号或密码错误'],
                ]);
            }

            // 查找企业管理员记录
            $enterpriseAdmin = $user->activeEnterpriseAdmins()->first();
            if (!$enterpriseAdmin) {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '您不是企业管理员');
            }

            // 检查企业状态
            if (!$enterpriseAdmin->enterprise->isActive()) {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '所属企业已被禁用');
            }

            // 生成JWT token
            $token = Auth::guard('enterprise_admin')->login($enterpriseAdmin);

            // 更新用户登录信息
            $user->update([
                'last_login_ip' => $request->ip(),
                'last_login_date' => now(),
            ]);

            return Respond::success([
                'token' => $token,
                'user' => $enterpriseAdmin->load(['enterprise', 'user']),
                'expires_in' => Auth::guard('enterprise_admin')->factory()->getTTL() * 60,
            ]);
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            if ($e instanceof AdminException) {
                throw $e;
            }
            return Respond::error('登录失败: ' . $e->getMessage());
        }
    }

    /**
     * 企业拥有者退出登录
     */
    public function enterpriseLogout()
    {
        try {
            Auth::guard('enterprise')->logout();
            return Respond::success(null, '退出成功');
        } catch (\Exception $e) {
            return Respond::error('退出失败: ' . $e->getMessage());
        }
    }

    /**
     * 企业管理员退出登录
     */
    public function enterpriseAdminLogout()
    {
        try {
            Auth::guard('enterprise_admin')->logout();
            return Respond::success(null, '退出成功');
        } catch (\Exception $e) {
            return Respond::error('退出失败: ' . $e->getMessage());
        }
    }

    /**
     * 刷新企业拥有者token
     */
    public function enterpriseRefresh()
    {
        try {
            $token = Auth::guard('enterprise')->refresh();
            return Respond::success([
                'token' => $token,
                'expires_in' => Auth::guard('enterprise')->factory()->getTTL() * 60,
            ]);
        } catch (\Exception $e) {
            return Respond::error('刷新token失败: ' . $e->getMessage());
        }
    }

    /**
     * 刷新企业管理员token
     */
    public function enterpriseAdminRefresh()
    {
        try {
            $token = Auth::guard('enterprise_admin')->refresh();
            return Respond::success([
                'token' => $token,
                'expires_in' => Auth::guard('enterprise_admin')->factory()->getTTL() * 60,
            ]);
        } catch (\Exception $e) {
            return Respond::error('刷新token失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取企业拥有者信息
     */
    public function enterpriseProfile()
    {
        try {
            $admin = Auth::guard('enterprise')->user();
            return Respond::success($admin->load('enterprise'));
        } catch (\Exception $e) {
            return Respond::error('获取用户信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取企业管理员信息
     */
    public function enterpriseAdminProfile()
    {
        try {
            $enterpriseAdmin = Auth::guard('enterprise_admin')->user();
            return Respond::success($enterpriseAdmin->load(['enterprise', 'user', 'roles']));
        } catch (\Exception $e) {
            return Respond::error('获取用户信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 企业拥有者修改密码
     */
    public function enterpriseChangePassword(Request $request)
    {
        $validated = $request->validate([
            'old_password' => 'required|string',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        try {
            $admin = Auth::guard('enterprise')->user();

            if (!Hash::check($validated['old_password'], $admin->password)) {
                throw ValidationException::withMessages([
                    'old_password' => ['原密码错误'],
                ]);
            }

            $admin->update([
                'password' => Hash::make($validated['new_password']),
            ]);

            return Respond::success(null, '密码修改成功');
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            return Respond::error('修改密码失败: ' . $e->getMessage());
        }
    }

    /**
     * 企业管理员修改密码
     */
    public function enterpriseAdminChangePassword(Request $request)
    {
        $validated = $request->validate([
            'old_password' => 'required|string',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        try {
            $enterpriseAdmin = Auth::guard('enterprise_admin')->user();
            $user = $enterpriseAdmin->user;

            if (!Hash::check($validated['old_password'], $user->password)) {
                throw ValidationException::withMessages([
                    'old_password' => ['原密码错误'],
                ]);
            }

            $user->update([
                'password' => Hash::make($validated['new_password']),
            ]);

            return Respond::success(null, '密码修改成功');
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            return Respond::error('修改密码失败: ' . $e->getMessage());
        }
    }
}
