<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Enums\PushMessageCategoryEnum;
use App\Enums\PushMessageDeliveryEnum;
use App\Exceptions\AdminException;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\PushTemplateResource;
use App\Models\PushTemplate;
use App\Services\PushChannelManager;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class PushTemplateController extends AdminBaseController
{
    protected $validationMessages = [
        'code.required'                       => '模板代码不能为空',
        'code.string'                         => '模板代码必须是字符串',
        'code.max'                            => '模板代码不能超过50个字符',
        'code.unique'                         => '模板代码已存在',
        'name.required'                       => '模板名称不能为空',
        'name.string'                         => '模板名称必须是字符串',
        'name.max'                            => '模板名称不能超过100个字符',
        'title.required'                      => '消息标题不能为空',
        'title.string'                        => '消息标题必须是字符串',
        'title.max'                           => '消息标题不能超过225个字符',
        'content.required'                    => '消息内容不能为空',
        'content.string'                      => '消息内容必须是字符串',
        'category.required'                   => '消息分类不能为空',
        'category.integer'                    => '消息分类必须是整数',
        'category.in'                         => '无效的消息分类',
        'delivery_type.required'              => '推送类型不能为空',
        'delivery_type.integer'               => '推送类型必须是整数',
        'delivery_type.in'                    => '无效的推送类型',
        'allowed_params.required'             => '模板参数不能为空',
        'allowed_params.array'                => '模板参数必须是数组',
        'allowed_params.*.key'                => '模板参数键名不能为空',
        'allowed_params.*.description'        => '模板参数描述不能为空',
        'allowed_extend_params.array'         => '扩展参数必须是数组',
        'allowed_extend_params.*.key'         => '扩展参数键名不能为空',
        'allowed_extend_params.*.description' => '扩展参数描述不能为空',
        'status.required'                     => '状态不能为空',
        'status.integer'                      => '状态必须是整数',
        'status.in'                           => '无效的状态值',
        'is_silent.required'                  => '静默推送设置不能为空',
        'is_silent.integer'                   => '静默推送设置必须是整数',
        'is_silent.in'                        => '无效的静默推送设置',
        'show_toast.required'                 => '通知栏显示设置不能为空',
        'show_toast.integer'                  => '通知栏显示设置必须是整数',
        'show_toast.in'                       => '无效的通知栏显示设置',
        'xiaomi_channel_id.string'            => '小米渠道ID必须是字符串',
        'vivo_category.string'                => 'VIVO分类必须是字符串',
        'oppo_channel_id.string'              => 'OPPO渠道ID必须是字符串',
        'oppo_category.string'                => 'OPPO分类必须是字符串',
        'huawei_channel_category.string'      => '华为渠道分类必须是字符串',
        'huawei_local_category.string'        => '华为本地分类必须是字符串',
        'harmony_channel_category.string'     => '鸿蒙渠道分类必须是字符串',
        'show_client_info.required'           => '是否显示客户端信息不能为空',
        'show_client_info.integer'            => '是否显示客户端信息必须是整数',
        'show_client_info.in'                 => '无效的显示客户端信息设置',
    ];

    protected const PERMISSION_MAP = [
        'index'        => '消息模板.查看列表',
        'store'        => '消息模板.创建',
        'show'         => '消息模板.查看详情',
        'update'       => '消息模板.编辑',
        'destroy'      => '消息模板.删除',
        'toggleStatus' => '消息模板.切换状态',
    ];

    /**
     * 获取模板列表
     */
    public function index(Request $request) {
        $templates = PushTemplate::when($request->get('keyword'), function ($query, $keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%");
            });
        })
                                 ->when($request->get('category'), function ($query, $category) {
                                     $query->where('category', $category);
                                 })
                                 ->when($request->get('status'), function ($query, $status) {
                                     $query->where('status', $status);
                                 })
                                 ->latest()
                                 ->paginate($request->get('per_page', 15));

        return Respond::success(PushTemplateResource::collection($templates));
    }

    /**
     * 获取模板选项数据(用于表单选择)
     */
    public function options() {
        $categories = PushMessageCategoryEnum::options();
        $status = PushTemplate::$statusMap;
        $delivery = PushMessageDeliveryEnum::options();
        $isSlient = PushTemplate::$isSilentMap;
        $showToast = PushTemplate::$showToastMap;
        $pushChannelOptions = PushChannelManager::getAllChannelOptions();
        $showClientInfo = PushTemplate::$showClientInfoMap;

        return Respond::success([
            'categories'   => $categories,
            'status'       => $status,
            'delivery'     => $delivery,
            'is_silent'    => $isSlient,
            'show_toast'   => $showToast,
            'push_channel' => $pushChannelOptions,
            'show_client_info' => $showClientInfo,
        ]);
    }

    /**
     * 保存新模板
     */
    public function store(Request $request) {
        $validated = $request->validate([
            'code'                                => 'required|string|max:50|unique:push_templates',
            'name'                                => 'required|string|max:100',
            'title'                               => 'required|string|max:225',
            'content'                             => 'required|string',
            'show_client_info'                    => 'required|integer|in:' . PushTemplate::SHOW_CLIENT_INFO_YES . ',' . PushTemplate::SHOW_CLIENT_INFO_NO,
            'category'                            => 'required|integer|in:' . implode(',', array_keys(PushMessageCategoryEnum::options())),
            'delivery_type'                       => 'required|integer|in:' . implode(',', array_keys(PushMessageDeliveryEnum::options())),
            'allowed_params'                      => 'required|array',
            'allowed_params.*.key'                => 'required|string',
            'allowed_params.*.description'        => 'required|string',
            'allowed_extend_params'               => 'nullable|array',
            'allowed_extend_params.*.key'         => 'required|string',
            'allowed_extend_params.*.description' => 'required|string',
            'status'                              => 'required|integer|in:' . PushTemplate::STATUS_ENABLE . ',' . PushTemplate::STATUS_DISABLE,
            'is_silent'                           => 'required|integer|in:' . PushTemplate::IS_SILENT_YES . ',' . PushTemplate::IS_SILENT_NO,
            'show_toast'                          => 'required|integer|in:' . PushTemplate::SHOW_TOAST_YES . ',' . PushTemplate::SHOW_TOAST_NO,
            'xiaomi_channel_id'                   => 'nullable|string',
            'vivo_category'                       => 'nullable|string',
            'oppo_channel_id'                     => 'nullable|string',
            'oppo_category'                       => 'nullable|string',
            'huawei_channel_category'             => 'nullable|string',
            'huawei_local_category'               => 'nullable|string',
            'harmony_channel_category'            => 'nullable|string',
        ], $this->validationMessages);

        // 格式化参数定义
        $allowedParams = collect($validated['allowed_params'] ?? [])
            ->mapWithKeys(function ($item) {
                return [$item['key'] => $item['description']];
            })
            ->toArray();

        $allowedExtendParams = collect($validated['allowed_extend_params'] ?? [])
            ->mapWithKeys(function ($item) {
                return [$item['key'] => $item['description']];
            })
            ->toArray();

        $template = DB::transaction(function () use ($validated, $allowedParams, $allowedExtendParams) {
            return PushTemplate::create([
                'code'                     => $validated['code'],
                'name'                     => $validated['name'],
                'title'                    => $validated['title'],
                'content'                  => $validated['content'],
                'show_client_info'         => $validated['show_client_info'],
                'category'                 => $validated['category'],
                'delivery_type'            => $validated['delivery_type'],
                'allowed_params'           => $allowedParams,
                'allowed_extend_params'    => $allowedExtendParams,
                'status'                   => $validated['status'],
                'is_silent'                => $validated['is_silent'],
                'show_toast'               => $validated['show_toast'],
                'xiaomi_channel_id'        => $validated['xiaomi_channel_id'] ?? '',
                'vivo_category'            => $validated['vivo_category'] ?? '',
                'oppo_channel_id'          => $validated['oppo_channel_id'] ?? '',
                'oppo_category'            => $validated['oppo_category'] ?? '',
                'huawei_channel_category'  => $validated['huawei_channel_category'] ?? '',
                'huawei_local_category'    => $validated['huawei_local_category'] ?? '',
                'harmony_channel_category' => $validated['harmony_channel_category'] ?? '',
            ]);
        });

        return Respond::success(PushTemplateResource::make($template));
    }

    /**
     * 获取单个模板详情
     */
    public function show($pushTemplateId) {
        $pushTemplate = PushTemplate::where('id', $pushTemplateId)
                                    ->firstOrFail();

        return Respond::success(PushTemplateResource::make($pushTemplate));
    }

    /**
     * 更新模板
     */
    public function update(Request $request, $pushTemplateId) {
        $pushTemplate = PushTemplate::where('id', $pushTemplateId)
                                    ->firstOrFail();

        $validated = $request->validate([
            'code'                                => [
                'required',
                'string',
                'max:50',
                Rule::unique('push_templates')
                    ->ignore($pushTemplate->id),
            ],
            'name'                                => 'required|string|max:100',
            'title'                               => 'required|string|max:225',
            'content'                             => 'required|string',
            'show_client_info'                    => 'required|integer|in:' . PushTemplate::SHOW_CLIENT_INFO_YES . ',' . PushTemplate::SHOW_CLIENT_INFO_NO,
            'category'                            => 'required|integer|in:' . implode(',', array_keys(PushMessageCategoryEnum::options())),
            'delivery_type'                       => 'required|integer|in:' . implode(',', array_keys(PushMessageDeliveryEnum::options())),
            'allowed_params'                      => 'required|array',
            'allowed_params.*.key'                => 'required|string',
            'allowed_params.*.description'        => 'required|string',
            'allowed_extend_params'               => 'nullable|array',
            'allowed_extend_params.*.key'         => 'required|string',
            'allowed_extend_params.*.description' => 'required|string',
            'status'                              => 'required|integer|in:' . PushTemplate::STATUS_ENABLE . ',' . PushTemplate::STATUS_DISABLE,
            'is_silent'                           => 'required|integer|in:' . PushTemplate::IS_SILENT_YES . ',' . PushTemplate::IS_SILENT_NO,
            'show_toast'                          => 'required|integer|in:' . PushTemplate::SHOW_TOAST_YES . ',' . PushTemplate::SHOW_TOAST_NO,
            'xiaomi_channel_id'                   => 'nullable|string',
            'vivo_category'                       => 'nullable|string',
            'oppo_channel_id'                     => 'nullable|string',
            'oppo_category'                       => 'nullable|string',
            'huawei_channel_category'             => 'nullable|string',
            'huawei_local_category'               => 'nullable|string',
            'harmony_channel_category'            => 'nullable|string',
        ], $this->validationMessages);

        // 格式化参数定义
        $allowedParams = collect($validated['allowed_params'] ?? [])
            ->mapWithKeys(function ($item) {
                return [$item['key'] => $item['description']];
            })
            ->toArray();

        $allowedExtendParams = collect($validated['allowed_extend_params'] ?? [])
            ->mapWithKeys(function ($item) {
                return [$item['key'] => $item['description']];
            })
            ->toArray();

        DB::transaction(function () use ($pushTemplate, $validated, $allowedParams, $allowedExtendParams) {
            $pushTemplate->update([
                'code'                     => $validated['code'],
                'name'                     => $validated['name'],
                'title'                    => $validated['title'],
                'content'                  => $validated['content'],
                'show_client_info'         => $validated['show_client_info'],
                'category'                 => $validated['category'],
                'delivery_type'            => $validated['delivery_type'],
                'allowed_params'           => $allowedParams,
                'allowed_extend_params'    => $allowedExtendParams,
                'status'                   => $validated['status'],
                'is_silent'                => $validated['is_silent'],
                'show_toast'               => $validated['show_toast'],
                'xiaomi_channel_id'        => $validated['xiaomi_channel_id'] ?? '',
                'vivo_category'            => $validated['vivo_category'] ?? '',
                'oppo_channel_id'          => $validated['oppo_channel_id'] ?? '',
                'oppo_category'            => $validated['oppo_category'] ?? '',
                'huawei_channel_category'  => $validated['huawei_channel_category'] ?? '',
                'huawei_local_category'    => $validated['huawei_local_category'] ?? '',
                'harmony_channel_category' => $validated['harmony_channel_category'] ?? '',
            ]);
        });

        return Respond::success(PushTemplateResource::make($pushTemplate));
    }

    /**
     * 删除模板
     * @throws AdminException
     */
    public function destroy($pushTemplateId) {
        $pushTemplate = PushTemplate::where('id', $pushTemplateId)
                                    ->firstOrFail();

        // 检查模板是否被使用
        if ($pushTemplate->pushMessages()
                         ->exists() || $pushTemplate->oauthClientTemplates()
                                                    ->exists()) {
            throw new AdminException(ErrorCodeEnum::PUSH_TEMPLATE_HAS_BEEN_USED);
        }

        $pushTemplate->delete();

        return Respond::success();
    }

    /**
     * 切换模板状态
     */
    public function toggleStatus($pushTemplateId) {
        $pushTemplate = PushTemplate::where('id', $pushTemplateId)
                                    ->firstOrFail();

        $pushTemplate->update([
            'status' => $pushTemplate->status === PushTemplate::STATUS_ENABLE ? PushTemplate::STATUS_DISABLE : PushTemplate::STATUS_ENABLE,
        ]);

        return new PushTemplateResource($pushTemplate);
    }
}
