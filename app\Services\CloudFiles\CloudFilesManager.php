<?php

namespace App\Services\CloudFiles;

use Illuminate\Support\Manager;
use InvalidArgumentException;
use App\Services\CloudFiles\Contracts\CloudFilesInterface;
use App\Services\CloudFiles\Providers\Oss;
use App\Services\CloudFiles\Providers\Cos;
use App\Services\CloudFiles\Providers\Obs;
use App\Services\CloudFiles\Providers\Qiniu;

/**
 * 云存储管理器
 */
class CloudFilesManager extends Manager
{
    /**
     * 已注册的自定义驱动
     * @var array
     */
    protected $customProviders = [];

    /**
     * 默认驱动映射
     * @var array
     */
    protected $providerMap = [
        'oss'   => Oss::class,
        'cos'   => Cos::class,
        'obs'   => Obs::class,
        'qiniu' => Qiniu::class,
    ];

    /**
     * 注册自定义驱动
     *
     * @param string $driver 驱动名称
     * @param string|callable $callback 驱动类名或回调函数
     *
     * @return void
     */
    public function extend($driver, $callback): void {
        $this->customProviders[$driver] = $callback;
    }

    /**
     * 创建驱动实例
     *
     * @param string $driver
     *
     * @return CloudFilesInterface
     * @throws InvalidArgumentException
     */
    protected function createDriver($driver): CloudFilesInterface {
        // 检查自定义驱动
        if (isset($this->customProviders[$driver])) {
            return $this->createCustomDriver($driver);
        }

        // 检查默认驱动映射
        if (isset($this->providerMap[$driver])) {
            return $this->createMappedDriver($driver);
        }

        throw new InvalidArgumentException("Driver [$driver] not supported.");
    }

    /**
     * 创建自定义驱动实例
     *
     * @param string $driver
     *
     * @return CloudFilesInterface
     */
    protected function createCustomDriver(string $driver): CloudFilesInterface {
        $provider = $this->customProviders[$driver];

        if (is_callable($provider)) {
            $instance = call_user_func($provider, $this->container);
        } else {
            $instance = new $provider();
        }

        if (!$instance instanceof CloudFilesInterface) {
            throw new InvalidArgumentException("Custom driver must implement CloudFilesInterface.");
        }

        return $this->configureInstance($instance, $driver);
    }

    /**
     * 创建映射的默认驱动实例
     *
     * @param string $driver
     *
     * @return CloudFilesInterface
     */
    protected function createMappedDriver(string $driver): CloudFilesInterface {
        $class = $this->providerMap[$driver];

        return $this->configureInstance(new $class(), $driver);
    }

    /**
     * 配置驱动实例
     *
     * @param CloudFilesInterface $instance
     * @param string $driver
     *
     * @return CloudFilesInterface
     */
    protected function configureInstance(CloudFilesInterface $instance, string $driver): CloudFilesInterface {
        $config = $this->container['config']["cloudfiles.disks.{$driver}"];

        if (empty($config)) {
            throw new InvalidArgumentException("Configuration for driver [{$driver}] not found.");
        }

        return $instance->config($config);
    }

    /**
     * 获取默认驱动名称
     *
     * @return string
     */
    public function getDefaultDriver(): string {
        return $this->container['config']->get('cloudfiles.default', 'oss');
    }

    /**
     * 设置默认驱动
     *
     * @param string $name
     *
     * @return void
     */
    public function setDefaultDriver(string $name): void {
        $this->container['config']->set('cloudfiles.default', $name);
    }

    /**
     * 获取已注册的所有驱动名称
     *
     * @return array
     */
    public function getAvailableDrivers(): array {
        return array_unique(array_merge(array_keys($this->providerMap), array_keys($this->customProviders)));
    }

    /**
     * 根据provider和path获取文件URL
     *
     * @param string $provider 提供商类型：url, oss, cos, obs, qiniu等
     * @param string $path 文件路径
     * @param int|null $expiration 过期时间(秒)，不为空时返回带签名的临时URL
     *
     * @return string
     */
    public function getFileUrl(string $provider, string $path, ?int $expiration = null): string {
        // 如果provider为url，直接返回path
        if (strtolower($provider) === 'url') {
            return $path;
        }

        // 获取对应的云存储驱动实例
        $driver = $this->driver($provider);

        // 创建临时的适配器实例来获取URL
        $adapter = new CloudFilesAdapter($driver, '', null);

        // 根据是否传入过期时间返回对应的URL
        if ($expiration > 0) {
            return $adapter->getTemporaryUrl($path, $expiration);
        }

        return $adapter->getUrl($path);
    }
}
