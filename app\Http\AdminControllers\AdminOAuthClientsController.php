<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\ClientTemplateResource;
use App\Http\Resources\ClientResource;
use App\Models\AdminUser;
use App\Models\Client;
use App\Models\OauthClientSubscription;
use App\Models\OauthClientTemplate;
use App\Models\PushTemplate;
use App\Utils\OAuthCacher;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminOAuthClientsController extends  AdminBaseController
{
    protected $validationMessages = [
        // Client 模板相关
        'client_ids.required'     => '客户端ID不能为空',
        'client_ids.array'        => '客户端ID必须是数组',
        'client_ids.*.exists'     => '客户端不存在',
        'is_owner.required'       => '请选择是否为拥有者',
        'is_owner.boolean'        => '拥有者标识必须是布尔值',

        // 模板相关
        'template_ids.required'   => '模板ID不能为空',
        'template_ids.array'      => '模板ID必须是数组',
        'template_ids.*.required' => '模板ID不能为空',
        'template_ids.*.exists'   => '模板不存在',
        'template_ids.*.distinct' => '模板ID不能重复',
    ];

    protected const PERMISSION_MAP = [
        'assignClients'      => '应用管理.分配管理应用',
        'removeClients'      => '应用管理.移除管理应用',
        'getAdminClients'    => '应用管理.获取管理员管理应用',
        'getClientTemplates' => '应用管理.获取应用模板',
        'assignTemplates'    => '应用管理.分配消息模板',
        'toggleTemplateStatus' => '应用管理.切换消息模板状态',
        'removeTemplate'     => '应用管理.移除消息模板',
    ];

    /**
     * 获取OAuth客户端管理相关选项
     */
    public function options() {
        return Respond::success([
            'client_type'     => Client::$clientTypeMap,
            'template_status' => OauthClientTemplate::$statusMap,
            'scopes'          => Client::availableScopes(),
            'admin_scopes'    => Client::availableAdminScopes(),
            'jsapi'           => Client::availableJsApis(),
            'owner'           => [
                0 => '普通管理员',
                1 => '拥有者',
            ],
        ]);
    }

    /**
     * 为管理员分配OAuth客户端
     *
     * @throws AdminException|\Throwable
     */
    public function assignClients(Request $request, $uuid) {
        $admin = AdminUser::findOrFail($uuid);

        // 检查当前用户是否有权限分配
        //        $currentAdmin = Auth::guard('admin')->user();
        //        if (!$currentAdmin->hasRole('super-admin')) {
        //            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        //        }

        $validated = $request->validate([
            'client_ids'   => 'required|array',
            'client_ids.*' => 'exists:oauth_clients,id',
            'is_owner'     => 'required|boolean',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $clientData = array_fill_keys($validated['client_ids'], ['is_owner' => $validated['is_owner']]);

            // 同步客户端关联
            $admin->oauthClients()
                  ->syncWithoutDetaching($clientData);

            // 清除缓存
            foreach ($validated['client_ids'] as $clientId) {
                OAuthCacher::invalidateClient($clientId);
            }

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 移除管理员的OAuth客户端
     *
     * @throws AdminException|\Throwable
     */
    public function removeClients(Request $request, $uuid) {
        $admin = AdminUser::findOrFail($uuid);

        // 检查当前用户是否有权限移除
        //        $currentAdmin = Auth::guard('admin')->user();
        //        if (!$currentAdmin->hasRole('super-admin')) {
        //            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        //        }

        $validated = $request->validate([
            'client_ids'   => 'required|array',
            'client_ids.*' => 'exists:oauth_clients,id',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            // 移除客户端关联
            $admin->oauthClients()
                  ->detach($validated['client_ids']);

            // 清除缓存
            foreach ($validated['client_ids'] as $clientId) {
                OAuthCacher::invalidateClient($clientId);
            }

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 获取管理员的OAuth客户端列表
     */
    public function getAdminClients($uuid) {
        $admin = AdminUser::findOrFail($uuid);

        $clients = $admin->oauthClients()
                        //  ->with([
                        //      'userBindings' => function ($query) {
                        //          $query->select('client_id')
                        //                ->selectRaw('count(*) as bindings_count')
                        //                ->groupBy('client_id');
                        //      },
                        //  ])
                         ->paginate();

        return Respond::success(ClientResource::collection($clients));
    }


    /**
     * 获取客户端的消息模板列表
     */
    public function getClientTemplates($clientId) {

        $templates = OauthClientTemplate::with(['pushTemplate'])
                                        ->where('oauth_client_id', $clientId)
                                        ->paginate();

        return Respond::success(ClientTemplateResource::collection($templates));
    }

    /**
     * 分配消息模板给客户端
     */
    public function assignTemplates(Request $request, $clientId) {

        $validated = $request->validate([
            'template_ids'   => 'required|array',
            'template_ids.*' => [
                'required',
                'exists:push_templates,id',
                'distinct',
                function ($attribute, $value, $fail) {
                    $template = PushTemplate::find($value);
                    if (!$template || $template->status != PushTemplate::STATUS_ENABLE) {
                        $fail('模板 ' . $value . ' 不可用');
                    }
                },
            ],
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            // 准备模板数据
            $templateData = [];
            foreach ($validated['template_ids'] as $templateId) {
                $templateData[] = [
                    'oauth_client_id'  => $clientId,
                    'push_template_id' => $templateId,
                    'status'           => OauthClientTemplate::STATUS_ENABLE,
                    'created_at'       => now(),
                    'updated_at'       => now(),
                ];
            }

            // 删除旧的关联
            OauthClientTemplate::where('oauth_client_id', $clientId)
                               ->delete();

            // 创建新的关联
            OauthClientTemplate::insert($templateData);

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 切换客户端模板状态
     */
    public function toggleTemplateStatus(Request $request, $clientId, $templateId) {

        $clientTemplate = OauthClientTemplate::where('oauth_client_id', $clientId)
                                             ->where('push_template_id', $templateId)
                                             ->firstOrFail();

        try {
            DB::beginTransaction();

            // 检查模板状态
            $template = PushTemplate::findOrFail($templateId);
            if ($template->status != PushTemplate::STATUS_ENABLE) {
                throw new AdminException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
            }

            // 切换状态
            $clientTemplate->update([
                'status' => $clientTemplate->status == OauthClientTemplate::STATUS_ENABLE ? OauthClientTemplate::STATUS_DISABLE : OauthClientTemplate::STATUS_ENABLE,
            ]);

            DB::commit();

            return Respond::success(ClientTemplateResource::make($clientTemplate));
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 移除客户端模板
     */
    public function removeTemplate($clientId, $templateId) {

        $clientTemplate = OauthClientTemplate::where('oauth_client_id', $clientId)
                                             ->where('push_template_id', $templateId)
                                             ->firstOrFail();

        // 检查是否有订阅使用此模板
        if (OauthClientSubscription::where('oauth_client_id', $clientId)
                                   ->where('push_template_id', $templateId)
                                   ->exists()) {
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [], '该模板正在被订阅使用，无法移除');
        }

        $clientTemplate->delete();

        return Respond::success();
    }
}
