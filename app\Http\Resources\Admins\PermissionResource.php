<?php
namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Permission Resource
 * @property-read \App\Models\Permission $resource
 * @mixin \App\Models\Permission
 */
class PermissionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'guard_name' => $this->guard_name,
            'module' => explode('.', $this->name)[0], // 获取模块名
            'action' => array_slice(explode('.', $this->name), 1), // 获取操作名
        ];
    }
}
