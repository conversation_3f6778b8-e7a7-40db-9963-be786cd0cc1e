<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录确认</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 16px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 40px auto;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .info-box {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: left;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: block;
            object-fit: cover;
            border: 2px solid #eee;
        }
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 25px;
        }
        .btn {
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            border: none;
        }
        .btn-confirm {
            background-color: #1890ff;
            color: white;
        }
        .btn-confirm:hover {
            background-color: #40a9ff;
        }
        .btn-cancel {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #d9d9d9;
        }
        .btn-cancel:hover {
            background-color: #e6e6e6;
        }
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin-top: 16px;
            padding: 12px;
            border-radius: 4px;
        }
        .message.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .message.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(24, 144, 255, 0.3);
            border-radius: 50%;
            border-top-color: #1890ff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        #contentBox {
            display: none;
        }
        #loadingBox {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
    </style>
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
    <script>
        var vConsole = new window.VConsole();
    </script> -->
</head>
<body>
<div class="container">
    <!-- 环境检测加载界面 -->
    <div id="loadingBox">
        <div class="loading"></div>
        <span>环境检测中，请稍候...</span>
    </div>
    
    <!-- 操作界面（初始隐藏） -->
    <div id="contentBox">
        <h1>管理员登录确认</h1>
        <p>您正在使用扫码登录管理后台，请确认是否继续？</p>
        
        <div class="info-box">
            <!-- <div class="info-item">
                <img id="userAvatar" src="" alt="用户头像" class="avatar">
                <div style="text-align: center; margin-bottom: 15px;">
                    <span class="info-label">用户昵称：</span>
                    <span id="userNickname"></span>
                </div>
            </div> -->
            <div class="info-item">
                <span class="info-label">登录账号：</span>
                <span id="adminUsername"></span>
            </div>
            <!-- <div class="info-item">
                <span class="info-label">管理员姓名：</span>
                <span id="adminTrueName"></span>
            </div> -->
            <div class="info-item">
                <span class="info-label">登录时间：</span>
                <span id="loginTime"></span>
            </div>
        </div>
        
        <div class="btn-group">
            <button id="confirmButton" class="btn btn-confirm" disabled>确认登录</button>
            <button id="cancelButton" class="btn btn-cancel" disabled>取消登录</button>
        </div>
    </div>
    
    <div id="messageBox" style="display: none;" class="message"></div>
</div>

<script>
    // 获取URL参数
    function getQueryParam(name) {
        var urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // 环境检查
    function isCGAppWeb() {
        var ua = window.navigator.userAgent.toLowerCase();
        return ua.indexOf('changguan') !== -1;
    }

    // 显示消息
    function showMessage(text, type) {
        var messageBox = document.getElementById('messageBox');
        messageBox.textContent = text;
        messageBox.className = 'message ' + type;
        messageBox.style.display = 'block';
    }

    // 显示内容区域
    function showContent() {
        document.getElementById('loadingBox').style.display = 'none';
        document.getElementById('contentBox').style.display = 'block';
    }

    // 格式化日期时间
    function formatDateTime(date) {
        var year = date.getFullYear();
        var month = (date.getMonth() + 1).toString().padStart(2, '0');
        var day = date.getDate().toString().padStart(2, '0');
        var hours = date.getHours().toString().padStart(2, '0');
        var minutes = date.getMinutes().toString().padStart(2, '0');
        var seconds = date.getSeconds().toString().padStart(2, '0');
        
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }

    // CGApp就绪检查
    function CGAppWebReady() {
        return new Promise(function(resolve, reject) {
            if (isCGAppWeb()) {
                if (window.cgapp) {
                    resolve();
                } else {
                    var timeoutId = setTimeout(function() {
                        reject(new Error('常观App环境加载超时'));
                    }, 5000);
                    
                    document.addEventListener(
                        'cgappjsbridgeready',
                        function() {
                            clearTimeout(timeoutId);
                            resolve();
                        },
                        false
                    );
                }
            } else {
                reject(new Error('请在常观App内打开页面'));
            }
        });
    }

    // 获取二维码信息
    function getQrCodeInfo(code) {
        return new Promise(function(resolve, reject) {
            cgapp.systemRequest({
                client_id: 'default',
                method: 'POST',
                uri: "{{ url('/api/auth-qr/scan') }}",
                params: {
                    code: code
                },
                success: function(res) {
                    if (res.statusCode === 200) {
                        resolve(res.data.data);
                    } else {
                        // 处理错误码 160014，显示快捷确认按钮
                        if (res.data.errcode === 160014) {
                            showQuickConfirmButton("{{ url('/workspace/quick-bind') }}");
                            reject(new Error(res.data.errmsg || '获取二维码信息失败'));
                        } else {
                            reject(new Error(res.data.errmsg || '获取二维码信息失败'));
                        }
                    }
                },
                fail: function(error) {
                    reject(new Error(error.errmsg || '请求失败，请重试'));
                }
            });
        });
    }

    // 显示快捷确认按钮
    function showQuickConfirmButton(quickUrl) {
        // 隐藏加载框
        document.getElementById('loadingBox').style.display = 'none';
        
        // 创建快捷确认按钮
        var quickConfirmBox = document.createElement('div');
        quickConfirmBox.style.textAlign = 'center';
        quickConfirmBox.style.margin = '20px 0';
        
        var quickButton = document.createElement('button');
        quickButton.className = 'btn btn-confirm';
        quickButton.textContent = '快捷确认绑定';
        quickButton.addEventListener('click', function() {
            cgapp.navigateTo({
                url: quickUrl,
            });
        });
        
        quickConfirmBox.appendChild(quickButton);
        document.querySelector('.container').appendChild(quickConfirmBox);
    }

    // 确认登录
    function confirmLogin(code) {
        var confirmButton = document.getElementById('confirmButton');
        var cancelButton = document.getElementById('cancelButton');
        
        confirmButton.disabled = true;
        cancelButton.disabled = true;
        
        cgapp.systemRequest({
            client_id: 'default',
            method: 'POST',
            uri: "{{ url('/api/auth-qr/confirm') }}",
            params: {
                code: code,
                confirm: 1
            },
            success: function(res) {
                if (res.statusCode === 200) {
                    showMessage('登录确认成功！', 'success');
                    setTimeout(function() {
                        // cgapp.closeWebView();
                        cgapp.navigateBack();
                    }, 2000);
                } else {
                    showMessage(res.data.errmsg || '确认失败，请重试', 'error');
                    confirmButton.disabled = false;
                    cancelButton.disabled = false;
                }
            },
            fail: function(error) {
                showMessage(error.errmsg || '请求失败，请重试', 'error');
                confirmButton.disabled = false;
                cancelButton.disabled = false;
            }
        });
    }

    // 取消登录
    function cancelLogin(code) {
        var confirmButton = document.getElementById('confirmButton');
        var cancelButton = document.getElementById('cancelButton');
        
        confirmButton.disabled = true;
        cancelButton.disabled = true;
        
        cgapp.systemRequest({
            client_id: 'default',
            method: 'POST',
            uri: "{{ url('/api/auth-qr/confirm') }}",
            params: {
                code: code,
                confirm: 0
            },
            success: function(res) {
                if (res.statusCode === 200) {
                    showMessage('已取消登录！', 'success');
                    setTimeout(function() {
                        // cgapp.closeWebView();
                        cgapp.navigateBack();
                    }, 2000);
                } else {
                    showMessage(res.data.errmsg || '操作失败，请重试', 'error');
                    confirmButton.disabled = false;
                    cancelButton.disabled = false;
                }
            },
            fail: function(error) {
                showMessage(error.errmsg || '请求失败，请重试', 'error');
                confirmButton.disabled = false;
                cancelButton.disabled = false;
            }
        });
    }

    // 页面初始化
    function initPage() {
        var code = getQueryParam('code');
        if (!code) {
            showMessage('缺少必要的二维码参数', 'error');
            return;
        }
        
        CGAppWebReady()
            .then(function() {
                cgapp.config({client_id:"default"});
                return getQrCodeInfo(code);
            })
            .then(function(data) {
                document.getElementById('adminUsername').textContent = data.nickname || '';
                document.getElementById('loginTime').textContent = formatDateTime(new Date());
                
                showContent();
                
                var confirmButton = document.getElementById('confirmButton');
                var cancelButton = document.getElementById('cancelButton');
                
                confirmButton.disabled = false;
                cancelButton.disabled = false;
                
                confirmButton.addEventListener('click', function() {
                    confirmLogin(code);
                });
                
                cancelButton.addEventListener('click', function() {
                    cancelLogin(code);
                });
            })
            .catch(function(error) {
                document.getElementById('loadingBox').style.display = 'none';
                showMessage(error.message || '初始化失败', 'error');
                
                document.getElementById('confirmButton').disabled = true;
                document.getElementById('cancelButton').disabled = true;
            });
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', initPage);
</script>
</body>
</html> 