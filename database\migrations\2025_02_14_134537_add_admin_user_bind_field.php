<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('admin_users', function (Blueprint $table) {
            $table->uuid('bind_user_uuid')
                  ->after('status')
                  ->nullable()
                  ->comment('绑定前端用户信息');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('admin_users', function (Blueprint $table) {
            $table->dropColumn('bind_user_uuid');
        });
    }
};
