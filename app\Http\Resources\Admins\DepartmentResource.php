<?php

namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Department Resource
 * @property-read \App\Models\AdminDepartment $resource
 * @mixin \App\Models\AdminDepartment
 */
class DepartmentResource extends JsonResource
{
    public function toArray(Request $request): array {
        return [
            'id'           => $this->id,
            'name'         => $this->name,
            'code'         => $this->code,
            'full_name'    => $this->full_name,
            'level'        => $this->level,
            'sort'         => $this->sort,
            'status'       => $this->status,
            'status_text'  => $this->status_text,
            'remark'       => $this->remark,
            'created_at'   => $this->created_at,
            // 管理员关联
            'admins_count' => $this->whenCounted('admins'),
            //            'admins' => AdminSimpleResource::collection($this->whenLoaded('admins')),
            $this->mergeWhen($this->hasPivotLoaded('admin_department_admin_user'), [
                'is_leader' => $this->pivot?->is_leader,
                'job_title'    => $this->pivot?->job_title,
            ]),
            // 领导关联
            'leaders'      => AdminSimpleResource::collection($this->whenLoaded('leaders')),
            'roles'        => RoleResource::collection($this->whenLoaded('roles')),
            'permissions'  => PermissionResource::collection($this->whenLoaded('permissions')),
            'parent'       => DepartmentResource::make($this->whenLoaded('parentDepartment')),
            'enterprise'   => EnterpriseResource::make($this->whenLoaded('enterprise')),
        ];
    }
}
