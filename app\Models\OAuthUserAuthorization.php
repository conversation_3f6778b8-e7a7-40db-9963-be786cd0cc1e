<?php

namespace App\Models;

use App\Models\Traits\HasUser;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $user_uuid
 * @property string|null $user_type 用户类型
 * @property int $client_id
 * @property array<array-key, mixed>|null $granted_scopes
 * @property array<array-key, mixed>|null $granted_admin_scopes 管理员权限范围
 * @property \Illuminate\Support\Carbon $authorized_at
 * @property \Illuminate\Support\Carbon|null $last_used_at
 * @property int $is_revoked
 * @property \Illuminate\Support\Carbon|null $revoked_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Client|null $client
 * @property-read mixed $is_active
 * @property-read mixed $revoked_text
 * @property-read \App\Models\User|null $user
 * @property-read mixed $user_type_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereAuthorizedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereGrantedAdminScopes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereGrantedScopes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereIsRevoked($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereLastUsedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereRevokedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereUserType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserAuthorization whereUserUuid($value)
 * @mixin \Eloquent
 * @mixin IdeHelperOAuthUserAuthorization
 */
class OAuthUserAuthorization extends Model
{
    use HasFactory;
    use HasUser;

    protected $table = 'oauth_user_authorizations';

    protected $fillable = [
        'user_uuid',
        'user_type',
        'client_id',
        'granted_scopes',
        'granted_admin_scopes',
        'authorized_at',
        'last_used_at',
        'is_revoked',
        'revoked_at',
    ];

    protected $casts = [
        'granted_scopes'       => 'json',
        'granted_admin_scopes' => 'json',
        'authorized_at'        => 'datetime',
        'last_used_at'         => 'datetime',
        'revoked_at'           => 'datetime',
    ];

    const IS_REVOKED_NO = 0;
    const IS_REVOKED_YES = 1;

    public static array $revokedMap = [
        self::IS_REVOKED_YES => '是',
        self::IS_REVOKED_NO  => '否',
    ];

    protected function revokedText(): Attribute {
        return Attribute::make(get: fn() => self::$revokedMap[$this->is_revoked]);
    }

//    public function user() {
//        return $this->belongsTo(User::class, 'user_uuid', 'uuid');
//    }

    public function client() {
        return $this->belongsTo(Client::class, 'client_id', 'id');
    }

    public function getIsActiveAttribute() {
        return !$this->is_revoked && ($this->last_used_at >= now()->subDays(180));
    }

    public function revoke() {
        return $this->forceFill([
            'is_revoked' => self::IS_REVOKED_YES,
            'revoked_at' => now(),
        ])
                    ->save();
    }

    public function scopeActive($query) {
        return $query->where('is_revoked', self::IS_REVOKED_NO);
    }
}
