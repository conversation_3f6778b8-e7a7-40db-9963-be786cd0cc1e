<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->renameColumn('redirect', 'auth_safe_domains');
            $table->json('auth_safe_domains')
                  ->nullable()
                  ->comment('安全域名')
                  ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->renameColumn('auth_safe_domains', 'redirect');
            $table->text('redirect')
                  ->comment('跳转地址')
                  ->nullable()
                  ->change();
        });
    }
};
