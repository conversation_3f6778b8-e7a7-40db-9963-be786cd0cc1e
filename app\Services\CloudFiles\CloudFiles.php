<?php

namespace App\Services\CloudFiles;

use App\Services\CloudFiles\Contracts\CloudFilesInterface;
use Illuminate\Support\Arr;
use League\Flysystem\Filesystem;
use Illuminate\Filesystem\FilesystemAdapter as LaravelFilesystemAdapter;

class CloudFiles
{
    /**
     * 驱动映射关系
     *
     * @var array
     */
    protected static $driverMap = [
        'cfoss' => 'oss',
        'cfcos' => 'cos',
        'cfobs' => 'obs',
        'cfqiniu' => 'qiniu'
    ];

    /**
     * 获取驱动映射关系
     *
     * @return array
     */
    public static function getDriverMap(): array
    {
        return static::$driverMap;
    }

    /**
     * 获取实际的驱动名称
     *
     * @param string $driver
     * @return string
     */
    public static function getActualDriver(string $driver): string
    {
        return static::$driverMap[$driver] ?? $driver;
    }

    /**
     * 是否是有效的云存储驱动
     *
     * @param string $driver
     * @return bool
     */
    public static function isValidDriver(string $driver): bool
    {
        return in_array($driver, ['oss', 'cos', 'obs', 'qiniu']);
    }

    /**
     * 从 Laravel Filesystem 配置中解析配置
     *
     * @param array $config
     * @return array
     */
    public static function parseFilesystemConfig(array $config): array
    {
        $driver = Arr::get($config, 'driver');
        $actualDriver = static::getActualDriver($driver);

        if (!static::isValidDriver($actualDriver)) {
            return [];
        }

        return [
            'driver' => $actualDriver,
            'access_key' => Arr::get($config, 'access_key') ?? Arr::get($config, 'key'),
            'secret_key' => Arr::get($config, 'secret_key') ?? Arr::get($config, 'secret'),
            'endpoint' => Arr::get($config, 'endpoint'),
            'bucket' => Arr::get($config, 'bucket'),
            'cdndomain' => Arr::get($config, 'cdn') ?? Arr::get($config, 'url'),
            'ssl' => Arr::get($config, 'ssl', false),
            'debug' => Arr::get($config, 'debug', false),
        ];
    }

    /**
     * 从 filesystem 配置中加载云存储配置
     *
     * @param array $filesystemConfig
     * @return array
     */
    public static function loadCloudFilesConfig(array $filesystemConfig): array
    {
        $cloudConfig = [];
        $hasConfig = false;

        foreach ($filesystemConfig as $disk => $config) {
            if (!isset($config['driver'])) {
                continue;
            }

            $actualDriver = static::getActualDriver($config['driver']);

            if (static::isValidDriver($actualDriver)) {
                $hasConfig = true;
                $cloudConfig["disks.{$actualDriver}"] = static::parseFilesystemConfig($config);
            }
        }

        if ($hasConfig) {
            $cloudConfig['default'] = config('cloudfiles.default', 'oss');
        }

        return $cloudConfig;
    }

    /**
     * 创建 Laravel Filesystem 适配器
     *
     * @param CloudFilesInterface $provider
     * @param array $config
     * @return LaravelFilesystemAdapter
     */
    public static function createFilesystemAdapter($provider, array $config): LaravelFilesystemAdapter
    {
        $adapter = new CloudFilesAdapter(
            $provider,
            Arr::get($config, 'root', ''),
            Arr::get($config, 'cdndomain') ?? Arr::get($config, 'url')
        );

        return new LaravelFilesystemAdapter(
            new Filesystem($adapter, $config),
            $adapter,
            $config
        );
    }

    /**
     * 注册到 Laravel Filesystem
     *
     * @return void
     */
    public static function registerFilesystem(): void
    {
        $filesystem = app('filesystem');

        foreach (static::$driverMap as $customDriver => $actualDriver) {
            $filesystem->extend($customDriver, function($app, $config) use ($actualDriver) {
                $parsedConfig = static::parseFilesystemConfig($config);
                $parsedConfig['driver'] = $actualDriver;
                $client = $app->make('cloudfiles')->driver($actualDriver);
                return static::createFilesystemAdapter($client, $parsedConfig);
            });
        }
    }
}
