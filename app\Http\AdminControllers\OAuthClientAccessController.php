<?php

namespace App\Http\AdminControllers;

use App\Events\AdminPushEvent;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\AdminSimpleResource;
use App\Http\Resources\Admins\ClientResource;
use App\Http\Resources\Admins\DepartmentResource;
use App\Models\AdminDepartment;
use App\Models\AdminUser;
use App\Models\AuthCode;
use App\Models\Client;
use App\Models\OAuthUserAuthorization;
use App\Models\OAuthUserBinding;
use App\Utils\OpenIDGenerater;
use App\Utils\RedirectToBuilder;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OAuthClientAccessController extends AdminBaseController
{
    protected const PERMISSION_MAP = [
        // 'getCurrentAdminAccessibleClients' => '个人资料.可访问应用列表',
        
        'getAdminAccessibleClients'        => '应用访问控制.用户可访问列表',
        'getDepartmentAccessibleClients'   => '应用访问控制.部门可访问应用',
        'getClientAccessibleAdmins'        => '应用访问控制.应用可访问管理员',
        'getClientAccessibleDepartments'   => '应用访问控制.应用可访问部门',
        'assignClientsToAdmin'             => '应用访问控制.分配应用给管理员',
        'assignClientsToDepartment'        => '应用访问控制.分配应用给部门',
        'removeClientFromAdmin'            => '应用访问控制.移除管理员应用',
        'removeClientFromDepartment'       => '应用访问控制.移除部门应用',
        'assignAdminsToClient'             => '应用访问控制.分配管理员给应用',
        'assignDepartmentsToClient'        => '应用访问控制.分配部门给应用',
        'checkCurrentAdminAccess'          => '应用访问控制.检查当前管理员访问权限',
        'getAccessInheritanceMap'          => '应用访问控制.获取访问继承映射',
        'checkAccess'                      => '应用访问控制.检查访问权限',
    ];


    /**
     * 获取当前管理员可访问的OAuth客户端
     */
    public function getCurrentAdminAccessibleClients(Request $request) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        // 使用缓存优化性能
        $clients = $admin->getAllAccessibleOauthClientsWithCache();

        // 为每个客户端生成授权码
        $clientsWithCodes = $clients->filter(function ($client) {
            return $client->show_in_matrix == Client::SHOW_IN_MATRIX_YES;
        })->map(function ($client) use ($admin) {

            $openIdGenerater = new OpenIDGenerater();

            // 确保用户绑定存在
            $userBinding = OAuthUserBinding::firstOrCreate([
                'user_uuid' => $admin->uuid,
                'client_id' => $client->id,
            ], [
                'user_type' => AuthCode::USER_TYPE_ADMIN,
                'open_id'   => $openIdGenerater->generateOpenID($client->id, $admin->uuid), // 理想情况下应使用OpenIDGenerater服务
            ]);

            // 撤销已有的授权码
            AuthCode::where('user_uuid', $admin->uuid)
                    ->where('client_id', $client->id)
                    ->where('user_type', AuthCode::USER_TYPE_ADMIN)
                    ->where('is_revoked', AuthCode::IS_REVOKED_NO)
                    ->update(['is_revoked' => AuthCode::IS_REVOKED_YES]);

            // 创建或更新授权记录
            OAuthUserAuthorization::updateOrCreate([
                'user_uuid' => $admin->uuid,
                'user_type' => AuthCode::USER_TYPE_ADMIN,
                'client_id' => $client->id,
            ], [
                'granted_scopes' => $client->allowed_scopes,
                'authorized_at'  => now(),
                'last_used_at'   => now(),
                'is_revoked'     => OAuthUserAuthorization::IS_REVOKED_NO,
                'revoked_at'     => null,
            ]);

            // 创建授权码，有效期60分钟
            $authCode = AuthCode::create([
                'id'         => Str::random(40),
                'user_uuid'  => $admin->uuid,
                'user_type'  => AuthCode::USER_TYPE_ADMIN,
                'client_id'  => $client->id,
                'scopes'     => $client->allowed_scopes,
                'is_revoked' => AuthCode::IS_REVOKED_NO,
                'expires_at' => now()->addMinutes(60),
            ]);

            // 添加授权码、重定向URL和OpenID到客户端数据
            //            $client->auth_code = $authCode->id;
            $client->default_redirect_url = RedirectToBuilder::addQueryParams($client->default_redirect_url, ['code' => $authCode->id]);

            //            $client->open_id = $userBinding->open_id;

            return $client;
        });

        return Respond::success(ClientResource::collection($clientsWithCodes));
    }

    /**
     * 获取指定管理员可访问的OAuth客户端
     */
    public function getAdminAccessibleClients(Request $request, $adminUuid) {
        $admin = AdminUser::where('uuid', $adminUuid)
                          ->firstOrFail();

        $clients = $admin->getAllAccessibleOauthClients();

        // 区分直接可访问和从部门继承的
        $directClientIds = $admin->accessibleOauthClients()
                                 ->pluck('id')
                                 ->toArray();

        $result = $clients->map(function ($client) use ($directClientIds) {
            $client->is_direct_access = in_array($client->id, $directClientIds);
            $client->is_inherited = !$client->is_direct_access;

            return $client;
        });

        return Respond::success(ClientResource::collection($result));
    }

    /**
     * 获取指定部门可访问的OAuth客户端
     */
    public function getDepartmentAccessibleClients(Request $request, $departmentId) {
        $department = AdminDepartment::findOrFail($departmentId);

        $clients = $department->getAllAccessibleOauthClients();

        // 区分直接可访问和从父部门继承的
        $directClientIds = $department->accessibleOauthClients()
                                      ->pluck('id')
                                      ->toArray();

        $result = $clients->map(function ($client) use ($directClientIds) {
            $client->is_direct_access = in_array($client->id, $directClientIds);
            $client->is_inherited = !$client->is_direct_access;

            return $client;
        });

        return Respond::success(ClientResource::collection($result));
    }

    /**
     * 获取拥有指定客户端访问权限的所有管理员
     */
    public function getClientAccessibleAdmins(Request $request, $clientId) {

        $client = Client::findOrFail($clientId);

        $admins = $client->getAllAccessibleAdmins();

        // 区分直接可访问和从部门继承的
        $directAdminUuids = $client->accessibleAdmins()
                                   ->pluck('uuid')
                                   ->toArray();

        $result = $admins->map(function ($admin) use ($directAdminUuids) {
            $admin->is_direct_access = in_array($admin->uuid, $directAdminUuids);
            $admin->is_inherited = !$admin->is_direct_access;

            return $admin;
        });

        return Respond::success(AdminSimpleResource::collection($result));
    }

    /**
     * 获取拥有指定客户端访问权限的所有部门
     */
    public function getClientAccessibleDepartments(Request $request, $clientId) {
        $client = Client::findOrFail($clientId);

        $departments = $client->accessibleDepartments; // 只获取直接分配的

        return Respond::success(DepartmentResource::collection($departments));
    }

    /**
     * 为管理员分配可访问的OAuth客户端
     */
    public function assignClientsToAdmin(Request $request) {
        $request->validate([
            'admin_uuid'   => 'required|exists:admin_users,uuid',
            'client_ids'   => 'required|array',
            'client_ids.*' => 'exists:oauth_clients,id',
        ]);

        $admin = AdminUser::where('uuid', $request->admin_uuid)
                          ->first();
        $clientIds = $request->client_ids;

        try {
            DB::beginTransaction();

            // 获取之前的客户端ID列表
            $previousClientIds = $admin->accessibleOauthClients()
                                     ->pluck('id')
                                     ->toArray();

            // 同步关联
            $admin->accessibleOauthClients()
                  ->sync($clientIds);

            // 清除缓存
            $admin->clearAccessibleOauthClientsCache();

            // 为新增的客户端创建绑定和OpenID
            $addedClientIds = array_diff($clientIds, $previousClientIds);
            foreach ($addedClientIds as $clientId) {
                // 创建用户绑定和OpenID
                $openIdGenerater = new OpenIDGenerater();
                OAuthUserBinding::firstOrCreate([
                    'user_uuid' => $admin->uuid,
                    'client_id' => $clientId,
                ], [
                    'user_type' => AuthCode::USER_TYPE_ADMIN,
                    'open_id'   => $openIdGenerater->generateOpenID($clientId, $admin->uuid),
                ]);
                
                event(new AdminPushEvent($admin, $clientId, 'access'));
            }

            // 对移除的客户端触发unaccess事件
            $removedClientIds = array_diff($previousClientIds, $clientIds);
            foreach ($removedClientIds as $clientId) {
                event(new AdminPushEvent($admin, $clientId, 'unaccess'));
            }

            DB::commit();
            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error();
        }
    }

    /**
     * 为部门分配可访问的OAuth客户端
     */
    public function assignClientsToDepartment(Request $request) {
        $request->validate([
            'department_id' => 'required|exists:admin_departments,id',
            'client_ids'    => 'required|array',
            'client_ids.*'  => 'exists:oauth_clients,id',
        ]);

        $department = AdminDepartment::findOrFail($request->department_id);
        $clientIds = $request->client_ids;

        try {
            DB::beginTransaction();

            // 获取部门成员之前可访问的客户端ID
            $adminClientMap = [];
            foreach ($department->admins as $admin) {
                $adminClientMap[$admin->uuid] = $admin->getAllAccessibleOauthClients()
                                                    ->pluck('id')
                                                    ->toArray();
            }

            // 同步关联
            $department->accessibleOauthClients()
                      ->sync($clientIds);

            // 清除部门及其所有成员的缓存
            $department->clearAccessibleOauthClientsCache();
            
            // 对每个管理员触发相应的事件
            foreach ($department->admins as $admin) {
                $admin->clearAccessibleOauthClientsCache();
                
                // 获取更新后的可访问客户端
                $newAccessibleClientIds = $admin->getAllAccessibleOauthClients()
                                              ->pluck('id')
                                              ->toArray();
                
                // 计算新增和移除的客户端
                $previousClientIds = $adminClientMap[$admin->uuid] ?? [];
                $addedClientIds = array_diff($newAccessibleClientIds, $previousClientIds);
                
                // 为新增的客户端创建绑定和OpenID
                foreach ($addedClientIds as $clientId) {
                    // 创建用户绑定和OpenID
                    $openIdGenerater = new OpenIDGenerater();
                    OAuthUserBinding::firstOrCreate([
                        'user_uuid' => $admin->uuid,
                        'client_id' => $clientId,
                    ], [
                        'user_type' => AuthCode::USER_TYPE_ADMIN,
                        'open_id'   => $openIdGenerater->generateOpenID($clientId, $admin->uuid),
                    ]);
                    
                    event(new AdminPushEvent($admin, $clientId, 'access'));
                }
                
                // 对于移除的客户端，只有当管理员完全失去访问权限时才触发unaccess事件
                $removedClientIds = array_diff($previousClientIds, $newAccessibleClientIds);
                foreach ($removedClientIds as $clientId) {
                    if (!$admin->canAccessOauthClient($clientId)) {
                        event(new AdminPushEvent($admin, $clientId, 'unaccess'));
                    }
                }
            }

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();

            return Respond::error();
        }
    }

    /**
     * 从管理员移除特定OAuth客户端访问权限
     */
    public function removeClientFromAdmin(Request $request) {

        $request->validate([
            'admin_uuid' => 'required|exists:admin_users,uuid',
            'client_id'  => 'required|exists:oauth_clients,id',
        ]);

        $admin = AdminUser::where('uuid', $request->admin_uuid)
                          ->first();

        try {
            DB::beginTransaction();

            // 移除关联
            $admin->accessibleOauthClients()
                  ->detach($request->client_id);

            // 清除缓存
            $admin->clearAccessibleOauthClientsCache();

            // 触发adminPushEvent
            event(new AdminPushEvent($admin, $request->client_id,'unaccess'));

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();

            return Respond::error();
        }
    }

    /**
     * 从部门移除特定OAuth客户端访问权限
     */
    public function removeClientFromDepartment(Request $request) {
        $request->validate([
            'department_id' => 'required|exists:admin_departments,id',
            'client_id'     => 'required|exists:oauth_clients,id',
        ]);

        $department = AdminDepartment::findOrFail($request->department_id);

        try {
            DB::beginTransaction();

            // 移除关联
            $department->accessibleOauthClients()
                       ->detach($request->client_id);

            // 清除部门及其所有成员的缓存
            $department->clearAccessibleOauthClientsCache();
            
            // 对每个管理员检查并可能触发unaccess事件
            foreach ($department->admins as $admin) {
                $admin->clearAccessibleOauthClientsCache();
                
                // 只有当管理员完全失去访问权限时才触发unaccess事件
                if (!$admin->canAccessOauthClient($request->client_id)) {
                    event(new AdminPushEvent($admin, $request->client_id, 'unaccess'));
                }
            }

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();

            return Respond::error();
        }
    }

    /**
     * 批量为客户端分配可访问的管理员
     */
    public function assignAdminsToClient(Request $request) {
        $request->validate([
            'client_id'     => 'required|exists:oauth_clients,id',
            'admin_uuids'   => 'required|array',
            'admin_uuids.*' => 'exists:admin_users,uuid',
        ]);

        $client = Client::findOrFail($request->client_id);
        $adminUuids = $request->admin_uuids;

        try {
            DB::beginTransaction();

            // 获取之前的管理员UUID列表
            $previousAdminUuids = $client->accessibleAdmins()
                                       ->pluck('uuid')
                                       ->toArray();

            // 同步关联
            $client->accessibleAdmins()
                   ->sync($adminUuids);

            // 为新增的管理员创建用户绑定和OpenID
            $addedAdminUuids = array_diff($adminUuids, $previousAdminUuids);
            foreach ($addedAdminUuids as $uuid) {
                $admin = AdminUser::where('uuid', $uuid)->first();
                if ($admin) {
                    // 创建用户绑定和OpenID
                    $openIdGenerater = new OpenIDGenerater();
                    OAuthUserBinding::firstOrCreate([
                        'user_uuid' => $admin->uuid,
                        'client_id' => $client->id,
                    ], [
                        'user_type' => AuthCode::USER_TYPE_ADMIN,
                        'open_id'   => $openIdGenerater->generateOpenID($client->id, $admin->uuid),
                    ]);
                    
                    // 清除缓存并触发访问事件
                    $admin->clearAccessibleOauthClientsCache();
                    event(new AdminPushEvent($admin, $client->id, 'access'));
                }
            }

            // 清除之前有权限但现在被移除的管理员的缓存
            $removedAdminUuids = array_diff($previousAdminUuids, $adminUuids);
            foreach ($removedAdminUuids as $uuid) {
                $admin = AdminUser::where('uuid', $uuid)->first();
                if ($admin) {
                    $admin->clearAccessibleOauthClientsCache();
                    event(new AdminPushEvent($admin, $client->id, 'unaccess'));
                }
            }

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();

            return Respond::error();
        }
    }

    /**
     * 批量为客户端分配可访问的部门
     */
    public function assignDepartmentsToClient(Request $request) {
        $request->validate([
            'client_id'        => 'required|exists:oauth_clients,id',
            'department_ids'   => 'required|array',
            'department_ids.*' => 'exists:admin_departments,id',
        ]);

        $client = Client::findOrFail($request->client_id);
        $departmentIds = $request->department_ids;

        try {
            DB::beginTransaction();

            // 获取之前分配的部门ID列表
            $previousDepartmentIds = $client->accessibleDepartments()
                                          ->pluck('id')
                                          ->toArray();

            // 获取每个部门下所有管理员的当前访问状态
            $deptAdminClientMap = [];
            $addedDeptIds = array_diff($departmentIds, $previousDepartmentIds);
            $departments = AdminDepartment::whereIn('id', $addedDeptIds)->get();
            foreach ($departments as $department) {
                foreach ($department->admins as $admin) {
                    $deptAdminClientMap[$admin->uuid] = $admin->getAllAccessibleOauthClients()
                                                           ->pluck('id')
                                                           ->toArray();
                }
            }

            // 同步关联
            $client->accessibleDepartments()
                   ->sync($departmentIds);

            // 清除所有相关部门及其成员的缓存
            $allAffectedDepts = AdminDepartment::whereIn('id', array_merge($departmentIds, $previousDepartmentIds))->get();
            foreach ($allAffectedDepts as $department) {
                $department->clearAccessibleOauthClientsCache();
                
                // 处理部门下的每个管理员
                foreach ($department->admins as $admin) {
                    $admin->clearAccessibleOauthClientsCache();
                    
                    // 获取更新后的可访问客户端
                    $newAccessibleClientIds = $admin->getAllAccessibleOauthClients()
                                                  ->pluck('id')
                                                  ->toArray();
                    
                    // 检查客户端访问权限是否发生变化
                    $previousClientIds = $deptAdminClientMap[$admin->uuid] ?? [];
                    $hasClientAccessBefore = in_array($client->id, $previousClientIds);
                    $hasClientAccessAfter = in_array($client->id, $newAccessibleClientIds);
                    
                    // 如果是新获得访问权限
                    if (!$hasClientAccessBefore && $hasClientAccessAfter) {
                        // 创建用户绑定和OpenID
                        $openIdGenerater = new OpenIDGenerater();
                        OAuthUserBinding::firstOrCreate([
                            'user_uuid' => $admin->uuid,
                            'client_id' => $client->id,
                        ], [
                            'user_type' => AuthCode::USER_TYPE_ADMIN,
                            'open_id'   => $openIdGenerater->generateOpenID($client->id, $admin->uuid),
                        ]);
                        
                        event(new AdminPushEvent($admin, $client->id, 'access'));
                    }
                    // 如果完全失去访问权限
                    else if ($hasClientAccessBefore && !$hasClientAccessAfter) {
                        event(new AdminPushEvent($admin, $client->id, 'unaccess'));
                    }
                }
            }

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();

            return Respond::error();
        }
    }

    /**
     * 检查当前登录管理员是否有指定客户端的访问权限
     */
    public function checkCurrentAdminAccess(Request $request) {
        $request->validate([
            'client_id' => 'required|exists:oauth_clients,id',
        ]);
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();
        $hasAccess = $admin->canAccessOauthClient($request->client_id);

        return response()->json([
            'code'    => 0,
            'message' => 'success',
            'data'    => [
                'has_access' => $hasAccess,
            ],
        ]);
    }

    /**
     * 获取访问权限继承图
     * 显示特定管理员或部门的权限继承关系
     */
    public function getAccessInheritanceMap(Request $request) {

        $request->validate([
            'type' => ['required', Rule::in(['admin', 'department'])],
            'id'   => 'required',
        ]);

        $type = $request->type;
        $id = $request->id;

        $result = [];

        if ($type === 'admin') {
            $admin = AdminUser::where('uuid', $id)
                              ->firstOrFail();

            // 获取管理员直接权限
            $directClients = $admin->accessibleOauthClients;
            $result['direct'] = $directClients;

            // 获取从各部门继承的权限
            $inheritedMap = [];
            foreach ($admin->departments as $department) {
                $departmentClients = $department->getAllAccessibleOauthClients();
                $inheritedMap[$department->name] = $departmentClients;
            }
            $result['inherited'] = $inheritedMap;
        } else if ($type === 'department') {
            $department = AdminDepartment::findOrFail($id);

            // 获取部门直接权限
            $directClients = $department->accessibleOauthClients;
            $result['direct'] = $directClients;

            // 获取从上级部门继承的权限
            $inheritedMap = [];
            $parent = $department->parent();
            while ($parent) {
                $parentClients = $parent->accessibleOauthClients;
                if ($parentClients->count() > 0) {
                    $inheritedMap[$parent->name] = $parentClients;
                }
                $parent = $parent->parent();
            }
            $result['inherited'] = $inheritedMap;
        }

        return response()->json([
            'code'    => 0,
            'message' => 'success',
            'data'    => $result,
        ]);
    }


    /**
     * 检查部门或管理员是否对客户端有访问权限
     */
    public function checkAccess(Request $request) {

        $request->validate([
            'type'      => ['required', Rule::in(['admin', 'department'])],
            'id'        => 'required',
            'client_id' => 'required|exists:oauth_clients,id',
        ]);

        $clientId = $request->client_id;
        $hasAccess = false;
        $details = null;

        if ($request->type === 'admin') {
            $admin = AdminUser::where('uuid', $request->id)
                              ->firstOrFail();
            $hasAccess = $admin->canAccessOauthClient($clientId);

            // 如果有访问权限，获取详细信息（直接还是继承）
            if ($hasAccess) {
                $isDirectAccess = $admin->accessibleOauthClients()
                                        ->where('id', $clientId)
                                        ->exists();

                if ($isDirectAccess) {
                    $details = ['access_type' => 'direct'];
                } else {
                    // 查找是通过哪个部门继承的
                    $sourceDepartments = [];
                    foreach ($admin->departments as $department) {
                        if ($department->canAccessOauthClient($clientId)) {
                            $sourceDepartments[] = [
                                'id'               => $department->id,
                                'name'             => $department->name,
                                'is_direct_access' => $department->accessibleOauthClients()
                                                                 ->where('id', $clientId)
                                                                 ->exists(),
                            ];
                        }
                    }
                    $details = [
                        'access_type'        => 'inherited',
                        'source_departments' => $sourceDepartments,
                    ];
                }
            }
        } else if ($request->type === 'department') {
            $department = AdminDepartment::findOrFail($request->id);
            $hasAccess = $department->canAccessOauthClient($clientId);

            // 如果有访问权限，获取详细信息（直接还是继承）
            if ($hasAccess) {
                $isDirectAccess = $department->accessibleOauthClients()
                                             ->where('id', $clientId)
                                             ->exists();

                if ($isDirectAccess) {
                    $details = ['access_type' => 'direct'];
                } else {
                    // 查找是通过哪个上级部门继承的
                    $sourceChain = [];
                    $parent = $department->parent();
                    while ($parent) {
                        if ($parent->canAccessOauthClient($clientId)) {
                            $sourceChain[] = [
                                'id'               => $parent->id,
                                'name'             => $parent->name,
                                'is_direct_access' => $parent->accessibleOauthClients()
                                                             ->where('id', $clientId)
                                                             ->exists(),
                            ];

                            if ($parent->accessibleOauthClients()
                                       ->where('id', $clientId)
                                       ->exists()) {
                                break; // 找到直接授权的上级，停止查找
                            }
                        }
                        $parent = $parent->parent();
                    }
                    $details = [
                        'access_type'  => 'inherited',
                        'source_chain' => $sourceChain,
                    ];
                }
            }
        }

        return response()->json([
            'code'    => 0,
            'message' => 'success',
            'data'    => [
                'has_access' => $hasAccess,
                'details'    => $details,
            ],
        ]);
    }
}
