<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserRevokeEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $userUuid;

    /**
     * Create a new event instance.
     */
    public function __construct($userUuid) {
        $this->userUuid = $userUuid;
    }

    /**
     * @return mixed
     */

    public function getUserUuid() {
        return $this->userUuid;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
