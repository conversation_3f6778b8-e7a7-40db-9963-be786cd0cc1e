<?php

namespace App\Jobs;

use App\Enums\PushMessageDeliveryEnum;
use App\Models\PushMessage;
use App\Models\UserDevice;
use App\Models\UserSubscription;
use App\Services\Push\Contracts\PushManagerInterface;
use App\Services\Push\Exceptions\InvalidConfigurationException;
use App\Services\Push\Exceptions\PushException;
use App\Services\Push\Messages\Message;
use App\Services\Push\PushManager;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProcessPushMessageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $backoff = 60;
    public $deleteWhenMissingModels = true;

    protected PushMessage $pushMessage;
    protected ?array $targetDevices;
    protected ?int $batchNo;

    public function __construct(PushMessage $pushMessage, ?array $targetDevices = null, ?int $batchNo = null) {
        $pushMessage->loadMissing('pushTemplate');
        $this->pushMessage = $pushMessage;
        $this->targetDevices = $targetDevices;
        $this->batchNo = $batchNo;

        $this->queue = 'push';
    }

    public function handle(PushManager $pushManager) {
        try {
            if (!$this->pushMessage->isPending()) {
                Log::info('Push message is not in pending status', [
                    'message_id' => $this->pushMessage->msg_key,
                    'status'     => $this->pushMessage->status,
                ]);

                return;
            }

            $this->pushMessage->update(['status' => PushMessage::STATUS_PROCESSING]);

            $result = match ($this->pushMessage->push_type) {
                PushMessage::PUSH_TYPE_PERSONAL => $this->handlePersonalPush($pushManager),
                PushMessage::PUSH_TYPE_BROADCAST => $this->handleBroadcastPush($pushManager),
                default => throw new \Exception("Unsupported push type: {$this->pushMessage->push_type}")
            };

            $this->updateMessageSuccess($result);

        } catch (\Throwable $e) {
            $this->handleError($e);
            throw $e;
        }
    }

    /**
     * @throws PushException
     * @throws InvalidConfigurationException
     * @throws \Exception
     */
    protected function handlePersonalPush(PushManager $pushManager): array {
        $results = [];

        // 如果指定了具体的设备类型
        if (in_array($this->pushMessage->delivery_type, [
            PushMessageDeliveryEnum::DELIVERY_ANDROID->value,
            PushMessageDeliveryEnum::DELIVERY_IOS->value,
            PushMessageDeliveryEnum::DELIVERY_HARMONY->value,
        ])) {
            $targets = $this->targetDevices ?? $this->getTargetDevices();
            if (empty($targets)) {
                throw new \Exception('No target devices found', 404);
            }

            // 使用设备token推送
            $message = $this->createMessage($targets);
            $message->setTargetType('device');

            $platform = match ($this->pushMessage->delivery_type) {
                PushMessageDeliveryEnum::DELIVERY_ANDROID->value => 'android',
                PushMessageDeliveryEnum::DELIVERY_IOS->value => 'ios',
                PushMessageDeliveryEnum::DELIVERY_HARMONY->value => 'harmony',
                default => throw new \Exception('Invalid platform type')
            };

            $results[] = $pushManager->driver()
                                     ->platform($platform)
                                     ->pushToDevices($message, $targets);

        } else {
            $targets = $this->targetDevices ?? $this->pushMessage->target_id;
            if (empty($targets)) {
                throw new \Exception('No target devices found', 404);
            }
            // 用户级别推送，使用 alias 方式
            $message = $this->createMessage($targets);
            $message->setTargetType('alias');

            // 需要向所有平台发送
            foreach (['android', 'ios'] as $platform) {
                $results[] = $pushManager->driver()
                                         ->platform($platform)
                                         ->pushToUsers($message, $targets);
            }
        }

        return $results;
    }

    /**
     * @throws PushException
     * @throws InvalidConfigurationException
     */
    protected function handleBroadcastPush(PushManager $pushManager): array {
        $results = [];
        $message = $this->createMessage([]);

        // 广播需要分别向每个平台发送
        foreach (['android', 'ios'] as $platform) {
            $results[] = $pushManager->driver()
                                     ->platform($platform)
                                     ->push($message);
        }

        return $results;
    }


    protected function parseTemplateContent($templateContent, array $params) {
        foreach ($params as $key => $value) {
            $placeholder = '#{' . $key . '}#';
            $templateContent = str_replace($placeholder, $value, $templateContent);
        }

        return Str::replace('\n', ' ', $templateContent);
    }

    protected function createMessage(array|string $target): Message {
        $title = $this->parseTemplateContent($this->pushMessage->title, $this->pushMessage->template_params ?? []);
        $content = $this->parseTemplateContent($this->pushMessage->content, $this->pushMessage->template_params ?? []);

        // 构建自定义内容
        $custom = [
            'title'       => $title,
            'content'     => $content,
            'redirect_to' => $this->pushMessage->extend_params['redirect_to'] ?? '',
            'show_toast'  => $this->pushMessage->pushTemplate->show_toast ?? 0,
        ];

        $extras = $this->pushMessage->extend_params['extra'] ?? [];

        // 创建基础消息实例
        $message = new Message($title, $content, $target, $extras);

        // 设置基本属性
        $messageId = $this->pushMessage->msg_key;
        if ($this->batchNo !== null) {
            $messageId .= '_' . $this->batchNo;
        }

        $message->setMessageId($messageId)
                ->setType($this->pushMessage->is_silent ? 'message' : 'notification')
                ->setCustom($custom);

        // 设置过期时间
        if ($this->pushMessage->expired_seconds) {
            $message->setExpireTime($this->pushMessage->expired_seconds);
        }

        // 设置厂商通道属性
        $channelProperties = $this->buildChannelProperties();
        if (!empty($channelProperties)) {
            $message->setChannelProperties($channelProperties);

            if (!empty($channelProperties['huawei_channel_category']) && $this->pushMessage->pushTemplate->huawei_local_category) {
                $message->setLocalProperties(['category' => $this->pushMessage->pushTemplate->huawei_local_category]);
            }
        }

        return $message;
    }

    /**
     * 构建厂商通道属性
     */
    protected function buildChannelProperties(): array {
        $properties = [];
        $template = $this->pushMessage->pushTemplate;

        // 如果存在模板且配置了厂商通道参数
        if ($template) {
            $channelProps = [
                'xiaomi_channel_id',
                'vivo_category',
                'oppo_channel_id',
                'oppo_category',
                'huawei_channel_category',
                'harmony_channel_category',
            ];

            foreach ($channelProps as $prop) {
                if (!empty($template->{$prop})) {
                    $properties[$prop] = $template->{$prop};
                }
            }
        }

        return array_filter($properties);
    }

    protected function getTargetDevices(): array {
        if ($this->pushMessage->oauth_client_subscription_id) {
            return $this->getSubscriberDevices();
        }

        if ($this->pushMessage->target_id) {
            return $this->getUserDevices($this->pushMessage->target_id);
        }

        return [];
    }

    protected function getUserDevices(string $userUuid): array {
        return UserDevice::where('user_uuid', $userUuid)
                         ->where('is_active', UserDevice::IS_ACTIVE_YES)
                         ->where('platform_type', $this->pushMessage->delivery_type)
                         ->pluck($this->getDeviceIdentifier())
                         ->toArray();
    }

    protected function getSubscriberDevices(): array {
        $subscribers = UserSubscription::where('oauth_client_subscription_id', $this->pushMessage->oauth_client_subscription_id)
                                       ->where('status', UserSubscription::STATUS_ENABLE)
                                       ->pluck('user_uuid');

        if ($subscribers->isEmpty()) {
            return [];
        }

        return UserDevice::whereIn('user_uuid', $subscribers)
                         ->where('is_active', UserDevice::IS_ACTIVE_YES)
                         ->where('platform_type', $this->pushMessage->delivery_type)
                         ->pluck($this->getDeviceIdentifier())
                         ->toArray();
    }

    protected function getDeviceIdentifier(): string {
        return $this->shouldUseDeviceToken() ? 'device_token' : 'user_uuid';
    }

    protected function shouldUseDeviceToken(): bool {
        return in_array($this->pushMessage->delivery_type, [
            PushMessageDeliveryEnum::DELIVERY_ANDROID->value,
            PushMessageDeliveryEnum::DELIVERY_IOS->value,
            PushMessageDeliveryEnum::DELIVERY_HARMONY->value,
        ]);
    }

    protected function updateMessageSuccess(array $result): void {
        $this->pushMessage->update([
            'status'    => PushMessage::STATUS_EXECUTED,
            'push_time' => Carbon::now(),
        ]);

        //        Log::info('Push message succeeded', [
        //            'message_id' => $this->pushMessage->id,
        //            'result'     => $result,
        //        ]);
    }

    protected function handleError(\Throwable $e): void {
        $error = [
            'message' => $e->getMessage(),
            'code'    => $e->getCode(),
            'file'    => $e->getFile(),
            'line'    => $e->getLine(),
        ];

        //        Log::error('Push message failed', [
        //            'message_id' => $this->pushMessage->id,
        //            'error'      => $error,
        //        ]);

        $this->pushMessage->update([
            'status'    => PushMessage::STATUS_FAILED,
            'error_msg' => $e->getMessage(),
            'result'    => json_encode($error),
        ]);
    }
}
