<?php

namespace App\Exceptions;

class SmsException extends BaseException
{
    // 发送失败, 发送频繁, 发送次数超限, 验证码错误, 验证码过期, 手机号错误, 未绑定手机号
//    const SEND_FAILED = 130001;
//    const SEND_FREQUENT = 130002;
//    const SEND_LIMIT = 130003;
//    const CODE_ERROR = 130004;
//    const CODE_EXPIRED = 130005;
//    const MOBILE_ERROR = 130006;
//    const MOBILE_NOT_BIND = 130007;
//
//    public static function message($code) {
//        $msgArr = [
//            static::SEND_FAILED => '发送失败',
//            static::SEND_FREQUENT => '发送频繁',
//            static::SEND_LIMIT => '发送次数超限',
//            static::CODE_ERROR => '验证码错误',
//            static::CODE_EXPIRED => '验证码过期',
//            static::MOBILE_ERROR => '手机号错误',
//            static::MOBILE_NOT_BIND => '未绑定手机号',
//        ];
//
//        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
//    }
}
