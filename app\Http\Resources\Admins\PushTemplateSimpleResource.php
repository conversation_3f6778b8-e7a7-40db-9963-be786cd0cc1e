<?php

namespace App\Http\Resources\Admins;

use App\Enums\PushMessageCategoryEnum;
use App\Enums\PushMessageDeliveryEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PushTemplateSimpleResource extends JsonResource
{
    public function toArray(Request $request) {
        return [
            'id'                       => $this->id,
            'code'                     => $this->code,
            'name'                     => $this->name,
            'title'                    => $this->title,
            'content'                  => $this->content,
            'delivery_type_text'       => $this->delivery_type ? PushMessageDeliveryEnum::from($this->delivery_type)
                                                                                        ->label() : '',
            'allowed_params'           => $this->allowed_params,
            'allowed_extend_params'    => $this->allowed_extend_params,
            'status'                   => $this->status,
            'status_text'              => $this->status_text,
            'is_silent'                => $this->is_silent,
            'is_silent_text'           => $this->is_silent_text,
            'show_toast'               => $this->show_toast,
            'show_toast_text'          => $this->show_toast_text,
        ];
    }
}
