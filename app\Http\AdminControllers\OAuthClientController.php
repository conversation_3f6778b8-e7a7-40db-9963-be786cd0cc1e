<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\ClientResource;
use App\Models\Client;
use App\Models\ClientCategory;
use App\Models\Material;
use App\Utils\OAuthCacher;
use App\Utils\Respond;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Rtgm\sm\RtSm2;

class OAuthClientController extends AdminBaseController
{
    protected $validationMessages = [
        'client_key.required'     => '客户端标识不能为空',
        'client_key.numeric'      => '客户端标识必须是数字',
        'client_key.digits'       => '客户端标识必须是16位数字',
        'icon.required'           => '应用图标不能为空',
        'icon.string'             => '应用图标格式错误',
        'name.required'           => '应用名称不能为空',
        'name.string'             => '应用名称必须是字符串',
        'name.max'                => '应用名称不能超过32个字符',
        'provider.required'       => '应用提供商不能为空',
        'provider.string'         => '应用提供商必须是字符串',
        'provider.max'            => '应用提供商不能超过32个字符',
        'description.string'      => '应用描述必须是字符串',
        'auth_safe_domains.array' => '安全域名数据格式无效',
        'auth_safe_domains.*.url' => '安全域名必须是有效的地址',
        'client_type.required'    => '客户端类型不能为空',
        'client_type.in'          => '无效的客户端类型',
        'allowed_scopes.required' => '授权范围不能为空',
        'allowed_scopes.array'    => '授权范围必须是数组',
        'allowed_jsapis.array'    => '允许的JSAPI必须是数组',
        'disabled_jsapis.array'   => '禁用的JSAPI必须是数组',
        'is_revoked.required'     => '撤销状态不能为空',
        'is_revoked.boolean'      => '撤销状态必须是布尔值',
        'is_workspace_client.required' => '是否为工作空间客户端不能为空',
        'is_workspace_client.boolean' => '是否为工作空间客户端必须是布尔值',
        'show_in_workspace.required' => '是否在工作中展示不能为空',
        'show_in_workspace.boolean' => '是否在工作中展示必须是布尔值',

        // resetKeys 方法的验证消息
        'password.required'       => '密码不能为空',
    ];

    protected const PERMISSION_MAP = [
        'index'     => '应用管理.查看列表',
        'show'      => '应用管理.查看详情',
        'store'     => '应用管理.创建',
        'update'    => '应用管理.编辑',
        'revoke'    => '应用管理.撤销',
        'getKeys'   => '应用管理.查看密钥',
        'resetKeys' => '应用管理.重置密钥',
        'statistics' => '应用管理.查看统计',
    ];

    public function index(Request $request)
    {
        $clients = Client::when($request->input('name'), function ($query, $name) {
            $query->where('name', 'like', "%$name%");
        })
            ->when($request->input('enterprise_id'), function ($query, $enterprise_id) {
                $query->where('enterprise_id', $enterprise_id);
            })
            ->when($request->filled('is_workspace_client'), function ($query) use ($request) {
                $query->where('is_workspace_client', $request->input('is_workspace_client'));
            })
            ->when($request->input('provider'), function ($query, $provider) {
                $query->where('provider', 'like', "%$provider%");
            })
            ->when($request->input('client_type'), function ($query, $client_type) {
                $query->where('client_type', $client_type);
            })
            ->when($request->input('client_key'), function ($query, $client_key) {
                $query->where('client_key', $client_key);
            })
            ->when($request->filled('is_revoked'), function ($query) use ($request) {
                $query->where('is_revoked', $request->input('is_revoked'));
            })
            ->orderBy('is_revoked')
            ->orderByDesc('id')
            ->paginate();

        return Respond::success(ClientResource::collection($clients));
    }

    public function workspaceClients(Request $request)
    {
        $clients = Client::where('is_revoked', Client::IS_REVOKED_NO)
            ->where('is_workspace_client', Client::IS_WORKSPACE_CLIENT_YES)
            ->when($request->input('enterprise_id'), function ($query, $enterprise_id) {
                $query->where('enterprise_id', $enterprise_id);
            })->when($request->input('name'), function ($query, $name) {
                $query->where('name', 'like', "%$name%");
            })
            ->paginate();

        return Respond::success(ClientResource::collection($clients));
    }

    public function options()
    {
        return Respond::success([
            'client_type' => Client::$clientTypeMap,
            'system'      => Client::$isSystemMap,
            'revoked'     => Client::$revokedMap,
            'scope'       => Client::availableScopes(),
            'admin_scope' => Client::availableAdminScopes(),
            'jsapi'       => Client::availableJsApis(),
            'is_workspace_client' => Client::$isWorkspaceClientMap,
            'show_in_workspace' => Client::$showInWorkspaceMap,
            'show_in_matrix' => Client::$showInMatrixMap,
        ]);
    }

    /**
     * Store a newly created client.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            //            'client_key'           => 'required|numeric|digits:16',
            'icon'                 => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)
                        ->first();
                    if ($value && !$material) {
                        $fail('无效的图像资源');
                    }
                },
            ],
            'name'                 => 'required|string|max:32',
            'provider'             => 'required|string|max:32',
            'description'          => 'nullable|string',
            'auth_safe_domains'    => 'nullable|array',
            'auth_safe_domains.*'  => '',
            'callback_url'         => 'nullable|url',
            'client_type'          => [
                'required',
                Rule::in(array_keys(Client::$clientTypeMap)),
            ],
            'default_redirect_url' => 'nullable',
            'white_ips'            => 'nullable|array',
            'allowed_scopes'       => 'required|array',
            'allowed_admin_scopes' => 'nullable|array',
            'allowed_jsapis'       => 'nullable|array',
            'disabled_jsapis'      => 'nullable|array',
            'is_revoked'           => 'required|boolean',
            'is_workspace_client'  => 'required|boolean',
            'show_in_workspace'    => 'required|boolean',
            'show_in_matrix'       => 'required|boolean',
            'workspace_redirect_url' => 'nullable|url',
            'category_id'          => 'nullable|exists:' . ClientCategory::class . ',id',
            'sort'                 => 'nullable|integer|min:0',
        ], $this->validationMessages);

        $avatarMaterrial = Material::where('uuid', $validated['icon'] ?? '')
            ->first();
        $validated['icon'] = [
            'path'     => $avatarMaterrial->path,
            'provider' => $avatarMaterrial->provider,
            'uuid'     => $avatarMaterrial->uuid,
        ];

        $validated['client_key'] = Carbon::now()
            ->format('YmdHis') . random_int(11, 99);

        $arrayFields = [
            'auth_safe_domains',
            'white_ips',
            'allowed_scopes',
            'allowed_admin_scopes',
            'allowed_jsapis',
            'disabled_jsapis'
        ];

        foreach ($arrayFields as $field) {
            $validated[$field] = empty($validated[$field]) ? [] : $validated[$field];
        }

        $client = new Client($validated);
        $sm2 = new RtSm2();
        [
            $privateKey,
            $publicKey,
        ] = $sm2->generatekey();

        $client->client_access_key = $publicKey;
        $client->client_access_secret = $privateKey;

        $client->save();

        OAuthCacher::preloadClients([$client->client_key], [$client->id]);

        return Respond::success(ClientResource::make($client));
    }

    /**
     * Display the specified client.
     */
    public function show(Client $client)
    {
        return Respond::success(ClientResource::make($client));
    }

    /**
     * Update the specified client.
     */
    public function update(Request $request, Client $client)
    {
        $validated = $request->validate([
            'icon'                 => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)
                        ->first();
                    if ($value && !$material) {
                        $fail('无效的头像资源');
                    }
                },
            ],
            'name'                 => 'required|string|max:32',
            'provider'             => 'required|string|max:32',
            'description'          => 'nullable|string',
            'auth_safe_domains'    => 'nullable|array',
            'auth_safe_domains.*'  => '',
            'callback_url'         => 'nullable|url',
            'default_redirect_url' => 'nullable',
            'white_ips'            => 'nullable|array',
            'allowed_scopes'       => 'required|array',
            'allowed_admin_scopes' => 'nullable|array',
            'allowed_jsapis'       => 'nullable|array',
            'disabled_jsapis'      => 'nullable|array',
            'is_revoked'           => 'required|boolean',
            // 'is_workspace_client'  => 'required|boolean',
            'show_in_workspace'    => 'required|boolean',
            'show_in_matrix'       => 'required|boolean',
            'workspace_redirect_url' => 'nullable|url',
            'category_id'          => 'nullable|exists:' . ClientCategory::class . ',id',
            'sort'                 => 'nullable|integer|min:0',
        ], $this->validationMessages);

        //        $client->update($validated);

        //        $oldScopes = $client->allowed_scopes;


        $avatarMaterrial = Material::where('uuid', $validated['icon'])
            ->first();
        $validated['icon'] = [
            'path'     => $avatarMaterrial->path,
            'provider' => $avatarMaterrial->provider,
            'uuid'     => $avatarMaterrial->uuid,
        ];

        $arrayFields = [
            'auth_safe_domains',
            'white_ips',
            'allowed_scopes',
            'allowed_admin_scopes',
            'allowed_jsapis',
            'disabled_jsapis'
        ];

        foreach ($arrayFields as $field) {
            $validated[$field] = empty($validated[$field]) ? [] : $validated[$field];
        }

        $client->update($validated);

        //        if ($oldScopes != $client->allowed_scopes) {
        //            // 获取所有相关的用户授权
        //            $authorizations = OAuthUserAuthorization::where('client_id', $client->id)
        //                                                    ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO)
        //                                                    ->get();
        //
        //            foreach ($authorizations as $authorization) {
        //                // 更新每个用户的授权缓存
        //                OAuthCacher::invalidateUserAuthorization($authorization->user_uuid, $client->id);
        //            }
        //        }

        $this->invalidateClientCache($client);

        // 清除所有可能访问此客户端的管理员用户缓存
        $this->clearAdminAccessibleClientsCache($client);

        return Respond::success();
    }

    public function updateSort(Request $request, Client $client)
    {
        $validated = $request->validate([
            'sort' => 'required|integer|min:0',
        ], $this->validationMessages);

        $client->sort = $validated['sort'];
        $client->save();

        return Respond::success();
    }

    /**
     * Revoke the specified client.
     */
    public function revoke(Client $client)
    {
        $client->is_revoked = true;
        $client->save();

        $this->invalidateClientCache($client);
        $this->clearAdminAccessibleClientsCache($client);

        return Respond::success();
    }

    public function getKeys($clientId)
    {
        $client = Client::findOrFail($clientId);

        return Respond::success([
            'client_id'            => $client->client_key,
            'client_access_key'    => $client->client_access_key,
            'client_access_secret' => $client->client_access_secret,
        ]);
    }

    public function resetKeys(Request $request, Client $client)
    {
        $admin = Auth::guard('admin')
            ->user();

        $this->validate($request, [
            'password' => ['required'],
        ], $this->validationMessages);

        if (Hash::check($request->input('password'), $admin->password) === false) {
            throw new AdminException(ErrorCodeEnum::ADMIN_PASSWORD_ERROR);
        }

        try {
            DB::beginTransaction();

            // 生成新的SM2密钥对
            $sm2 = new RtSm2();
            [
                $privateKey,
                $publicKey,
            ] = $sm2->generatekey();

            $client->forceFill([
                'client_access_key'    => $publicKey,
                'client_access_secret' => $privateKey,
            ])
                ->save();

            // 清除缓存
            OAuthCacher::invalidateClient($client->id);
            OAuthCacher::invalidateClientByKey($client->client_key);
            $this->clearAdminAccessibleClientsCache($client);

            DB::commit();

            return Respond::success([
                'client_access_key'    => $publicKey,
                'client_access_secret' => $privateKey,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [$e->getMessage()]);
        }
    }

    /**
     * Get client statistics
     */
    public function statistics(Client $client)
    {
        $stats = [
            'total_users'           => $client->userBindings()
                ->count(),
            'active_authorizations' => $client->userAuthorizations()
                ->active()
                ->count(),
            'total_tokens'          => $client->tokens()
                ->count(),
        ];

        return Respond::success($stats);
    }

    private function invalidateClientCache(Client $client)
    {
        // 清除客户端基本信息缓存
        OAuthCacher::invalidateClient($client->id);
        OAuthCacher::invalidateClientByKey($client->client_key);

        // 清除所有相关的用户绑定缓存
        //        $userBindings = $client->userBindings()->get();
        //        foreach ($userBindings as $binding) {
        //            OAuthCacher::invalidateUserBinding($binding->user_uuid, $client->id);
        //        }
    }

    /**
     * 清除所有可能访问此客户端的管理员用户缓存
     */
    private function clearAdminAccessibleClientsCache(Client $client)
    {
        // 清除直接关联的管理员用户缓存
        $client->accessibleAdmins()->each(function ($admin) {
            $admin->clearAccessibleOauthClientsCache();
        });

        // 清除通过部门关联的管理员用户缓存
        $client->accessibleDepartments()->each(function ($department) {
            $department->admins()->each(function ($admin) {
                $admin->clearAccessibleOauthClientsCache();
            });
        });
    }
}
