<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->json('allowed_admin_scopes')
                  ->after('allowed_scopes')
                  ->nullable()
                  ->comment('允许的管理员权限范围');
        });

        Schema::table('oauth_access_tokens', function (Blueprint $table) {
            $table->json('scopes')
                  ->change();
            $table->json('admin_scopes')
                  ->after('scopes')
                  ->nullable()
                  ->comment('管理员权限范围');
        });

        Schema::table('oauth_auth_codes', function (Blueprint $table) {
            $table->json('scopes')
                  ->change();
            $table->json('admin_scopes')
                  ->after('scopes')
                  ->nullable()
                  ->comment('管理员权限范围');
        });

        Schema::table('oauth_user_authorizations', function (Blueprint $table) {
            $table->json('granted_admin_scopes')
                  ->after('granted_scopes')
                  ->nullable()
                  ->comment('管理员权限范围');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->dropColumn('allowed_admin_scopes');
        });

        Schema::table('oauth_access_tokens', function (Blueprint $table) {
            $table->string('scopes',1000)
                  ->change();
            $table->dropColumn('admin_scopes');
        });

        Schema::table('oauth_auth_codes', function (Blueprint $table) {
            $table->string('scopes',1000)
                  ->change();
            $table->dropColumn('admin_scopes');
        });

        Schema::table('oauth_user_authorizations', function (Blueprint $table) {
            $table->dropColumn('granted_admin_scopes');
        });
    }
};
