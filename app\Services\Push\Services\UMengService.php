<?php

namespace App\Services\Push\Services;

use App\Services\Push\Clients\UMeng\AndroidClient;
use App\Services\Push\Clients\UMeng\HarmonyClient;
use App\Services\Push\Clients\UMeng\IOSClient;
use App\Services\Push\Contracts\ClientInterface;
use App\Services\Push\Contracts\MessageInterface;
use App\Services\Push\Exceptions\PushException;
use Illuminate\Support\Str;

class UMengService extends AbstractPushService
{
    /**
     * 客户端实例映射
     */
    protected array $clients = [];

    /**
     * 默认的 alias type
     */
    protected string $defaultAliasType = 'app';

    /**
     * 单次批量发送的最大数量
     */
    protected const MAX_BATCH_SIZE = 500;

    public function __construct(array $config) {
        parent::__construct($config);

        if (isset($config['alias_type'])) {
            $this->defaultAliasType = $config['alias_type'];
        }
    }

    /**
     * 获取推送客户端
     * @throws PushException
     */
    protected function getClient(): ClientInterface {
        if (!isset($this->clients[$this->platform])) {
            $this->clients[$this->platform] = match ($this->platform) {
                'android' => new AndroidClient($this->config->toArray()),
                'ios' => new IOSClient($this->config->toArray()),
                'harmony' => new HarmonyClient($this->config->toArray()),
                default => throw new PushException("Unsupported platform: {$this->platform}")
            };
        }

        return $this->clients[$this->platform];
    }

    /**
     * 转换消息为友盟格式
     *
     * @throws PushException
     */
    protected function transformMessage(MessageInterface $message): array {
        $messageId = $message->getMessageId();

        // 基础参数
        $params = [
            'timestamp'       => time(),
            'production_mode' => $this->getClient()
                                      ->getConfig($this->platform . '.productionMode'),
            'description'     => Str::substr($message->getContent(), 0, 50),
        ];

        // 设置推送目标
        $params = array_merge($params, $this->buildTarget($message));

        // 设置消息内容
        $params['payload'] = $this->buildPayload($message);

        // 设置消息策略
        $policy = $this->buildPolicy($message);
        if (!empty($policy)) {
            $params['policy'] = $policy;
        }

        // 设置消息分类(可选)
        if ($category = $message->getCategory()) {
            $params['category'] = $category;
        }

        // 只有Android和Harmony平台需要设置厂商通道配置
        if (in_array($this->platform, ['android', 'harmony'])) {
            $channelProperties = $this->buildChannelProperties($message);
            if (!empty($channelProperties)) {
                $params['channel_properties'] = $channelProperties;

                if (!empty($channelProperties['huawei_channel_category']) && $message->getLocalProperties()) {
                    $params['local_properties']['category'] = $message->getLocalProperties()['huawei_local_category'] ?? '';
                }
            }
        }

        return $params;
    }

    /**
     * 构建推送目标参数
     */
    protected function buildTarget(MessageInterface $message): array {
        $target = $message->getTarget();
        $params = [];

        // 空目标视为广播
        if (empty($target)) {
            return ['type' => 'broadcast'];
        }

        if ($message->getTargetType() === 'device') {
            // 设备token推送
            if (is_array($target)) {
                $params['type'] = 'listcast';
                $params['device_tokens'] = implode(',', array_filter($target));
            } else {
                $params['type'] = 'unicast';
                $params['device_tokens'] = $target;
            }
        } else {
            // alias 方式推送
            $params['type'] = 'customizedcast';
            $params['alias_type'] = $this->defaultAliasType;
            $params['alias'] = is_array($target) ? implode(',', array_filter($target)) : $target;
        }

        return $params;
    }

    /**
     * 构建消息内容
     */
    protected function buildPayload(MessageInterface $message): array {
        if ($this->platform === 'ios') {
            return $this->buildIOSPayload($message);
        }

        // Android 和 Harmony 使用相同的消息格式
        return $this->buildAndroidPayload($message);
    }

    /**
     * 构建 iOS 消息内容
     */
    protected function buildIOSPayload(MessageInterface $message): array {
        $payload = [
            'aps' => [
                'alert' => [
                    'title' => $message->getTitle(),
                    'body'  => $message->getContent(),
                ],
            ],
        ];

        // 添加声音
        if ($sound = $message->getSound()) {
            $payload['aps']['sound'] = $sound;
        } else {
            $payload['aps']['sound'] = 'default';
        }

        // 添加角标
        if ($badge = $message->getBadge()) {
            $payload['aps']['badge'] = $badge;
        }

        // 静默推送
        if ($message->getType() === 'message') {
            $payload['aps']['content-available'] = 1;
        }

        // 添加自定义数据
        if ($custom = $message->getCustom()) {
            $payload['custom'] = $custom;
        }

        // 添加额外参数(注意避开友盟保留字段)
        if ($extras = $message->getExtras()) {
            //            foreach ($extras as $key => $value) {
            //                if (!in_array($key, ['d', 'p'])) {
            //                    $payload[$key] = $value;
            //                }
            //            }

            $payload['extra'] = $extras;
        }

        return $payload;
    }

    /**
     * 构建 Android/Harmony 消息内容
     */
    protected function buildAndroidPayload(MessageInterface $message): array {
        if ($message->getType() === 'message') {
            $body = [];

            if ($custom = $message->getCustom()) {
                $body['custom'] = $custom;
            }

            $payload = [
                'display_type' => 'message',
                'body'         => $body,
            ];

            // 添加额外参数
            if ($extras = $message->getExtras()) {
                $payload['extra'] = $extras;
            }
        } else {
            $body = [
                'ticker'       => $message->getTitle(),
                'title'        => $message->getTitle(),
                'text'         => $message->getContent(),
                'after_open'   => $message->getAfterOpen() ?? 'go_app',
                'play_lights'  => 'true',
                'play_sound'   => 'true',
                'play_vibrate' => 'true',
            ];

            // 添加可选参数
            $this->addOptionalAndroidParams($body, $message);

            if ($custom = $message->getCustom()) {
                $body['custom'] = $custom;
            }

            $payload = [
                'display_type' => 'notification',
                'body'         => $body,
            ];

            // 添加额外参数
            if ($extras = $message->getExtras()) {
                $payload['extra'] = $extras;
            }
        }

        return $payload;
    }

    /**
     * 添加Android/Harmony平台可选参数
     */
    protected function addOptionalAndroidParams(array &$body, MessageInterface $message): void {
        // 大文本内容
        if ($bigBody = $message->getBigBody()) {
            $body['big_body'] = $bigBody;
        }

        // 图标
        if ($icon = $message->getIcon()) {
            $body['icon'] = $icon;
        }

        // 大图标URL
        if ($img = $message->getImg()) {
            $body['img'] = $img;
        }

        // 展开图片URL(仅小米通道支持)
        if ($expandImage = $message->getExpandImage()) {
            $body['expand_image'] = $expandImage;
        }

        // 自定义声音
        if ($sound = $message->getSound()) {
            $body['sound'] = $sound;
        }

        // 处理点击动作
        switch ($message->getAfterOpen()) {
            case 'go_url':
                if ($url = $message->getUrl()) {
                    $body['url'] = $url;
                }
                break;
            case 'go_activity':
                if ($activity = $message->getActivity()) {
                    $body['activity'] = $activity;
                }
                break;
            case 'go_custom':
                if ($custom = $message->getCustom()) {
                    $body['custom'] = $custom;
                }
                break;
        }

        // 处理角标
        if ($badge = $message->getBadge()) {
            if (str_starts_with($badge, '+')) {
                $body['add_badge'] = (int)substr($badge, 1);
            } else {
                $body['set_badge'] = (int)$badge;
            }
        }
    }

    /**
     * 构建消息策略
     */
    protected function buildPolicy(MessageInterface $message): array {
        $policy = [];

        // 过期时间
        if ($expireTime = $message->getExpireTime()) {
            $policy['expire_time'] = date('Y-m-d H:i:s', time() + $expireTime);
        }

        // 消息ID用于去重
        if ($messageId = $message->getMessageId()) {
            $policy['out_biz_no'] = $messageId;
        }

        return $policy;
    }

    /**
     * 构建厂商通道特殊配置
     * 仅适用于Android和Harmony平台
     * @throws PushException
     */
    protected function buildChannelProperties(MessageInterface $message): array {
        $properties = [];
        $channelConfig = $message->getChannelProperties() ?? [];

        if ($this->platform === 'android') {
            // 系统弹窗配置
            // channel_activity判断
            if (isset($channelConfig['channel_activity'])) {
                $properties['channel_activity'] = $channelConfig['channel_activity'];
            } else if ($channelActivity = $this->getClient()
                                               ->getConfig('android.channelActivity')) {
                $properties['channel_activity'] = $channelActivity;
            }

            if ($mainActivity = $this->getClient()
                                     ->getConfig('android.mainActivity')) {
                $properties['main_activity'] = $mainActivity;
            }

            // 厂商通道配置
            $androidProperties = [
                'xiaomi_channel_id',
                'vivo_category',
                'oppo_channel_id',
                'oppo_category',
                'huawei_channel_category',
            ];

            foreach ($androidProperties as $property) {
                if (!empty($channelConfig[$property])) {
                    $properties[$property] = $channelConfig[$property];
                    // 如果为oppo_category , 则设置 oppo_notify_level
                    if ($property === 'oppo_category') {
                        $properties['oppo_notify_level'] = $channelConfig['oppo_notify_level'] ?? '16';
                    }
                }
            }

            // 如果未设置华为通道分类,使用默认值
            if (!isset($properties['huawei_channel_category'])) {
                $properties['huawei_channel_importance'] = 'NORMAL';
            }
        } elseif ($this->platform === 'harmony') {
            if (!empty($channelConfig['harmony_channel_category'])) {
                $properties['harmony_channel_category'] = $channelConfig['harmony_channel_category'];
            } else {
                $properties['harmony_channel_category'] = $this->getClient()
                                                               ->getConfig('harmony.channelCategory', 'NORMAL');
            }
        }

        return array_filter($properties);
    }

    /**
     * 发送请求到推送平台
     *
     * @throws PushException
     */
    protected function sendRequest(array $params): array {
        try {
            $response = $this->getClient()
                             ->send($params);

            if (!isset($response['ret']) || $response['ret'] !== 'SUCCESS') {
                $errorMsg = $response['error_msg'] ?? '未知错误';
                $errorCode = $response['error_code'] ?? 0;
                throw new PushException("Push request failed: {$errorMsg}", $errorCode);
            }

            return [
                'success'      => true,
                'message_id'   => $response['data']['msg_id'] ?? '',
                'platform'     => $this->platform,
                'error_code'   => 0,
                'error_msg'    => '',
                'raw_response' => $response,
            ];

        } catch (\Throwable $e) {
//            Log::error('UMeng push failed', [
//                'error'    => $e->getMessage(),
//                'platform' => $this->platform,
//                'params'   => $params,
//                'response' => $response ?? null,
//            ]);

            if ($e instanceof PushException) {
                throw $e;
            }

            throw new PushException("Push request failed: {$e->getMessage()}", $e->getCode(), $e);
        }
    }

    public function revoke(string $messageId): array {
        // TODO: Implement revoke() method.
    }

    public function statistics(array $params): array {
        // TODO: Implement statistics() method.
    }
}
