<?php

namespace App\Listeners;

use App\Enums\OAuthScopeEnum;
use App\Events\AdminUserUpdateEvent;
use App\Models\AdminUser;
use App\Models\Client;
use App\Utils\OAuthCacher;
use App\Utils\SignatureValidator;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use App\Utils\Tools;
use Illuminate\Http\Client\ConnectionException;

class AdminUserUpdateListener implements ShouldQueue
{
    use InteractsWithQueue;

    public $queue = 'account';

    /**
     * Create the event listener.
     */
    public function __construct() {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(AdminUserUpdateEvent $event): void {
        $adminUser = $event->getAdminUser();

        $postData = [
            'event'   => 'adminuser_info_modified',
            'to_user' => $adminUser->uuid,
            'field'   => $event->getField(),
            'value'   => $adminUser->{$event->getField()},
        ];

        if ($event->getField() == 'avatar') {
            $postData = [
                'event'   => 'adminuser_info_modified',
                'to_user' => $adminUser->uuid,
                'field'   => 'avatar',
                'value'   => $adminUser->display_avatar,
            ];
        }

        Client::where('is_revoked', Client::IS_REVOKED_NO)
              ->where('is_workspace_client', Client::IS_WORKSPACE_CLIENT_YES)
              ->get()
              ->each(function (Client $client) use ($postData) {
                //   if ($client->is_system != Client::IS_SYSTEM_YES) {
                //       if (!OAuthCacher::isUserAuthorized($postData['to_user'], $client->id)) {
                //           return;
                //       }
                //       $toUser = OAuthCacher::getOpenIdByUserUuid($postData['to_user'], $client->id);

                //       if (!$toUser) {
                //           return;
                //       }

                //       $postData['to_user'] = $toUser;
                //   }


                  $signatureData = [
                          'client_id' => $client->client_key,
                          'timestamp' => time(),
                          'nonce'     => Str::random(),
                      ] + $postData;

                  $signatureValidator = new SignatureValidator($client->client_key, $client->client_access_secret);

                  $signature = $signatureValidator->generateSignature($signatureData);

                  // 验证callback_url是否为有效的url
                  if ($client->callback_url && filter_var($client->callback_url, FILTER_VALIDATE_URL)) {
                      try {
                          $res = Http::withHeaders([
                              Tools::HEADER_SIGN      => $signature,
                              Tools::HEADER_TIMESTAMP => $signatureData['timestamp'],
                              Tools::HEADER_NONCE     => $signatureData['nonce'],
                              Tools::HEADER_CLIENT_ID => $client->client_key,
                          ])
                                     ->withOptions([
                                         'verify' => false,
                                         'curl'   => [
                                             CURLOPT_RESOLVE => [
                                                 'uc.example.com:80:127.0.0.1',
                                                 'kanchangzhou.example.com:80:127.0.0.1',
                                             ],
                                         ],
                                     ])
                                     ->post($client->callback_url, $postData);

                      } catch (ConnectionException $e) {

                      }
                  }
              });

    }
}
