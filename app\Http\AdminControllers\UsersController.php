<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Http\Resources\Admins\UserResource;
use App\Models\Material;
use App\Models\User;
use App\Models\UserChangeLog;
use App\Http\Resources\Admins\UserPendingAuditResource;
use App\Utils\Respond;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class UsersController extends  AdminBaseController
{
    protected $validationMessages = [
        'username.required' => '用户名不能为空',
        'username.string'   => '用户名必须是字符串',
        'username.min'      => '用户名不能少于4个字符',
        'username.max'      => '用户名不能超过20个字符',
        'username.unique'   => '用户名已存在',
        'password.required' => '密码不能为空',
        'password.min'      => '密码不能少于8个字符',
        'password.regex'    => '密码必须包含大小写字母、数字和特殊字符',
        'nickname.required' => '昵称不能为空',
        'nickname.string'   => '昵称必须是字符串',
        'nickname.max'      => '昵称不能超过16个字符',
        'gender.required'   => '性别不能为空',
        'gender.in'         => '无效的性别类型',
        'mobile.required'   => '手机号不能为空',
        'mobile.string'     => '手机号必须是字符串',
        'mobile.size'       => '手机号必须是11位',
        'mobile.isMobile'   => '无效的手机号格式',
        'mobile.unique'     => '手机号已存在',
        'status.required'   => '状态不能为空',
        'status.in'         => '无效的状态类型',
        'avatar.string'     => '头像必须是字符串',
        'birthday.date'     => '生日必须是有效的日期',
        'comment.max'       => '备注不能超过100个字符',
    ];

    protected const PERMISSION_MAP = [
        'index'                => '用户管理.查看列表',
        'show'                 => '用户管理.查看详情',
        'store'                => '用户管理.创建',
        'update'               => '用户管理.编辑',
        'destroy'              => '用户管理.删除',
        'changeStatus'         => '用户管理.修改状态',
        'resetPassword'        => '用户管理.重置密码',
        'getBindings'          => '用户管理.查看绑定应用',
        'getAuthorizations'    => '用户管理.查看授权应用',
        'revokeAuthorization'  => '用户管理.撤销授权',
        'getTokens'            => '用户管理.查看令牌',
        'revokeToken'          => '用户管理.撤销令牌',
        'getPendingChanges'    => '用户管理.查看待审核变更',
        'approveChange'        => '用户管理.批准变更',
        'rejectChange'         => '用户管理.拒绝变更',
        'batchApproveChanges'  => '用户管理.批量批准变更',
    ];

    /**
     * 获取用户列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::query();

        // 搜索条件
        $query->when($request->filled('keyword'), function ($query) use ($request) {
            $keyword = $request->input('keyword');
            $query->where(function ($q) use ($keyword) {
                $q->where('username', 'like', "%{$keyword}%")
                    ->orWhere('nickname', 'like', "%{$keyword}%")
                    ->orWhere('mobile', 'like', "%{$keyword}%")
                    ->orWhere('uuid', $keyword);
            });
        })
            // 状态筛选
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->input('status'));
            })
            // 实名认证状态筛选
            ->when($request->filled('realname_auth'), function ($query) use ($request) {
                $query->where('realname_auth_confirmed', $request->input('realname_auth'));
            })
            // 注册时间范围
            ->when($request->filled([
                'start_at',
                'end_at',
            ]), function ($query) use ($request) {
                $query->whereBetween('created_at', [
                    $request->input('start_at'),
                    $request->input('end_at'),
                ]);
            })
            // 手机认证状态
            ->when($request->filled('mobile_confirmed'), function ($query) use ($request) {
                $query->where('mobile_confirmed', $request->input('mobile_confirmed'));
            })
            // 注册平台
            ->when($request->filled('register_platform'), function ($query) use ($request) {
                $query->where('register_env_uni_platform', $request->input('register_platform'));
            });

        $users = $query->select(['id', 'uuid', 'username', 'nickname', 'nickname_confirmed', 'gender', 'status', 'mobile', 'mobile_confirmed', 'avatar', 'avatar_confirmed', 'birthday', 'realname_auth_confirmed', 'comment', 'comment_confirmed', 'created_at', 'updated_at'])
            ->orderByDesc('id')
            ->paginate();

        return Respond::success(UserResource::collection($users));
    }

    /**
     * 获取用户详情
     */
    public function show(string $uuid): JsonResponse
    {
        $user = User::findOrFail($uuid);

        return Respond::success(UserResource::make($user));
    }

    public function options()
    {
        return Respond::success([
            'status'        => User::$statusMap,
            'gender'        => User::$genderMap,
            'confirmed'     => User::$confirmedMap,
            'realname_auth' => User::$realnameAuthMap,
            'platform'      => User::$registerEnvPlatformMap,
            'os'            => User::$registerEnvOsMap,
        ]);
    }

    /**
     * 创建用户
     */
    public function store(Request $request)
    {
        // 验证请求数据
        $validated = $request->validate([
            'username' => 'required|string|min:4|max:20|unique:users',
            'password' => [
                'required',
                'min:8',
                'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
            ],
            'nickname' => 'required|string|max:16',
            'gender'   => [
                'required',
                Rule::in([
                    User::GENDER_UNKNOWN,
                    User::GENDER_MALE,
                    User::GENDER_FEMALE,
                ]),
            ],
            'mobile'   => 'required|string|size:11|isMobile|unique:users',
            'status'   => [
                'required',
                Rule::in([
                    User::STATUS_INACTIVE,
                    User::STATUS_ACTIVE,
                    User::STATUS_DISABLED,
                    User::STATUS_LOCKED,
                ]),
            ],
            'avatar'   => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)
                        ->first();
                    if ($value && !$material) {
                        $fail('无效的头像资源');
                    }
                },
            ],
            'comment'  => 'nullable|string|max:100',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            // 密码加密
            $validated['password'] = Hash::make($validated['password']);

            // 设置默认值
            $validated['uuid'] = Str::uuid();
            $validated['nickname_confirmed'] = User::CONFIRMED_YES;
            $validated['mobile_confirmed'] = User::CONFIRMED_YES;
            $validated['avatar_confirmed'] = User::CONFIRMED_YES;
            $validated['comment_confirmed'] = User::CONFIRMED_YES;


            $avatarMaterrial = Material::where('uuid', $validated['avatar'] ?? null)
                ->first();
            $validated['avatar'] = $avatarMaterrial ? [
                'path'     => $avatarMaterrial->path,
                'provider' => $avatarMaterrial->provider,
                'uuid'     => $avatarMaterrial->uuid,
            ] : ['path' => config('uc.default_avatar'), 'provider' => 'url', 'uuid' => ''];

            // 创建用户
            $user = User::create($validated);

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();

            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR->value, '创建用户失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    public function update(Request $request, string $uuid): JsonResponse
    {
        $user = User::findOrFail($uuid);
        /** @var \App\Models\AdminUser $adminUser */
        $adminUser = Auth::guard('admin')->user(); // 获取当前管理员

        $validated = $request->validate([
            'username' => [
                'required',
                'string',
                'min:4', // 与 store 保持一致
                'max:20', // 与 store 保持一致
                Rule::unique('users')
                    ->ignore($user->id, 'id'), // 按 id 忽略
            ],
            'nickname' => 'required|string|max:16',
            'gender'   => [
                'required',
                Rule::in([
                    User::GENDER_UNKNOWN,
                    User::GENDER_MALE,
                    User::GENDER_FEMALE,
                ]),
            ],
            'status'   => [
                'required',
                Rule::in([
                    User::STATUS_INACTIVE,
                    User::STATUS_ACTIVE,
                    User::STATUS_DISABLED,
                    User::STATUS_LOCKED,
                ]),
            ],
            'avatar'   => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    if ($value && !Material::where('uuid', $value)->exists()) {
                        $fail('无效的头像资源 UUID');
                    }
                },
            ],
            'birthday' => 'nullable|date_format:Y-m-d',
            'comment'  => 'nullable|string|max:100',
            'password' => [
                'nullable',
                'min:8',
                'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
            ],
            // 密码是可选的
        ], $this->validationMessages);

        $auditFields = ['nickname', 'avatar', 'comment', 'username'];
        $changes = [];

        DB::beginTransaction();
        try {
            // 处理密码
            if (isset($validated['password'])) {
                $validated['password'] = Hash::make($validated['password']);
            }
             // 处理头像
             $avatarUuid = $validated['avatar'] ?? null;
             unset($validated['avatar']); // 从主数组移除，单独处理
             $newAvatarData = null;
             $currentAvatarUuid = $user->avatar['uuid'] ?? null;

             if ($avatarUuid && $avatarUuid !== $currentAvatarUuid) {
                 $material = Material::where('uuid', $avatarUuid)->first();
                 if ($material) {
                     $newAvatarData = [
                        'path'     => $material->path,
                        'provider' => $material->provider,
                        'uuid'     => $material->uuid,
                    ];
                 } else {
                     // 虽然验证已过，但以防万一
                     DB::rollBack();
                     return Respond::error(ErrorCodeEnum::INVALID_REQUEST->value, '更新失败：无效的头像资源');
                 }
                 $validated['avatar'] = $newAvatarData; // 加入待更新数组
                 $validated['avatar_confirmed'] = User::CONFIRMED_YES; // 管理员更新，直接确认
                 $changes['avatar'] = [
                     'old' => json_encode($user->getOriginal('avatar') ?: ['path' => config('uc.default_avatar'), 'provider' => 'url', 'uuid' => '']),
                     'new' => $avatarUuid // changeLog 存 uuid
                 ];
             } else if (!$avatarUuid && $currentAvatarUuid) {
                  // 如果传入空值且当前有头像，则设置回默认头像？ 或不允许清空？
                  // 这里假设不允许管理员直接清空头像，保留原值。如果需要清空，则设置 $validated['avatar'] = [...] 并记录 changeLog
             }

             // 处理其他字段
             foreach ($validated as $field => $newValue) {
                // 跳过 avatar_confirmed，因为它随 avatar 一起处理
                 if ($field === 'avatar_confirmed') continue;

                 $currentValue = $user->getOriginal($field);

                 if ($currentValue != $newValue) {
                      $isAuditField = in_array($field, $auditFields);

                      // 记录变更，无论是否为审核字段
                      if ($isAuditField) {
                          $changes[$field] = [
                              'old' => $currentValue,
                              'new' => $newValue
                          ];
                      }

                      // 如果是需要审核的字段，同时更新 confirmed 状态为 YES
                      $confirmedField = $field . '_confirmed';
                      if ($isAuditField && property_exists($user, $confirmedField)) {
                           $validated[$confirmedField] = User::CONFIRMED_YES;
                      }
                 }
             }

            // 更新 User 表
            if (!empty($validated)) {
                $user->update($validated);
            }

            DB::commit();

            return Respond::success(UserResource::make($user->refresh()));
        } catch (\Exception $e) {
            DB::rollBack();
            // logger()->error('Admin update user failed', ['user_uuid' => $uuid, 'error' => $e->getMessage()]);
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR->value, '更新用户失败：' . $e->getMessage());
        }
    }

    /**
     * 获取有待审核信息的用户列表
     * @param Request $request
     * @return JsonResponse
     */
    public function getPendingChanges(Request $request): JsonResponse
    {
        $query = User::query()
                     ->where(function (Builder $q) {
                         $q->where('nickname_confirmed', User::CONFIRMED_NO)
                           ->orWhere('avatar_confirmed', User::CONFIRMED_NO)
                           ->orWhere('comment_confirmed', User::CONFIRMED_NO);
                           // 检查是否存在待审核的 username 变更
                        //    ->orWhereHas('changeLogs', function (Builder $logQuery) {
                        //        $logQuery->where('change_key', 'username')
                        //                 ->where('status', UserChangeLog::STATUS_PENDING);
                        //    });
                     })
                     // 预加载最新的待审核变更记录 (每个字段只加载最新一条)
                     ->with([
                         'latestPendingNicknameLog',
                         'latestPendingAvatarLog',
                         'latestPendingCommentLog',
                        //  'latestPendingUsernameLog'
                     ]);

        // 可选：按用户关键词搜索 (用户名/昵称/uuid)
        $query->when($request->filled('user_keyword'), function ($q) use ($request) {
            $keyword = $request->input('user_keyword');
            $q->where(function($userQuery) use ($keyword) {
                 $userQuery->where('username', 'like', "%{$keyword}%")
                          ->orWhere('nickname', 'like', "%{$keyword}%")
                          ->orWhere('uuid', $keyword);
            });
        });

        // 可选：按需要审核的字段筛选
         $query->when($request->filled('field'), function ($q) use ($request) {
            $field = $request->input('field');
            match ($field) {
                'nickname' => $q->where('nickname_confirmed', User::CONFIRMED_NO),
                'avatar' => $q->where('avatar_confirmed', User::CONFIRMED_NO),
                'comment' => $q->where('comment_confirmed', User::CONFIRMED_NO),
                'username' => $q->whereHas('changeLogs', fn($lq) => $lq->where('change_key', 'username')->where('status', UserChangeLog::STATUS_PENDING)),
                default => null,
            };
        });

        $users = $query->orderByDesc('updated_at') // 按用户更新时间排序可能更合适
                       ->paginate($request->input('per_page', 15));

        // 需要创建 UserPendingAuditResource 来格式化输出
        return Respond::success(UserPendingAuditResource::collection($users));
    }

    /**
     * 批准用户指定字段的变更请求
     * @param Request $request
     * @param string $uuid 用户 UUID
     * @return JsonResponse
     */
    public function approveChange(Request $request, string $uuid): JsonResponse
    {
        $validated = $request->validate([
            'field' => 'required|string|in:nickname,avatar,comment,username',
        ]);
        $field = $validated['field'];

        $user = User::where('uuid', $uuid)->firstOrFail();

        // 查找最新的待审核记录
        $log = $user->latestPendingLog($field)->first();

        if (!$log) {
            // 如果找不到待审核记录，检查用户表确认状态是否已经是 YES
             $confirmedField = $field . '_confirmed';
            if ($field !== 'username' && property_exists($user, $confirmedField) && $user->$confirmedField === User::CONFIRMED_YES) {
                 return Respond::success(null, '该字段已经是审核通过状态');
            }
            return Respond::error(ErrorCodeEnum::NOT_FOUND->value, '未找到该字段的待审核记录');
        }

        /** @var \App\Models\AdminUser $adminUser */
        $adminUser = Auth::guard('admin')->user();

        DB::beginTransaction();
        try {
            $newValue = $log->new_value;

            // 更新 User 表
            if ($field === 'avatar') {
                // 头像 newValue 可能是 uuid 或 json 字符串
                $avatarUuid = null;
                 if (is_string($newValue)) {
                    $decoded = json_decode($newValue, true);
                    if (json_last_error() === JSON_ERROR_NONE && isset($decoded['uuid'])) {
                        $avatarUuid = $decoded['uuid'];
                    } else if (Str::isUuid($newValue)) { // 检查是否是 UUID 字符串
                         $avatarUuid = $newValue;
                    }
                }

                if (!$avatarUuid) {
                    DB::rollBack();
                    return Respond::error(ErrorCodeEnum::INVALID_REQUEST->value, '无效的头像数据格式');
                }

                 $material = Material::where('uuid', $avatarUuid)->first();
                 if (!$material) {
                     DB::rollBack();
                     return Respond::error(ErrorCodeEnum::INVALID_REQUEST->value, '无效的头像资源');
                 }
                 $user->avatar = [
                     'uuid'     => $material->uuid,
                     'path'     => $material->path,
                     'provider' => $material->provider,
                 ];
            } else {
                $user->$field = $newValue;
            }

            // 更新确认状态
            $confirmedField = $field . '_confirmed';
            if ($user->hasAttribute($confirmedField)) {
                $user->$confirmedField = User::CONFIRMED_YES;
            }

            $user->save();

            // 更新 ChangeLog 状态
            $log->update([
                'status'     => UserChangeLog::STATUS_APPROVED,
                'admin_user_id'   => $adminUser->id,
                'audit_time' => Carbon::now(),
            ]);

             // 可选：将此用户此字段的其他旧的 PENDING 记录标记为 REJECTED？
             UserChangeLog::where('user_id', $user->id)
                          ->where('change_key', $field)
                          ->where('status', UserChangeLog::STATUS_PENDING)
                          ->where('id', '!=', $log->id)
                          ->update([
                              'status' => UserChangeLog::STATUS_REJECTED,
                              'admin_user_id' => $adminUser->id, // 记录是系统自动拒绝
                              'audit_time' => Carbon::now(),
                          ]);

            DB::commit();
            return Respond::success(null, '审核通过');
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR->value, '批准失败: ' . $e->getMessage());
        }
    }

     /**
     * 拒绝用户指定字段的变更请求
     * @param Request $request
     * @param string $uuid 用户 UUID
     * @return JsonResponse
     */
    public function rejectChange(Request $request, string $uuid): JsonResponse
    {
        $validated = $request->validate([
            'field' => 'required|string|in:nickname,avatar,comment,username',
        ]);
        $field = $validated['field'];

        $user = User::where('uuid', $uuid)->firstOrFail();

        // 查找最新的待审核记录
        $log = $user->latestPendingLog($field)->first();

        if (!$log) {
            return Respond::error(ErrorCodeEnum::NOT_FOUND->value, '未找到该字段的待审核记录');
        }

        /** @var \App\Models\AdminUser $adminUser */
        $adminUser = Auth::guard('admin')->user();

        try {
            // 更新 ChangeLog 状态
            $log->update([
                'status'     => UserChangeLog::STATUS_REJECTED,
                'admin_user_id'   => $adminUser->id,
                'audit_time' => Carbon::now(),
            ]);

             // 用户标的 confirmed 恢复原状
             $confirmedField = $field . '_confirmed';
             if ($user->hasAttribute($confirmedField)) {
                $user->$confirmedField = User::CONFIRMED_YES;
                $user->save();
             }

            return Respond::success(null, '审核已拒绝');
        } catch (\Exception $e) {
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR->value, '拒绝失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量批准指定用户的指定字段变更请求
     * @param Request $request
     * @return JsonResponse
     */
    public function batchApproveChanges(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_uuids' => 'required|array',
            'user_uuids.*' => 'string|uuid', // 确保是 UUID 数组
            'field' => 'required|string|in:nickname,avatar,comment,username', // 必须指定字段
        ]);

        /** @var \App\Models\AdminUser $adminUser */
        $adminUser = Auth::guard('admin')->user();
        $userUuids = $validated['user_uuids'];
        $field = $validated['field'];
        $approvedCount = 0;
        $errors = [];

        $users = User::whereIn('uuid', $userUuids)->get();

        if ($users->isEmpty()) {
             return Respond::error(ErrorCodeEnum::INVALID_REQUEST->value, '未找到指定的用户');
        }

        foreach ($users as $user) {
            DB::beginTransaction();
            try {
                // 查找该用户该字段最新的待审核记录
                $log = $user->latestPendingLog($field)->first();

                if (!$log) {
                    // $errors[$user->uuid] = '未找到待审核记录';
                    DB::rollBack(); // 仅回滚当前事务
                    continue; // 处理下一个用户
                }

                $newValue = $log->new_value;

                // 更新 User 表
                if ($field === 'avatar') {
                    $avatarUuid = null;
                     if (is_string($newValue)) {
                        $decoded = json_decode($newValue, true);
                         if (json_last_error() === JSON_ERROR_NONE && isset($decoded['uuid'])) {
                            $avatarUuid = $decoded['uuid'];
                        } else if (Str::isUuid($newValue)) {
                            $avatarUuid = $newValue;
                        }
                    }
                    if (!$avatarUuid) {
                        $errors[$user->uuid] = '无效的头像数据格式';
                         DB::rollBack(); 
                         continue;
                    }
                    $material = Material::where('uuid', $avatarUuid)->first();
                    if (!$material) {
                         $errors[$user->uuid] = '无效的头像资源';
                         DB::rollBack(); 
                         continue;
                    }
                    $user->avatar = [
                        'uuid'     => $material->uuid,
                        'path'     => $material->path,
                        'provider' => $material->provider,
                    ];
                } else {
                    $user->$field = $newValue;
                }

                // 更新确认状态
                $confirmedField = $field . '_confirmed';
                if ($user->hasAttribute($confirmedField)) {
                    $user->$confirmedField = User::CONFIRMED_YES;
                }
                $user->save();

                // 更新 ChangeLog 状态
                $log->update([
                    'status'     => UserChangeLog::STATUS_APPROVED,
                    'admin_user_id'   => $adminUser->id,
                    'audit_time' => Carbon::now(),
                ]);

                 // 可选：标记其他旧 PENDING 为 REJECTED
                UserChangeLog::where('user_id', $user->id)
                          ->where('change_key', $field)
                          ->where('status', UserChangeLog::STATUS_PENDING)
                          ->where('id', '!=', $log->id)
                          ->update([
                              'status' => UserChangeLog::STATUS_REJECTED,
                              'admin_user_id' => $adminUser->id,
                              'audit_time' => Carbon::now(),
                          ]);

                DB::commit();
                $approvedCount++;
            } catch (\Exception $e) {
                DB::rollBack();
                 $errors[$user->uuid] = '批准失败: ' . $e->getMessage();
                logger()->error('Batch approve change failed for user: ' . $user->uuid, ['field' => $field, 'error' => $e->getMessage()]);
            }
        }

        $total = count($userUuids);
        if (empty($errors)) {
            return Respond::success(null, "成功批准 {$approvedCount} 个用户的 {$field} 字段变更。");
        } else {
            return Respond::error(ErrorCodeEnum::PARTIAL_FAILURE->value, "操作完成，成功批准 {$approvedCount} 个，失败 " . count($errors) . " 个用户的 {$field} 字段变更。", ['errors' => $errors]);
        }
    }

    /**
     * 修改用户状态
     */
    public function updateStatus(Request $request, string $uuid): JsonResponse
    {
        $validated = $request->validate([
            'status' => [
                'required',
                Rule::in([
                    User::STATUS_ACTIVE,
                    User::STATUS_DISABLED,
                    User::STATUS_LOCKED,
                ]),
            ],
        ], $this->validationMessages);

        try {
            $user = User::findOrFail($uuid);
            $user->update($validated);

            $statusText = $user->status_text ?? '更新';

            return Respond::success(null, "用户{$statusText}成功");
        } catch (\Exception $e) {
            return Respond::error(1, "操作失败：" . $e->getMessage());
        }
    }
}
