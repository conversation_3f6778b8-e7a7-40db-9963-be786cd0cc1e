<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // 消息模板表
        Schema::create('push_templates', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->unique()->comment('模板唯一编码');
            $table->string('name', 100)->comment('模板名称');
            $table->string('title')->comment('消息标题模板');
            $table->text('content')->comment('消息内容模板');
            $table->tinyInteger('category')->comment('消息分类:1系统通知,2活动通知,3@我的,4钱包通知,5被评论,6被点赞,7关注通知');
            $table->tinyInteger('delivery_type')->default(\App\Enums\PushMessageDeliveryEnum::DELIVERY_USER->value)->comment('目标类型:1安卓设备,2iOS设备,3用户,5实时活动pushToken,6实时活动activityId,7鸿蒙设备');
            $table->json('allowed_params')->nullable()->comment('允许的模板参数定义:{key:描述}');
            $table->json('allowed_extend_params')->nullable()->comment('允许的扩展参数定义:{key:描述}');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->timestamps();
            $table->softDeletes();
        });

        // 应用可用模板表
        Schema::create('oauth_client_templates', function (Blueprint $table) {
            $table->id();
            $table->string('oauth_client_id')->comment('OAuth Client ID');
            $table->unsignedBigInteger('push_template_id')->comment('模板ID');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['oauth_client_id', 'push_template_id'],'client_push_template_ix');
        });

        // 应用订阅配置表
        Schema::create('oauth_client_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('oauth_client_id')->comment('OAuth Client ID');
            $table->string('name')->comment('订阅名称');
            $table->string('description')->nullable()->comment('订阅描述');
            $table->unsignedBigInteger('push_template_id')->comment('使用的模板ID');
            $table->json('filter_conditions')->nullable()->comment('订阅过滤条件');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->timestamps();
            $table->softDeletes();
        });

        // 用户订阅表
        Schema::create('user_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('oauth_client_subscription_id')->comment('订阅配置ID');
            $table->uuid('user_uuid')->comment('用户UUID');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['oauth_client_subscription_id', 'user_uuid'],'subscription_user_ix');
        });

        // 定时推送主任务表
        Schema::create('push_schedules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('push_template_id')->comment('模板ID');
            $table->string('oauth_client_id')->nullable()->comment('来源应用ID');
            $table->string('title')->comment('任务标题');
            $table->tinyInteger('strategy_type')->comment('策略类型:0立即,1定时,2循环');
            $table->timestamp('fixed_time')->nullable()->comment('定时发送时间');
            $table->timestamp('start_time')->nullable()->comment('循环开始时间');
            $table->timestamp('end_time')->nullable()->comment('循环结束时间');
            $table->tinyInteger('circle_type')->nullable()->comment('循环类型:1每天,2每周,3每月');
            $table->json('circle_value')->nullable()->comment('循环值');
            $table->string('circle_time')->nullable()->comment('循环发送时间,格式:HH:mm:ss');
            $table->tinyInteger('delivery_type')->comment('目标类型:1安卓设备,2iOS设备,3用户,5实时活动pushToken,6实时活动activityId,7鸿蒙设备');
            $table->json('template_params')->nullable()->comment('模板参数');
            $table->json('extend_params')->nullable()->comment('扩展参数');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->timestamps();
            $table->softDeletes();

            $table->index('status');
        });

        // 定时推送批次表
        Schema::create('push_schedule_batches', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('push_schedule_id')->comment('主任务ID');
            $table->json('target_list')->comment('本批次目标列表');
            $table->integer('batch_no')->comment('批次号');
            $table->tinyInteger('status')->default(0)->comment('状态:0未执行,1已执行,2执行失败');
            $table->timestamp('executed_at')->nullable()->comment('执行时间');
            $table->string('error_msg')->nullable()->comment('失败原因');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['push_schedule_id', 'batch_no']);
            $table->index('status');
        });

        // 消息记录表
        Schema::create('push_messages', function (Blueprint $table) {
            $table->id();
            $table->string('msg_key', 64)->comment('消息唯一标识');
            $table->unsignedBigInteger('push_template_id')->comment('关联的模板ID');
            $table->string('oauth_client_id')->nullable()->comment('来源应用ID');
            $table->unsignedBigInteger('oauth_client_subscription_id')->nullable()->comment('关联的订阅ID');
            $table->unsignedBigInteger('push_schedule_id')->nullable()->comment('关联的定时任务ID');
            $table->unsignedBigInteger('push_schedule_batch_id')->nullable()->comment('关联的批次ID');
            $table->string('title')->comment('消息标题');
            $table->text('content')->comment('消息内容');
            $table->tinyInteger('category')->comment('消息分类');
            $table->tinyInteger('delivery_type')->comment('目标类型:1安卓设备,2iOS设备,3用户,5实时活动pushToken,6实时活动activityId,7鸿蒙设备');
            $table->string('target_id', 64)->nullable()->comment('推送目标ID');
            $table->tinyInteger('status')->default(0)->comment('状态:0待发送,1发送成功,2发送失败');
            $table->timestamp('push_time')->nullable()->comment('推送时间');
            $table->integer('expired_seconds')->nullable()->default(300)->comment('有效期(秒)');
            $table->json('template_params')->nullable()->comment('模板参数');
            $table->json('extend_params')->nullable()->comment('扩展参数');
            $table->string('error_msg')->nullable()->comment('失败原因');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['delivery_type', 'target_id']);
            $table->index('msg_key');
            $table->index('oauth_client_id');
            $table->index('status');
        });

        // 用户消息表
        Schema::create('user_messages', function (Blueprint $table) {
            $table->id();
            $table->uuid('user_uuid')->comment('用户ID');
            $table->unsignedBigInteger('push_message_id')->comment('消息ID');
            $table->tinyInteger('is_read')->default(0)->comment('是否已读:0未读,1已读');
            $table->timestamp('read_time')->nullable()->comment('阅读时间');
            $table->timestamps();
            $table->softDeletes();

            $table->index('user_uuid');
            $table->index('push_message_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_messages');
        Schema::dropIfExists('push_messages');
        Schema::dropIfExists('push_schedule_batches');
        Schema::dropIfExists('push_schedules');
        Schema::dropIfExists('user_subscriptions');
        Schema::dropIfExists('oauth_client_subscriptions');
        Schema::dropIfExists('oauth_client_templates');
        Schema::dropIfExists('push_templates');
    }
};
