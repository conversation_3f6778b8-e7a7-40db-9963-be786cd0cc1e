<?php

namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class ClientSubscriptionResource
 *
 * @property-read \App\Models\OauthClientSubscription $resource
 * @mixin \App\Models\OauthClientSubscription
 */
class ClientSubscriptionResource extends JsonResource
{
    public function toArray(Request $request): array {
        return [
            'id'                => $this->id,
            'subscription_code' => $this->subscription_code,
            'name'              => $this->name,
            'description'       => $this->description,
            'status'            => $this->status,
            'status_text'       => $this->status_text,
            //            'filter_conditions' => $this->filter_conditions,
            'push_template'     => PushTemplateSimpleResource::make($this->whenLoaded('pushTemplate')),
            'client'            => ClientResource::make($this->whenLoaded('client')),
            'created_at'        => $this->created_at,
        ];
    }
}
