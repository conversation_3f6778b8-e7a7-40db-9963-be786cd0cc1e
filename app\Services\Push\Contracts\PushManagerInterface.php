<?php

namespace App\Services\Push\Contracts;

use App\Services\Push\Exceptions\InvalidConfigurationException;

interface PushManagerInterface
{
    /**
     * 获取指定驱动的推送服务
     *
     * @param string|null $driver 驱动名称
     * @return PushServiceInterface
     * @throws InvalidConfigurationException
     */
    public function driver(?string $driver = null): PushServiceInterface;

    /**
     * 扩展自定义驱动
     *
     * @param string $driver
     * @param \Closure $callback
     * @return $this
     */
    public function extend(string $driver, \Closure $callback): self;

    /**
     * 获取默认驱动名称
     *
     * @return string
     */
    public function getDefaultDriver(): string;

    /**
     * 设置默认驱动名称
     *
     * @param string $name
     * @return $this
     */
    public function setDefaultDriver(string $name): self;
}
