<?php

namespace App\Services;

use App\Enums\PushMessageCategoryEnum;
use App\Enums\Push\XiaomiChannelEnum;
use App\Enums\Push\VivoCategoryEnum;
use App\Enums\Push\OppoChannelEnum;
use App\Enums\Push\OppoCategoryEnum;
use App\Enums\Push\HuaweiCategoryEnum;
use App\Enums\Push\HarmonyCategoryEnum;

class PushChannelManager
{
    /**
     * 根据消息类别获取对应的小米渠道ID
     * @param PushMessageCategoryEnum $category 消息类别
     * @return XiaomiChannelEnum 对应的小米渠道
     */
    public static function getXiaomiChannel(PushMessageCategoryEnum $category): XiaomiChannelEnum
    {
        return match($category) {
            PushMessageCategoryEnum::CATEGORY_SYSTEM => XiaomiChannelEnum::SYSTEM,
            PushMessageCategoryEnum::CATEGORY_ACTIVITY => XiaomiChannelEnum::ACTIVITY,
            PushMessageCategoryEnum::CATEGORY_AT, 
            PushMessageCategoryEnum::CATEGORY_WALLET,
            PushMessageCategoryEnum::CATEGORY_COMMENT,
            PushMessageCategoryEnum::CATEGORY_LIKE,
            PushMessageCategoryEnum::CATEGORY_FOLLOW,
            PushMessageCategoryEnum::CATEGORY_HISTORY => XiaomiChannelEnum::MESSAGE,
            default => XiaomiChannelEnum::SYSTEM,
        };
    }

    /**
     * 根据消息类别获取对应的VIVO消息类别
     * @param PushMessageCategoryEnum $category 消息类别
     * @return VivoCategoryEnum 对应的VIVO消息类别
     */
    public static function getVivoCategory(PushMessageCategoryEnum $category): VivoCategoryEnum
    {
        return match($category) {
            PushMessageCategoryEnum::CATEGORY_SYSTEM,
            PushMessageCategoryEnum::CATEGORY_WALLET => VivoCategoryEnum::ACCOUNT,
            
            PushMessageCategoryEnum::CATEGORY_ACTIVITY => VivoCategoryEnum::MARKETING,
            
            PushMessageCategoryEnum::CATEGORY_AT,
            PushMessageCategoryEnum::CATEGORY_COMMENT,
            PushMessageCategoryEnum::CATEGORY_LIKE,
            PushMessageCategoryEnum::CATEGORY_FOLLOW => VivoCategoryEnum::SOCIAL,
            
            PushMessageCategoryEnum::CATEGORY_HISTORY => VivoCategoryEnum::NEWS,
            
            default => VivoCategoryEnum::ACCOUNT,
        };
    }

    /**
     * 根据消息类别获取对应的OPPO渠道
     * @param PushMessageCategoryEnum $category 消息类别
     * @return OppoChannelEnum 对应的OPPO渠道
     */
    public static function getOppoChannel(PushMessageCategoryEnum $category): OppoChannelEnum
    {
        return match($category) {
            PushMessageCategoryEnum::CATEGORY_SYSTEM,
            PushMessageCategoryEnum::CATEGORY_WALLET => OppoChannelEnum::DEFAULT,
            
            PushMessageCategoryEnum::CATEGORY_ACTIVITY,
            PushMessageCategoryEnum::CATEGORY_AT,
            PushMessageCategoryEnum::CATEGORY_COMMENT,
            PushMessageCategoryEnum::CATEGORY_LIKE,
            PushMessageCategoryEnum::CATEGORY_FOLLOW,
            PushMessageCategoryEnum::CATEGORY_HISTORY => OppoChannelEnum::CONTENT,
            
            default => OppoChannelEnum::DEFAULT,
        };
    }

    /**
     * 根据消息类别获取对应的OPPO消息类别
     * @param PushMessageCategoryEnum $category 消息类别
     * @return OppoCategoryEnum 对应的OPPO消息类别
     */
    public static function getOppoCategory(PushMessageCategoryEnum $category): OppoCategoryEnum
    {
        return match($category) {
            PushMessageCategoryEnum::CATEGORY_SYSTEM,
            PushMessageCategoryEnum::CATEGORY_WALLET => OppoCategoryEnum::ACCOUNT,
            
            PushMessageCategoryEnum::CATEGORY_ACTIVITY => OppoCategoryEnum::MARKETING,
            
            PushMessageCategoryEnum::CATEGORY_AT,
            PushMessageCategoryEnum::CATEGORY_COMMENT,
            PushMessageCategoryEnum::CATEGORY_LIKE,
            PushMessageCategoryEnum::CATEGORY_FOLLOW => OppoCategoryEnum::SOCIAL,
            
            PushMessageCategoryEnum::CATEGORY_HISTORY => OppoCategoryEnum::NEWS,
            
            default => OppoCategoryEnum::ACCOUNT,
        };
    }

    /**
     * 根据消息类别获取对应的华为渠道类别
     * @param PushMessageCategoryEnum $category 消息类别
     * @return HuaweiCategoryEnum 对应的华为渠道类别
     */
    public static function getHuaweiCategory(PushMessageCategoryEnum $category): HuaweiCategoryEnum
    {
        return match($category) {
            PushMessageCategoryEnum::CATEGORY_SYSTEM,
            PushMessageCategoryEnum::CATEGORY_WALLET => HuaweiCategoryEnum::ACCOUNT,
            
            PushMessageCategoryEnum::CATEGORY_ACTIVITY => HuaweiCategoryEnum::MARKETING,
            
            PushMessageCategoryEnum::CATEGORY_AT,
            PushMessageCategoryEnum::CATEGORY_COMMENT,
            PushMessageCategoryEnum::CATEGORY_LIKE,
            PushMessageCategoryEnum::CATEGORY_FOLLOW,
            PushMessageCategoryEnum::CATEGORY_HISTORY => HuaweiCategoryEnum::SUBSCRIPTION,
            
            default => HuaweiCategoryEnum::ACCOUNT,
        };
    }

    /**
     * 根据消息类别获取对应的鸿蒙渠道类别
     * @param PushMessageCategoryEnum $category 消息类别
     * @return HarmonyCategoryEnum 对应的鸿蒙渠道类别
     */
    public static function getHarmonyCategory(PushMessageCategoryEnum $category): HarmonyCategoryEnum
    {
        return match($category) {
            PushMessageCategoryEnum::CATEGORY_SYSTEM,
            PushMessageCategoryEnum::CATEGORY_WALLET => HarmonyCategoryEnum::ACCOUNT,
            
            PushMessageCategoryEnum::CATEGORY_ACTIVITY => HarmonyCategoryEnum::MARKETING,
            
            PushMessageCategoryEnum::CATEGORY_AT,
            PushMessageCategoryEnum::CATEGORY_COMMENT,
            PushMessageCategoryEnum::CATEGORY_LIKE,
            PushMessageCategoryEnum::CATEGORY_FOLLOW,
            PushMessageCategoryEnum::CATEGORY_HISTORY => HarmonyCategoryEnum::SUBSCRIPTION,
            
            default => HarmonyCategoryEnum::ACCOUNT,
        };
    }

    /**
     * 根据消息类别获取所有推送渠道配置
     * @param PushMessageCategoryEnum $category 消息类别
     * @return array 所有推送渠道配置
     */
    public static function getAllChannelConfigs(PushMessageCategoryEnum $category): array
    {
        return [
            'xiaomi_channel_id' => self::getXiaomiChannel($category)->value,
            'vivo_category' => self::getVivoCategory($category)->value,
            'oppo_channel_id' => self::getOppoChannel($category)->value,
            'oppo_category' => self::getOppoCategory($category)->value,
            'huawei_channel_category' => self::getHuaweiCategory($category)->value,
            'harmony_channel_category' => self::getHarmonyCategory($category)->value,
        ];
    }

    /**
     * 获取所有推送渠道选项
     * @return array 所有推送渠道选项
     */
    public static function getAllChannelOptions(): array
    {
        return [
            'xiaomi_channels' => XiaomiChannelEnum::options(),
            'vivo_categories' => VivoCategoryEnum::options(),
            'oppo_channels' => OppoChannelEnum::options(),
            'oppo_categories' => OppoCategoryEnum::options(),
            'huawei_categories' => HuaweiCategoryEnum::options(),
            'harmony_categories' => HarmonyCategoryEnum::options(),
        ];
    }
}