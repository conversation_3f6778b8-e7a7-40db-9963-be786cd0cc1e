<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $push_schedule_id 主任务ID
 * @property array<array-key, mixed> $target_list 本批次目标列表
 * @property int $batch_no 批次号
 * @property int $status 状态:0未执行,1已执行,2执行失败
 * @property \Illuminate\Support\Carbon|null $executed_at 执行时间
 * @property string|null $error_msg 失败原因
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\PushSchedule|null $pushSchedule
 * @property-read mixed $status_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereBatchNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereErrorMsg($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereExecutedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch wherePushScheduleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereTargetList($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushScheduleBatch withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperPushScheduleBatch
 */
class PushScheduleBatch extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'push_schedule_id',
        'target_list',
        'batch_no',
        'status',
        'executed_at',
        'error_msg',
    ];

    protected $casts = [
        'target_list' => 'array',
        'executed_at' => 'datetime',
    ];

    // 未执行 0, 已执行1 , 执行失败 2
    const STATUS_PENDING = 0;
    const STATUS_EXECUTED = 1;
    const STATUS_FAILED = 2;

    public static array $statusMap = [
        self::STATUS_FAILED => '执行失败',
        self::STATUS_PENDING => '未执行',
        self::STATUS_EXECUTED => '已执行',
    ];

    protected function statusText(): Attribute {
        return Attribute::make(get: fn($value) => self::$statusMap[$value] ?? '未知');
    }

    public function pushSchedule(): BelongsTo {
        return $this->belongsTo(PushSchedule::class);
    }
}
