<?php

namespace App\Models;

use App\Enums\PushMessageEnum;
use App\Enums\PushMessageStrategyEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $push_template_id 模板ID
 * @property string|null $oauth_client_id 来源应用ID
 * @property string $title 任务标题
 * @property int $strategy_type 策略类型:0立即,1定时,2循环
 * @property \Illuminate\Support\Carbon|null $fixed_time 定时发送时间
 * @property \Illuminate\Support\Carbon|null $start_time 循环开始时间
 * @property \Illuminate\Support\Carbon|null $end_time 循环结束时间
 * @property int|null $circle_type 循环类型:1每天,2每周,3每月
 * @property array<array-key, mixed>|null $circle_value 循环值
 * @property string|null $circle_time 循环发送时间,格式:HH:mm:ss
 * @property int $delivery_type 目标类型:1安卓设备,2iOS设备,3用户,5实时活动pushToken,6实时活动activityId,7鸿蒙设备
 * @property array<array-key, mixed>|null $template_params 模板参数
 * @property array<array-key, mixed>|null $extend_params 扩展参数
 * @property int $status 状态:1启用,0禁用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PushScheduleBatch> $pushScheduleBatches
 * @property-read int|null $push_schedule_batches_count
 * @property-read \App\Models\PushTemplate|null $pushTemplate
 * @property-read mixed $status_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereCircleTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereCircleType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereCircleValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereDeliveryType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereExtendParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereFixedTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereOauthClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule wherePushTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereStrategyType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereTemplateParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushSchedule withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperPushSchedule
 */
class PushSchedule extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'push_template_id',
        'oauth_client_id',
        'title',
        'strategy_type',
        'fixed_time',
        'start_time',
        'end_time',
        'circle_type',
        'circle_value',
        'circle_time',
        'delivery_type',
        'template_params',
        'extend_params',
        'status',
    ];

    protected $casts = [
        'fixed_time' => 'datetime',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'circle_value' => 'array',
        'template_params' => 'array',
        'extend_params' => 'array',
    ];

    const STATUS_DISABLE = 0;
    const STATUS_ENABLE = 1;

    public static array $statusMap = [
        self::STATUS_ENABLE => '启用',
        self::STATUS_DISABLE => '禁用',
    ];

    protected function statusText(): Attribute {
        return Attribute::make(get: fn($value) => self::$statusMap[$value] ?? '未知');
    }

    public function pushTemplate(): BelongsTo {
        return $this->belongsTo(PushTemplate::class);
    }

    public function pushScheduleBatches(): HasMany {
        return $this->hasMany(PushScheduleBatch::class);
    }

    public function isImmediateStrategy(): bool {
        return $this->strategy_type == PushMessageStrategyEnum::STRATEGY_IMMEDIATE->value;
    }

    public function isFixedStrategy(): bool {
        return $this->strategy_type == PushMessageStrategyEnum::STRATEGY_FIXED->value;
    }

    public function isCircleStrategy(): bool {
        return $this->strategy_type == PushMessageStrategyEnum::STRATEGY_CIRCLE->value;
    }
}
