<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @property int $id
 * @property string $admin_user_uuid 管理员UUID
 * @property string $module 模块
 * @property string $action 操作
 * @property string $url 请求地址
 * @property string $method 请求方法
 * @property array<array-key, mixed>|null $params 请求参数
 * @property string|null $ip IP地址
 * @property string|null $user_agent User Agent
 * @property int $status 状态:1成功,0失败
 * @property string|null $remark 备注
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\AdminUser|null $admin
 * @property-read mixed $status_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereAdminUserUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereModule($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminOperationLog whereUserAgent($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAdminOperationLog
 */
class AdminOperationLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'admin_id',
        'module',
        'action',
        'url',
        'method',
        'params',
        'ip',
        'user_agent',
        'status',
        'remark'
    ];

    protected $casts = [
        'params' => 'json',
        'status' => 'integer'
    ];

    const STATUS_FAILED = 0;
    const STATUS_SUCCESS = 1;

    public static array $statusMap = [
        self::STATUS_SUCCESS => '成功',
        self::STATUS_FAILED => '失败',
    ];

    // 状态文本
    protected function statusText(): Attribute
    {
        return Attribute::make(
            get: fn () => self::$statusMap[$this->status] ?? '未知'
        );
    }

    // 关联管理员
    public function admin()
    {
        return $this->belongsTo(AdminUser::class);
    }
}
