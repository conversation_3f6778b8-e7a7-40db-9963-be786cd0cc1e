<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('users', function (Blueprint $table) {
            // _id（文档中提到系统自动生成，这里假设使用自增ID代替）
            $table->id();

            $table->uuid()
                  ->index();
            // username
            $table->string('username')
                  ->unique();

            // password（假设使用Laravel默认的密码哈希机制存储）
            $table->string('password');

            // password_secret_version
            $table->unsignedInteger('password_secret_version')
                  ->nullable();

            // nickname
            $table->string('nickname')
                  ->nullable();
            $table->unsignedTinyInteger('nickname_confirmed')
                  ->default(0);
            // gender
            $table->unsignedInteger('gender')
                  ->nullable()
                  ->default(0);

            // status
            $table->unsignedInteger('status')
                  ->default(0);

            // mobile
            $table->string('mobile')
                  ->nullable();

            // mobile_confirmed
            $table->unsignedInteger('mobile_confirmed')
                  ->nullable()
                  ->default(0);

            // email
            $table->string('email')
                  ->nullable();

            // email_confirmed
            $table->unsignedInteger('email_confirmed')
                  ->default(0);

            // avatar
            $table->string('avatar', 1000)
                  ->nullable();
            $table->unsignedInteger('avatar_confirmed')
                  ->default(0);
            $table->date('birthday')
                  ->nullable();
            // wx_unionid
            $table->string('wx_unionid')
                  ->nullable();

            // wx_openid（将其拆分为多个字段）
//            $table->string('wx_openid_app')->nullable();
//            $table->string('wx_openid_mp')->nullable();
//            $table->string('wx_openid_h5')->nullable();
//            $table->string('wx_openid_web')->nullable();
            $table->json('wx_openids')
                  ->nullable();

            // ali_openid
            $table->string('ali_openid')
                  ->nullable();

            // apple_openid
            $table->string('apple_openid')
                  ->nullable();

            // dcloud_appid（假设以JSON格式存储数组）
            $table->json('dcloud_appid')
                  ->nullable();

            // comment
            $table->string('comment')
                  ->nullable();

            // third_party（由于结构复杂，这里将其相关子结构拆分为多个字段）
//            $table->string('third_party_mp_weixin_session_key')
//                  ->nullable();
//            $table->string('third_party_app_weixin_access_token')
//                  ->nullable();
//            $table->string('third_party_app_weixin_access_token_expired')
//                  ->nullable();
//            $table->string('third_party_app_weixin_refresh_token')
//                  ->nullable();
//            $table->string('third_party_h5_weixin_access_token')
//                  ->nullable();
//            $table->string('third_party_h5_weixin_access_token_expired')
//                  ->nullable();
//            $table->string('third_party_h5_weixin_refresh_token')
//                  ->nullable();
//            $table->string('third_party_web_weixin_access_token')
//                  ->nullable();
//            $table->string('third_party_web_weixin_access_token_expired')
//                  ->nullable();
//            $table->string('third_party_web_weixin_refresh_token')
//                  ->nullable();
//            $table->string('third_party_mp_qq_session_key')
//                  ->nullable();
//            $table->string('third_party_app_qq_access_token')
//                  ->nullable();
//            $table->string('third_party_app_qq_access_token_expired')
//                  ->nullable();

            $table->json('third_party')
                  ->nullable();

            // register_env（将其拆分为多个字段）
            $table->string('register_env_appid')
                  ->nullable();
            $table->string('register_env_uni_platform')
                  ->nullable();
            $table->string('register_env_os_name')
                  ->nullable();
            $table->string('register_env_app_name')
                  ->nullable();
            $table->string('register_env_app_version')
                  ->nullable();
            $table->string('register_env_app_version_code')
                  ->nullable();
            $table->string('register_env_channel')
                  ->nullable();
            $table->string('register_env_client_ip')
                  ->nullable();

            $table->unsignedInteger('realname_auth_confirmed')
                  ->default(0)
                  ->nullable();

            $table->text('realname_auth_data')
                  ->nullable();
            // realname_auth（将其拆分为多个字段）
//            $table->integer('realname_auth_type')
//                  ->nullable();
//            $table->integer('realname_auth_auth_status')
//                  ->nullable();
//            $table->timestamp('realname_auth_auth_date')
//                  ->nullable();
//            $table->string('realname_auth_real_name')
//                  ->nullable();
//            $table->string('realname_auth_identity')
//                  ->nullable();
//            $table->string('realname_auth_id_card_front')
//                  ->nullable();
//            $table->string('realname_auth_id_card_back')
//                  ->nullable();
//            $table->string('realname_auth_in_hand')
//                  ->nullable();
//            $table->string('realname_auth_license')
//                  ->nullable();
//            $table->string('realname_auth_contact_person')
//                  ->nullable();
//            $table->string('realname_auth_contact_mobile')
//                  ->nullable();
//            $table->string('realname_auth_contact_email')
//                  ->nullable();

            // register_date（使用Laravel的时间戳类型）
            $table->timestamp('register_date')
                  ->nullable();

            // register_ip
            $table->string('register_ip')
                  ->nullable();

            // last_login_date
            $table->timestamp('last_login_date')
                  ->nullable();

            // last_login_ip
            $table->string('last_login_ip')
                  ->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')
                  ->primary();
            $table->string('token');
            $table->timestamp('created_at')
                  ->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')
                  ->primary();
            $table->foreignId('user_id')
                  ->nullable()
                  ->index();
            $table->string('ip_address', 45)
                  ->nullable();
            $table->text('user_agent')
                  ->nullable();
            $table->longText('payload');
            $table->integer('last_activity')
                  ->index();
        });

        Schema::create('tags', function (Blueprint $table) {
            $table->id();
            $table->string('name')
                  ->index();
            $table->string('description')
                  ->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('user_tags', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')
                  ->index();
            $table->foreignId('tag_id')
                  ->index();
            $table->timestamps();
        });

        Schema::create('groups', function (Blueprint $table) {
            $table->id();
            $table->string('name')
                  ->index();
            $table->string('description')
                  ->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('user_groups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')
                  ->index();
            $table->foreignId('group_id')
                  ->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('tags');
        Schema::dropIfExists('user_tags');
        Schema::dropIfExists('groups');
        Schema::dropIfExists('user_groups');
    }
};
