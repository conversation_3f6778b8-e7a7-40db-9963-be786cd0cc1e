<?php

namespace App\Services\CloudFiles\Providers;

use Obs\ObsClient;
use Obs\ObsException;
use App\Services\CloudFiles\Exceptions\CloudFilesException;

/**
 * 华为云OBS存储服务实现类
 */
class Obs extends AbstractCloudFiles
{
    /**
     * OBS客户端实例
     * @var ObsClient
     */
    protected $client;

    /**
     * 结果映射关系
     * @var array
     */
    protected static $resultMap = [
        'Body' => 'raw_contents',
        'ContentLength' => 'size',
        'ContentType' => 'mimetype',
        'Size' => 'size',
        'StorageClass' => 'storage_class',
        'LastModified' => 'timestamp',
        'ETag' => 'etag',
    ];

    /**
     * 默认上传选项
     * @var array
     */
    protected $defaultOptions = [
        'PartSize' => 10 * 1024 * 1024, // 每个分片10MB
    ];

    /**
     * 初始化OBS客户端
     *
     * @return $this
     * @throws CloudFilesException
     */
    protected function initializeClient()
    {
        try {
            $this->client = new ObsClient([
                'key' => $this->getConfig('access_key'),
                'secret' => $this->getConfig('secret_key'),
                'endpoint' => $this->getConfig('endpoint'),
                'ssl_verify' => $this->getConfig('ssl', false),
                'max_retry_count' => 3,
                'socket_timeout' => 30,
                'connect_timeout' => 10
            ]);

            $this->debug('OBS客户端初始化成功', [
                'endpoint' => $this->getConfig('endpoint'),
                'bucket' => $this->getConfig('bucket')
            ]);

            return $this;
        } catch (ObsException $e) {
            $this->handleException($e, '初始化客户端');
        }
    }

    /**
     * 获取文件完整路径
     */
    public function getPath(string $path): string
    {
        return $this->removePathPrefix($path);
    }

    /**
     * 获取文件访问URL
     */
    public function getUrl(string $path): string
    {
        $cdnDomain = $this->getConfig('cdndomain');
        $ssl = $this->getConfig('ssl', false);
        $endpoint = $this->getConfig('endpoint');

        $domain = rtrim($cdnDomain ?: ($ssl ? 'https://' : 'http://') . $endpoint, '/');
        return $domain . '/' . $this->removePathPrefix($path);
    }

    /**
     * 获取临时访问URL
     */
    public function signatureUrl(string $path, int $expire = 3600, array $options = []): string
    {
        try {
            $response = $this->client->createSignedUrl([
                'Method' => 'GET',
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($path),
                'Expires' => $expire
            ]);

            return $response['SignedUrl'];
        } catch (ObsException $e) {
            $this->handleException($e, '生成签名URL');
        }
    }

    /**
     * 获取上传策略
     * 用于客户端直传场景
     *
     * @param string $callbackRoute 回调路由
     * @param string $dir 目录
     * @param int $expire 过期时间(秒)
     * @param int $contentLengthRange 内容长度范围
     *
     * @return array
     */
    public function policy(string $callbackRoute = '', string $dir = '', int $expire = 300, int $contentLengthRange = 1048576000): array
    {
        $dir = ltrim($dir, '/');
        $expiration = time() + $expire;
        $bucket = $this->getConfig('bucket');
        $accessKey = $this->getConfig('access_key');
        $secretKey = $this->getConfig('secret_key');
        $endpoint = $this->getConfig('endpoint');

        // 构建策略
        $conditions = [];
        $conditions[] = ['bucket' => $bucket];

        if ($dir) {
            $conditions[] = ['starts-with', '$key', $dir];
        }

        // 添加文件大小限制
        $conditions[] = ['content-length-range', 0, $contentLengthRange];

        // 如果有回调配置
        if ($callbackRoute) {
            $callback = [
                'callbackUrl' => $callbackRoute,
                'callbackBody' => 'bucket=${bucket}&object=${object}&etag=${etag}&size=${size}&mimeType=${mimeType}',
                'callbackBodyType' => 'application/x-www-form-urlencoded'
            ];
            $conditions[] = ['eq', '$callback', base64_encode(json_encode($callback))];
        }

        $policy = [
            'expiration' => gmdate('Y-m-d\TH:i:s\Z', $expiration),
            'conditions' => $conditions
        ];

        $policyBase64 = base64_encode(json_encode($policy));

        // 计算签名
        $signature = base64_encode(hash_hmac('sha1', $policyBase64, $secretKey, true));

        $host = ($this->getConfig('ssl', false) ? 'https://' : 'http://') . ($this->getConfig('cdndomain') ?: $this->getConfig('endpoint'));

        return [
            'policy' => $policyBase64,
            'signature' => $signature,
            'AccessKeyId' => $accessKey,
            'key' => $dir . '${filename}',
            'host' => $host,
            'dir' => $dir,
            'expire' => $expiration,
            'callback' => $callbackRoute ? base64_encode(json_encode($callback)) : ''
        ];
    }

    /**
     * 验证上传回调
     * 验证 OBS 回调请求的合法性
     * 参考: https://support.huaweicloud.com/api-obs/obs_04_0111.html
     *
     * @return bool
     * @throws CloudFilesException
     */
    public function verify(): bool
    {
        // 获取回调头部信息
        $authType = $_SERVER['HTTP_AUTH_TYPE'] ?? '';
        $signature = $_SERVER['HTTP_AUTH_INFO'] ?? '';
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $queryString = $_SERVER['QUERY_STRING'] ?? '';

        if (!$signature || $authType !== 'OBS') {
            throw new CloudFilesException('缺少OBS回调验证信息');
        }

        // 获取请求body
        $body = file_get_contents('php://input');

        // 构建规范请求串
        $canonicalRequest = $this->buildCanonicalRequest($requestUri, $queryString, $body);

        // 使用SK计算签名
        $calculatedSignature = base64_encode(
            hash_hmac('sha1', $canonicalRequest, $this->getConfig('secret_key'), true)
        );

        // 比对签名
        return $calculatedSignature === $signature;
    }

    /**
     * 构建规范请求串
     *
     * @param string $requestUri 请求URI
     * @param string $queryString 查询字符串
     * @param string $body 请求体
     * @return string
     */
    protected function buildCanonicalRequest(string $requestUri, string $queryString, string $body): string
    {
        // 移除查询字符串
//        $path = parse_url($requestUri, PHP_URL_PATH);
        $path = $requestUri;

        // 组装规范请求串
        $canonicalRequest = "POST\n";  // HTTP Method
        $canonicalRequest .= $path . "\n";  // URI Path
        $canonicalRequest .= $queryString . "\n";  // Query String
        $canonicalRequest .= $body;  // Request Body

        return $canonicalRequest;
    }

    /**
     * 写入文件
     */
    public function write(string $path, $contents, ?array $config = null): bool
    {
        try {
            if (is_resource($contents)) {
                $contents = stream_get_contents($contents);
            }

            $options = [];
            if ($config) {
                $options['ContentType'] = $config['ContentType'] ?? null;
            }

            $this->client->putObject([
                    'Bucket' => $this->getConfig('bucket'),
                    'Key' => $this->removePathPrefix($path),
                    'Body' => $contents,
                ] + $options);

            return true;
        } catch (ObsException $e) {
            $this->handleException($e, '写入文件');
        }
    }

    /**
     * 使用流写入文件
     */
    public function writeStream(string $path, $resource, ?array $config = null): bool
    {
        return $this->write($path, $resource, $config);
    }

    /**
     * 写入本地文件
     */
    public function writeFile(string $path, string $filePath, ?array $config = null): bool
    {
        try {
            $this->client->putObject([
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($path),
                'SourceFile' => $filePath
            ]);

            return true;
        } catch (ObsException $e) {
            $this->handleException($e, '上传本地文件');
        }
    }

    /**
     * Base64写入文件
     */
    public function base64(string $path, string $base64Data): bool
    {
        if (preg_match('/^data:\s*image\/(\w+);base64,/', $base64Data, $matches)) {
            $contents = base64_decode(substr($base64Data, strpos($base64Data, ',') + 1));
            return $this->write($path, $contents);
        }

        throw new CloudFilesException('无效的Base64图片数据');
    }

    /**
     * 删除文件
     */
    public function delete($path): bool
    {
        try {
            if (is_array($path)) {
                $objects = array_map(function($key) {
                    return ['Key' => $this->removePathPrefix($key)];
                }, $path);

                $this->client->deleteObjects([
                    'Bucket' => $this->getConfig('bucket'),
                    'Objects' => $objects
                ]);
            } else {
                $this->client->deleteObject([
                    'Bucket' => $this->getConfig('bucket'),
                    'Key' => $this->removePathPrefix($path)
                ]);
            }

            return true;
        } catch (ObsException $e) {
            $this->handleException($e, '删除文件');
        }
    }

    /**
     * 复制文件
     */
    public function copy(string $path, string $newPath): bool
    {
        try {
            $this->client->copyObject([
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($newPath),
                'CopySource' => $this->getConfig('bucket') . '/' . $this->removePathPrefix($path)
            ]);

            return true;
        } catch (ObsException $e) {
            $this->handleException($e, '复制文件');
        }
    }

    /**
     * 移动/重命名文件
     */
    public function rename(string $path, string $newPath): bool
    {
        if (!$this->copy($path, $newPath)) {
            return false;
        }

        return $this->delete($path);
    }

    /**
     * 判断文件是否存在
     */
    public function has(string $path): bool
    {
        try {
            $this->client->getObjectMetadata([
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($path)
            ]);

            return true;
        } catch (ObsException $e) {
            return false;
        }
    }

    /**
     * 读取文件
     */
    public function read(string $path): array
    {
        try {
            $result = $this->client->getObject([
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($path)
            ]);

            return $this->normalizeResponse([
                'contents' => $result['Body'],
                'path' => $path,
                'type' => 'file'
            ], $path);
        } catch (ObsException $e) {
            $this->handleException($e, '读取文件');
        }
    }

    /**
     * 读取文件流
     */
    public function readStream(string $path): array
    {
        $result = $this->read($path);
        $stream = fopen('php://temp', 'r+');
        fwrite($stream, $result['contents']);
        rewind($stream);

        $result['stream'] = $stream;
        unset($result['contents']);

        return $result;
    }

    /**
     * 列出目录内容
     */
    public function listContents(string $dirname = '', bool $recursive = false): array
    {
        try {
            $dirname = rtrim($dirname, '/') . '/';

            $options = [
                'Bucket' => $this->getConfig('bucket'),
                'Delimiter' => $recursive ? '' : '/',
                'Prefix' => $dirname,
                'MaxKeys' => 1000
            ];

            $result = $this->client->listObjects($options);

            $contents = [];

            // 处理文件
            foreach ($result['Contents'] ?? [] as $object) {
                $contents[] = $this->normalizeResponse([
                    'Key' => $object['Key'],
                    'LastModified' => $object['LastModified'],
                    'ETag' => $object['ETag'],
                    'Size' => $object['Size'],
                    'StorageClass' => $object['StorageClass'],
                    'type' => 'file'
                ], $object['Key']);
            }

            // 处理目录
            if (!$recursive) {
                foreach ($result['CommonPrefixes'] ?? [] as $prefix) {
                    $contents[] = [
                        'path' => $prefix['Prefix'],
                        'type' => 'dir'
                    ];
                }
            }

            return array_filter($contents, function ($item) {
                return !empty($item['path']);
            });
        } catch (ObsException $e) {
            $this->handleException($e, '列出目录内容');
        }
    }

    /**
     * 获取文件元数据
     */
    public function getMetadata(string $path): array
    {
        try {
            $result = $this->client->getObjectMetadata([
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($path)
            ]);

            return $this->normalizeResponse($result, $path);
        } catch (ObsException $e) {
            $this->handleException($e, '获取文件元数据');
        }
    }

    /**
     * 创建目录 (OBS是对象存储,不需要实际创建目录)
     */
    public function createDir(string $dirname): bool
    {
        try {
            $dirname = rtrim($this->removePathPrefix($dirname), '/') . '/';

            return $this->write($dirname, '');
        } catch (ObsException $e) {
            $this->handleException($e, '创建目录');
        }
    }

    /**
     * 删除目录
     */
    public function deleteDir(string $dirname): bool
    {
        try {
            $dirname = rtrim($this->removePathPrefix($dirname), '/') . '/';

            // 列出目录下的所有文件
            $contents = $this->listContents($dirname, true);

            if (!empty($contents)) {
                $files = array_map(function ($item) {
                    return $item['path'];
                }, $contents);

                // 分批删除文件
                foreach (array_chunk($files, 100) as $chunk) {
                    $this->delete($chunk);
                }
            }

            // 删除目录标记
            return $this->delete($dirname);
        } catch (ObsException $e) {
            $this->handleException($e, '删除目录');
        }
    }

    /**
     * 设置文件访问权限
     */
    public function setVisibility(string $path, string $visibility): bool
    {
        try {
            $this->client->setObjectAcl([
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($path),
                'ACL' => $this->normalizeVisibility($visibility)
            ]);

            return true;
        } catch (ObsException $e) {
            $this->handleException($e, '设置访问权限');
        }
    }

    /**
     * 获取文件访问权限
     */
    public function getVisibility(string $path): array
    {
        try {
            $result = $this->client->getObjectAcl([
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($path)
            ]);

            return [
                'visibility' => $this->normalizeVisibilityResult($result['Grants'][0]['Grantee']['URI'] ?? '')
            ];
        } catch (ObsException $e) {
            $this->handleException($e, '获取访问权限');
        }
    }

    /**
     * 分片上传
     */
    public function multiUpload(string $path, string $filePath, array $options = []): bool|string
    {
        try {
            $options = array_merge($this->defaultOptions, $options);

            $result = $this->client->uploadFile([
                'Bucket' => $this->getConfig('bucket'),
                'Key' => $this->removePathPrefix($path),
                'SourceFile' => $filePath,
                'PartSize' => $options['PartSize']
            ]);

            if ($result['HttpStatusCode'] === 200) {
                return $this->getUrl($path);
            }

            return false;
        } catch (ObsException $e) {
            $this->handleException($e, '分片上传');
        }
    }

    /**
     * 格式化访问权限
     */
    protected function normalizeVisibility(string $visibility): string
    {
        return match ($visibility) {
            'public' => ObsClient::AclPublicRead,
            default => ObsClient::AclPrivate,
        };
    }

    /**
     * 格式化访问权限结果
     */
    protected function normalizeVisibilityResult(string $visibility): string
    {
        return match ($visibility) {
            'http://acs.amazonaws.com/groups/global/AllUsers' => 'public',
            default => 'private',
        };
    }

    /**
     * 创建软链接
     * 注意: OBS不支持软链接操作
     */
    public function createSymlink(string $path, string $target): bool
    {
        throw new CloudFilesException('OBS不支持软链接操作');
    }

    /**
     * 读取软链接
     * 注意: OBS不支持软链接操作
     */
    public function readSymlink(string $path): ?string
    {
        throw new CloudFilesException('OBS不支持软链接操作');
    }

    /**
     * 更新文件内容
     */
    public function update(string $path, $contents, ?array $config = null): bool
    {
        return $this->write($path, $contents, $config);
    }

    /**
     * 使用流更新文件
     */
    public function updateStream(string $path, $resource, ?array $config = null): bool
    {
        return $this->writeStream($path, $resource, $config);
    }

    /**
     * 异常处理
     */
    protected function handleException(\Throwable $e, string $action): void
    {
        $context = [
            'exception' => get_class($e),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'code' => $e->getCode(),
            'bucket' => $this->getConfig('bucket'),
            'endpoint' => $this->getConfig('endpoint'),
        ];

        if ($e instanceof ObsException) {
            $context['request_id'] = $e->getRequestId();
            $context['error_code'] = $e->getErrorCode();
        }

        $message = sprintf('[%s] %s失败: %s', get_class($this), $action, $e->getMessage());

        if ($this->debug) {
            $this->debug($message, $context);
        }

        throw new CloudFilesException($message, $e->getCode(), $e);
    }
}
