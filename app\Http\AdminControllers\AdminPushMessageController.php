<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Http\Resources\Admins\PushMessageResource;
use App\Models\AdminUser;
use App\Models\Client;
use App\Models\OauthClientSubscription;
use App\Models\PushTemplate;
use App\Services\PushMessageService;
use App\Utils\OAuthCacher;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class AdminPushMessageController extends AdminBaseController
{
    protected $validationMessages = [
        'template_code.required' => '模板代码不能为空',
        'template_code.string' => '模板代码必须是字符串',
        'template_code.max' => '模板代码不能超过50个字符',
        'client_id.required' => '应用ID不能为空',
        'client_id.exists' => '应用不存在',
        'to_open_id.required' => '目标用户ID不能为空',
        'to_open_id.string' => '目标用户ID必须是字符串',
        'template_params.array' => '模板参数必须是数组',
        'extend_params.array' => '扩展参数必须是数组',
        'extend_params.max' => '扩展参数不能超过5个',
        'redirect_to.string' => '跳转链接必须是字符串',
        'subscription_code.required' => '订阅代码不能为空',
        'subscription_code.string' => '订阅代码必须是字符串',
        'subscription_code.max' => '订阅代码不能超过50个字符',
        'name.required' => '订阅名称不能为空',
        'name.string' => '订阅名称必须是字符串',
        'name.max' => '订阅名称不能超过100个字符',
        'description.string' => '订阅描述必须是字符串',
        'description.max' => '订阅描述不能超过255个字符',
        'push_template_id.required' => '推送模板ID不能为空',
        'push_template_id.exists' => '推送模板不存在或已被禁用',
    ];

    protected const PERMISSION_MAP = [
        'pushToUser' => '消息推送.推送个人消息',
        'broadcast' => '消息推送.广播消息',
        'pushByClient' => '消息推送.应用推送',
        'createSubscription' => '消息推送.创建订阅',
        'triggerSubscription' => '消息推送.触发订阅',
    ];

    /**
     * 推送消息给指定用户（系统级别）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function pushToUser(Request $request)
    {
        $validated = $request->validate([
            'template_code' => 'required|string|max:50',
            'to_open_id' => 'required|string',
            'template_params' => 'array|nullable',
            'extend_params' => 'array|nullable|max:5',
            'redirect_to' => 'string|nullable',
        ], $this->validationMessages);

        $pushService = app(PushMessageService::class);
        
        try {
            // 直接使用系统默认客户端ID
            $message = $pushService->pushToUserWithClient(
                $validated['template_code'],
                $validated['to_open_id'],
                config('uc.system_client_id'),
                $validated['template_params'] ?? null,
                $validated['extend_params'] ?? null,
                $validated['redirect_to'] ?? null
            );
            
            return Respond::success(new PushMessageResource($message));
        } catch (\Exception $e) {
            return Respond::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 广播消息（系统级别）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function broadcast(Request $request)
    {
        $validated = $request->validate([
            'template_code' => 'required|string|max:50',
            'template_params' => 'array|nullable',
            'extend_params' => 'array|nullable|max:5',
            'redirect_to' => 'string|nullable',
        ], $this->validationMessages);

        $pushService = app(PushMessageService::class);
        
        try {
            // 直接使用系统默认客户端ID
            $message = $pushService->broadcastWithClient(
                $validated['template_code'],
                config('uc.system_client_id'),
                $validated['template_params'] ?? null,
                $validated['extend_params'] ?? null,
                $validated['redirect_to'] ?? null
            );
            
            return Respond::success(new PushMessageResource($message));
        } catch (\Exception $e) {
            return Respond::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 应用推送给指定用户（需要验证管理员是否有应用管理权限）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function pushByClient(Request $request)
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,client_key',
            'template_code' => 'required|string|max:50',
            'to_open_id' => 'required|string',
            'template_params' => 'array|nullable',
            'extend_params' => 'array|nullable|max:5',
            'redirect_to' => 'string|nullable',
        ], $this->validationMessages);

        // 确定要使用的客户端ID
        $clientId = $validated['client_id'];
        $isSystemDefaultClient = (string)$clientId === (string)config('uc.system_client_id');
        
        // 如果不是系统默认客户端，需要验证权限
        if (!$isSystemDefaultClient) {
            // 检查管理员是否有该应用的管理权限
            /** @var AdminUser $admin */
            $admin = Auth::guard('admin')->user();
            
            $client = Client::where('client_key', $clientId)->first();
            if (!$client) {
                throw new AdminException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
            }
            
            if (!$admin->canManageOauthClient($client->id)) {
                throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
            }

            // 检查模板是否存在并且属于该应用
            $this->validateTemplateForClient($validated['template_code'], $client->id);
        }

        $pushService = app(PushMessageService::class);
        
        try {
            $message = $pushService->pushToUserWithClient(
                $validated['template_code'],
                $validated['to_open_id'],
                $clientId,
                $validated['template_params'] ?? null,
                $validated['extend_params'] ?? null,
                $validated['redirect_to'] ?? null
            );
            
            return Respond::success(new PushMessageResource($message));
        } catch (\Exception $e) {
            return Respond::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 创建应用订阅配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createSubscription(Request $request)
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,client_key',
            'subscription_code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('oauth_client_subscriptions', 'subscription_code')
                    ->where('oauth_client_id', OAuthCacher::getClientIdByKey($request->input('client_id')))
            ],
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:255',
            'push_template_id' => [
                'required',
                'exists:push_templates,id',
                function ($attribute, $value, $fail) {
                    $template = PushTemplate::find($value);
                    if (!$template || $template->status != PushTemplate::STATUS_ENABLE) {
                        $fail('模板不可用');
                    }
                },
            ],
            'filter_conditions' => 'nullable|array',
        ], $this->validationMessages);

        // 检查管理员是否有该应用的管理权限
        /** @var AdminUser $admin */
        $admin = Auth::guard('admin')->user();
        
        $client = Client::where('client_key', $validated['client_id'])->first();
        if (!$client) {
            throw new AdminException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }
        
        if (!$admin->canManageOauthClient($client->id)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        try {
            DB::beginTransaction();

            $subscription = new OauthClientSubscription([
                'subscription_code' => $validated['subscription_code'],
                'oauth_client_id' => $client->id,
                'name' => $validated['name'],
                'description' => $validated['description'] ?? '',
                'push_template_id' => $validated['push_template_id'],
                'filter_conditions' => $validated['filter_conditions'] ?? [],
                'status' => OauthClientSubscription::STATUS_ENABLE,
            ]);

            $subscription->save();

            DB::commit();

            return Respond::success($subscription);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 触发订阅推送
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function triggerSubscription(Request $request)
    {
        $validated = $request->validate([
            'client_id' => 'nullable|exists:clients,client_key',
            'subscription_code' => 'required|string|max:50',
            'template_params' => 'array|nullable',
            'extend_params' => 'array|nullable|max:5',
            'redirect_to' => 'string|nullable',
        ], $this->validationMessages);

        // 确定要使用的客户端ID
        $clientId = $validated['client_id'] ?? config('uc.system_client_id');
        $isSystemDefaultClient = (string)$clientId === (string)config('uc.system_client_id');
        
        // 如果不是系统默认客户端，需要验证权限
        if (!$isSystemDefaultClient) {
            $client = Client::where('client_key', $clientId)->first();
            if (!$client) {
                throw new AdminException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
            }

            // 检查管理员是否有该应用的管理权限
            /** @var AdminUser $admin */
            $admin = Auth::guard('admin')->user();
            
            if (!$admin->canManageOauthClient($client->id)) {
                throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
            }

            // 检查订阅是否存在并且属于该应用
            $subscription = OauthClientSubscription::where('oauth_client_id', $client->id)
                ->where('subscription_code', $validated['subscription_code'])
                ->where('status', OauthClientSubscription::STATUS_ENABLE)
                ->first();
            
            if (!$subscription) {
                throw new AdminException(ErrorCodeEnum::PUSH_SUBSCRIPTION_NOT_FOUND);
            }
        }

        $pushService = app(PushMessageService::class);
        
        try {
            $message = $pushService->triggerSubscriptionWithClient(
                $validated['subscription_code'],
                $clientId,
                $validated['template_params'] ?? null,
                $validated['extend_params'] ?? null,
                $validated['redirect_to'] ?? null
            );
            
            return Respond::success(new PushMessageResource($message));
        } catch (\Exception $e) {
            return Respond::error($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 验证模板是否存在并且属于该应用
     *
     * @param string $templateCode
     * @param int $clientId
     * @throws AdminException
     */
    private function validateTemplateForClient(string $templateCode, int $clientId)
    {
        // 检查是否为系统默认client_id
        $isSystemDefaultClient = (string)$clientId === (string)config('uc.system_client_id');
        
        // 如果是系统默认client_id，跳过验证
        if ($isSystemDefaultClient) {
            return;
        }
            
        // 先查找客户端模板
        $clientTemplate = \App\Models\OauthClientTemplate::where('template_code', $templateCode)
            ->where('oauth_client_id', $clientId)
            ->first();

        if ($clientTemplate) {
            if ($clientTemplate->status !== \App\Models\OauthClientTemplate::STATUS_ENABLE) {
                throw new AdminException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
            }
            return;
        }

        // 如果没有找到客户端模板，查找全局模板
        $template = PushTemplate::where('code', $templateCode)
            ->where('status', PushTemplate::STATUS_ENABLE)
            ->first();

        if (!$template) {
            throw new AdminException(ErrorCodeEnum::PUSH_TEMPLATE_NOT_AVAILABLE);
        }
    }
} 