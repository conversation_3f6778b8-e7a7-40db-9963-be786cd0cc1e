<?php

namespace App\Services;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Models\Client;
use App\Models\Enterprise;
use App\Models\EnterpriseAdmin;
use App\Utils\OAuthCacher;
use Illuminate\Support\Facades\DB;
use Rtgm\sm\RtSm2;

class EnterpriseClientService
{
    /**
     * 创建企业应用
     */
    public function createEnterpriseClient(array $data, string $enterpriseId, ?string $createdBy = null): Client
    {
        $enterprise = Enterprise::find($enterpriseId);
        if (!$enterprise) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '企业不存在');
        }

        if (!$enterprise->isActive()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '企业已被禁用');
        }

        return DB::transaction(function () use ($data, $enterpriseId, $createdBy) {
            // 生成SM2密钥对
            $sm2 = new RtSm2();
            [$privateKey, $publicKey] = $sm2->generatekey();

            $clientData = array_merge($data, [
                'enterprise_id' => $enterpriseId,
                'client_access_key' => $publicKey,
                'client_access_secret' => $privateKey,
                'status' => Client::AUDIT_STATUS_PENDING, // 企业创建的应用默认待审核
                'created_by' => $createdBy,
            ]);

            $client = Client::create($clientData);

            return $client;
        });
    }

    /**
     * 审核应用
     */
    public function auditClient(string $clientId, int $status, ?string $reviewBy = null, ?string $remark = null): Client
    {
        $client = Client::find($clientId);
        if (!$client) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '应用不存在');
        }

        if (!$client->isEnterpriseClient()) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '只能审核企业应用');
        }

        if ($client->status !== Client::AUDIT_STATUS_PENDING) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '应用不是待审核状态');
        }

        if (!in_array($status, [Client::AUDIT_STATUS_APPROVED, Client::AUDIT_STATUS_REJECTED])) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '无效的审核状态');
        }

        return DB::transaction(function () use ($client, $status, $reviewBy, $remark) {
            $client->status = $status;
            $client->review_by = $reviewBy;
            $client->review_at = now();
            $client->review_remark = $remark;
            $client->save();

            // 如果审核通过，预加载客户端缓存
            if ($status === Client::AUDIT_STATUS_APPROVED) {
                OAuthCacher::preloadClients([$client->client_key], [$client->id]);
            }

            return $client;
        });
    }

    /**
     * 批量审核应用
     */
    public function batchAuditClients(array $clientIds, int $status, ?string $reviewBy = null, ?string $remark = null): int
    {
        if (!in_array($status, [Client::AUDIT_STATUS_APPROVED, Client::AUDIT_STATUS_REJECTED])) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '无效的审核状态');
        }

        return DB::transaction(function () use ($clientIds, $status, $reviewBy, $remark) {
            $clients = Client::whereIn('id', $clientIds)
                           ->where('status', Client::AUDIT_STATUS_PENDING)
                           ->get();

            $updatedCount = 0;
            foreach ($clients as $client) {
                if ($client->isEnterpriseClient()) {
                    $client->status = $status;
                    $client->review_by = $reviewBy;
                    $client->review_at = now();
                    $client->review_remark = $remark;
                    $client->save();

                    // 如果审核通过，预加载客户端缓存
                    if ($status === Client::AUDIT_STATUS_APPROVED) {
                        OAuthCacher::preloadClients([$client->client_key], [$client->id]);
                    }

                    $updatedCount++;
                }
            }

            return $updatedCount;
        });
    }

    /**
     * 为应用分配企业管理员
     */
    public function assignAdminToClient(string $clientId, string $adminId, bool $isOwner = false): bool
    {
        $client = Client::find($clientId);
        if (!$client) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '应用不存在');
        }

        $admin = EnterpriseAdmin::find($adminId);
        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '企业管理员不存在');
        }

        // 检查是否属于同一企业
        if ($client->enterprise_id !== $admin->enterprise_id) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '应用和管理员不属于同一企业');
        }

        return DB::transaction(function () use ($client, $admin, $isOwner) {
            // 如果是设置为拥有者
            if ($isOwner) {
                // 检查是否已有拥有者
                $existingOwner = DB::table('admin_oauth_client')
                                  ->where('oauth_client_id', $client->id)
                                  ->where('is_owner', 1)
                                  ->first();

                if ($existingOwner && $existingOwner->admin_uuid !== $admin->id) {
                    throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '该应用已有拥有者');
                }

                // 设置拥有者关系
                DB::table('admin_oauth_client')->updateOrInsert(
                    [
                        'admin_uuid' => $admin->id,
                        'oauth_client_id' => $client->id,
                    ],
                    [
                        'is_owner' => 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                );
            }

            // 设置管理关系
            if (!$admin->clients()->where('client_id', $client->id)->exists()) {
                $admin->clients()->attach($client->id, [
                    'status' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            return true;
        });
    }

    /**
     * 移除应用的企业管理员
     */
    public function removeAdminFromClient(string $clientId, string $adminId): bool
    {
        $client = Client::find($clientId);
        if (!$client) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '应用不存在');
        }

        $admin = EnterpriseAdmin::find($adminId);
        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '企业管理员不存在');
        }

        return DB::transaction(function () use ($client, $admin) {
            // 移除管理关系
            $admin->clients()->detach($client->id);

            // 移除拥有者关系
            DB::table('admin_oauth_client')
                ->where('admin_uuid', $admin->id)
                ->where('oauth_client_id', $client->id)
                ->delete();

            return true;
        });
    }

    /**
     * 获取企业的所有应用
     */
    public function getEnterpriseClients(string $enterpriseId, ?int $status = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = Client::where('enterprise_id', $enterpriseId);

        if ($status !== null) {
            $query->where('status', $status);
        }

        return $query->orderByDesc('created_at')->get();
    }

    /**
     * 获取待审核的应用列表
     */
    public function getPendingClients(?string $enterpriseId = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = Client::pending()->with(['enterprise']);

        if ($enterpriseId) {
            $query->where('enterprise_id', $enterpriseId);
        }

        return $query->orderByDesc('created_at')->get();
    }

    /**
     * 重新生成应用密钥
     */
    public function regenerateClientKeys(string $clientId): Client
    {
        $client = Client::find($clientId);
        if (!$client) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '应用不存在');
        }

        return DB::transaction(function () use ($client) {
            // 清除旧的客户端缓存
            OAuthCacher::forgetClient($client->client_key);

            // 生成新的SM2密钥对
            $sm2 = new RtSm2();
            [$privateKey, $publicKey] = $sm2->generatekey();

            $client->client_access_key = $publicKey;
            $client->client_access_secret = $privateKey;
            $client->save();

            // 如果应用已审核通过，重新加载缓存
            if ($client->status === Client::AUDIT_STATUS_APPROVED) {
                OAuthCacher::preloadClients([$client->client_key], [$client->id]);
            }

            return $client;
        });
    }

    /**
     * 检查应用是否可以删除
     */
    public function canDeleteClient(string $clientId): bool
    {
        $client = Client::find($clientId);
        if (!$client) {
            return false;
        }

        // 检查是否有活跃的授权
        $hasActiveAuthorizations = $client->userAuthorizations()
                                          ->active()
                                          ->exists();

        // 检查是否有活跃的token
        $hasActiveTokens = $client->tokens()
                                 ->where('expires_at', '>', now())
                                 ->exists();

        return !$hasActiveAuthorizations && !$hasActiveTokens;
    }

    /**
     * 删除企业应用
     */
    public function deleteClient(string $clientId): bool
    {
        $client = Client::find($clientId);
        if (!$client) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '应用不存在');
        }

        if (!$this->canDeleteClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '应用还有活跃的授权或token，无法删除');
        }

        return DB::transaction(function () use ($client) {
            // 清除客户端缓存
            OAuthCacher::forgetClient($client->client_key);

            // 删除关联关系
            DB::table('admin_oauth_client')
                ->where('oauth_client_id', $client->id)
                ->delete();

            DB::table('enterprise_admin_clients')
                ->where('client_id', $client->id)
                ->delete();

            // 删除应用
            return $client->delete();
        });
    }
}
