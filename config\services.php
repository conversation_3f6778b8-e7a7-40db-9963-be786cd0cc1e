<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'easy-sms' => [
        'timeout' => 5,
        'default' => [
            'strategy' => \Overtrue\EasySms\Strategies\OrderStrategy::class,
            'gateways' => [
                'aliyun',
            ],
        ],
        'gateways' => [
            'errorlog' => [
                'file' => storage_path('logs/sms.log'),
            ],
            'aliyun' => [
                'access_key_id' => env('ALISMS_ACCESS_KEY', ''),
                'access_key_secret' => env('ALISMS_ACCESS_SECRET', ''),
                'sign_name' => env('ALISMS_SIGN_NAME'),
            ],
        ],
    ],


    'socialite' => [
        'wechat' => [
            'client_id' => env('SOCIALITE_WECHAT_APPKEY', ''),
            'client_secret' => env('SOCIALITE_WECHAT_APPSECRET', ''),
            'redirect' => env('SOCIALITE_WECHAT_REDIRECT', ''),
            // Open Platform - Third-party Platform Need
            //            'component' => [
            //                // or 'app_id', 'component_app_id' as key
            //                'id' => env('SOCIALITE_WECHAT_COMPONENT_ID', ''),
            //                // or 'app_token', 'access_token', 'component_access_token' as key
            //                'token' => env('SOCIALITE_WECHAT_COMPONENT_TOKEN', ''),
            //            ],
        ],
        'wx_mp' => [
            'client_id' => env('SOCIALITE_WECHAT_MP_APPKEY', ''),
            'client_secret' => env('SOCIALITE_WECHAT_MP_APPSECRET', ''),
            'redirect' => env('SOCIALITE_WECHAT_MP_REDIRECT', ''),
            'provider' => 'wechat',
        ],
        //        'weibo' => [
        //            'client_id' => 'your-app-id',
        //            'client_secret' => 'your-app-secret',
        //            'redirect' => 'http://localhost/socialite/callback.php',
        //        ],
        //        'alipay' => [
        //            // the key, pointed by the key value of this array, can also be named as 'app_id' like the official documentation.
        //            'client_id' => 'your-app-id',
        //
        //            // Please refer to the official documentation, in the official management background configuration RSA2.
        //            // Note: This is your own private key.
        //            // Note: Do not allow the private key content to have extra characters.
        //            // Recommendation: For security, you can read directly from the file. But here as long as the value, please remember to remove the head and tail of the decoration.
        //            'rsa_private_key' => 'your-rsa-private-key',
        //
        //            // Be sure to set this value and make sure that it is the same address value as set in the official admin system.
        //            // the key, pointed by the key value of this array, can also be named as 'redirect_url' like the official documentation.
        //            'redirect' => 'http://localhost/socialite/callback.php',
        //        ],
        //        'dingtalk' => [
        //            // or 'app_id'
        //            'client_id' => 'your app id',
        //
        //            // or 'app_secret'
        //            'client_secret' => 'your app secret',
        //
        //            // or 'redirect_url'
        //            'redirect' => 'redirect URL',
        //        ],
        //        'douyin' => [
        //            // or 'app_id'
        //            'client_id' => 'your app id',
        //
        //            // or 'app_secret'
        //            'client_secret' => 'your app secret',
        //
        //            // or 'redirect_url'
        //            'redirect' => 'redirect URL',
        //        ],
    ],

    'realname_auth' => [
        'aliyun' => [
            'access_key_id' => env('ALIYUN_FACEVERIFY_ACCESS_KEY_ID', ''),
            'access_secret' => env('ALIYUN_FACEVERIFY_ACCESS_SECRET', ''),
            'endpoint' => env('ALIYUN_FACEVERIFY_ENDPOINT', ''),
            'scene_id' => env('ALIYUN_FACEVERIFY_SCENE_ID', ''),
        ],
    ],
];
