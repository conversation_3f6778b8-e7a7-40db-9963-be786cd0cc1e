<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InitPushTemplateSeeder extends Seeder
{
    protected array $channelProperties = [
        // 系统通知类配置
        'system' => [
            'xiaomi_channel_id' => '132246', // 常观活动通知
            'vivo_category' => 'ACCOUNT',
            'oppo_channel_id' => 'upush_default', // 私信频道
            'oppo_category' => 'ACCOUNT',
            'huawei_channel_category' => 'ACCOUNT',
            'harmony_channel_category' => 'ACCOUNT'
        ],
        // 新闻资讯类配置
        'news' => [
            'xiaomi_channel_id' => '132244', // 常观资讯
            'vivo_category' => 'NEWS',
            'oppo_channel_id' => 'push_oplus_category_content', // 公信频道
            'oppo_category' => 'NEWS',
            'huawei_channel_category' => 'MARKETING',
            'harmony_channel_category' => 'MARKETING'
        ],
        // 社交互动类配置
        'social' => [
            'xiaomi_channel_id' => '132245', // 常观互动消息
            'vivo_category' => 'SOCIAL',
            'oppo_channel_id' => 'upush_default', // 私信频道
            'oppo_category' => 'SOCIAL',
            'huawei_channel_category' => 'MARKETING',
            'harmony_channel_category' => 'MARKETING'
        ],
        // 活动营销类配置
        'marketing' => [
            'xiaomi_channel_id' => '132246', // 常观活动通知
            'vivo_category' => 'MARKETING',
            'oppo_channel_id' => 'push_oplus_category_content', // 公信频道
            'oppo_category' => 'MARKETING',
            'huawei_channel_category' => 'MARKETING',
            'harmony_channel_category' => 'MARKETING'
        ],
        // 订阅类配置
        'subscription' => [
            'xiaomi_channel_id' => '132244', // 常观资讯
            'vivo_category' => 'SUBSCRIPTION',
            'oppo_channel_id' => 'push_oplus_category_content', // 公信频道
            'oppo_category' => 'SUBSCRIPTION',
            'huawei_channel_category' => 'SUBSCRIPTION',
            'harmony_channel_category' => 'SUBSCRIPTION'
        ]
    ];

    public function run()
    {
        $now = Carbon::now();
        $templates = [
            // 1. 系统通知 (支持广播)
            [
                'code' => 'system_notice',
                'name' => '系统通知',
                'title' => '系统通知: #{title}#',
                'content' => '#{content}#',
                'category' => 1, // 系统通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'title' => '通知标题',
                    'content' => '通知内容'
                ]),
                'allowed_extend_params' => json_encode([
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['system']
            ],

            // 2. 新闻推荐 (支持广播)
            [
                'code' => 'news_recommendation',
                'name' => '新闻推荐',
                'title' => '#{category_name}#头条: #{title}#',
                'content' => '#{title}#\n#{summary}#',
                'category' => 2, // 活动通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'category_name' => '新闻分类',
                    'title' => '新闻标题',
                    'summary' => '新闻摘要'
                ]),
                'allowed_extend_params' => json_encode([
                    'news_id' => '新闻ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['news']
            ],

            // 3. 直播开播通知 (支持广播)
            [
                'code' => 'live_start',
                'name' => '直播开播通知',
                'title' => '#{anchor_name}#开播啦',
                'content' => '#{anchor_name}#开启了#{live_title}#\n#{live_desc}#',
                'category' => 2, // 活动通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'anchor_name' => '主播名称',
                    'live_title' => '直播标题',
                    'live_desc' => '直播简介'
                ]),
                'allowed_extend_params' => json_encode([
                    'live_id' => '直播ID',
                    'anchor_id' => '主播ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['news']
            ],

            // 4. @通知
            [
                'code' => 'at_notice',
                'name' => '@通知',
                'title' => '#{from_name}# @了你',
                'content' => '#{from_name}#在#{location}# @了你: #{content}#',
                'category' => 3, // @通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'from_name' => '发起人名称',
                    'location' => '位置描述',
                    'content' => '内容'
                ]),
                'allowed_extend_params' => json_encode([
                    'from_id' => '发起人ID',
                    'content_id' => '内容ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['social']
            ],

            // 5. 评论通知
            [
                'code' => 'comment_notice',
                'name' => '评论通知',
                'title' => '#{from_name}#评论了你',
                'content' => '#{from_name}#评论了你的#{content_type}#: #{comment_content}#',
                'category' => 5, // 评论通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'from_name' => '评论人名称',
                    'content_type' => '内容类型',
                    'comment_content' => '评论内容'
                ]),
                'allowed_extend_params' => json_encode([
                    'from_id' => '评论人ID',
                    'content_id' => '内容ID',
                    'comment_id' => '评论ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['social']
            ],

            // 6. 回复通知
            [
                'code' => 'reply_notice',
                'name' => '回复通知',
                'title' => '#{from_name}#回复了你',
                'content' => '#{from_name}#回复了你的评论: #{reply_content}#\n原评论: #{original_content}#',
                'category' => 5, // 评论通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'from_name' => '回复人名称',
                    'reply_content' => '回复内容',
                    'original_content' => '原评论内容'
                ]),
                'allowed_extend_params' => json_encode([
                    'from_id' => '回复人ID',
                    'comment_id' => '评论ID',
                    'reply_id' => '回复ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['social']
            ],

            // 7. 关注通知
            [
                'code' => 'follow_notice',
                'name' => '关注通知',
                'title' => '#{from_name}#关注了你',
                'content' => '#{from_name}#关注了你，点击查看TA的主页',
                'category' => 7, // 关注通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'from_name' => '关注人名称'
                ]),
                'allowed_extend_params' => json_encode([
                    'from_id' => '关注人ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['social']
            ],

            // 8. 点赞通知
            [
                'code' => 'like_notice',
                'name' => '点赞通知',
                'title' => '#{from_name}#赞了你',
                'content' => '#{from_name}#赞了你的#{content_type}#',
                'category' => 6, // 点赞通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 1, // 静默推送
                'allowed_params' => json_encode([
                    'from_name' => '点赞人名称',
                    'content_type' => '内容类型'
                ]),
                'allowed_extend_params' => json_encode([
                    'from_id' => '点赞人ID',
                    'content_id' => '内容ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['social']
            ],

            // 9. 钱包通知
            [
                'code' => 'wallet_notice',
                'name' => '钱包通知',
                'title' => '钱包#{change_type}#通知',
                'content' => '您的钱包#{change_type}#了#{amount}#元，当前余额: #{balance}#元',
                'category' => 4, // 钱包通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'change_type' => '变动类型',
                    'amount' => '变动金额',
                    'balance' => '当前余额'
                ]),
                'allowed_extend_params' => json_encode([
                    'transaction_id' => '交易ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['system']
            ],

            // 10. 积分获取提醒
            [
                'code' => 'points_earned',
                'name' => '积分获取提醒',
                'title' => '获得#{points}#积分',
                'content' => '恭喜您通过#{action}#获得了#{points}#积分，当前积分: #{total_points}#',
                'category' => 4, // 钱包通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 1, // 静默推送
                'allowed_params' => json_encode([
                    'points' => '获得积分',
                    'action' => '获取方式',
                    'total_points' => '总积分'
                ]),
                'allowed_extend_params' => json_encode([
                    'record_id' => '积分记录ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['system']
            ],

            // 11. 积分兑换通知
            [
                'code' => 'points_exchange',
                'name' => '积分兑换通知',
                'title' => '积分兑换成功',
                'content' => '您成功兑换了#{product_name}#，消耗#{points}#积分，剩余积分: #{total_points}#',
                'category' => 4, // 钱包通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'product_name' => '商品名称',
                    'points' => '消耗积分',
                    'total_points' => '剩余积分'
                ]),
                'allowed_extend_params' => json_encode([
                    'order_id' => '订单ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['system']
            ],

            // 12. 活动提醒通知
            [
                'code' => 'activity_reminder',
                'name' => '活动提醒',
                'title' => '活动即将开始: #{activity_name}#',
                'content' => '您报名的活动"#{activity_name}#"将在#{time_left}#后开始\n活动地点: #{location}#',
                'category' => 2, // 活动通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'activity_name' => '活动名称',
                    'time_left' => '剩余时间',
                    'location' => '活动地点'
                ]),
                'allowed_extend_params' => json_encode([
                    'activity_id' => '活动ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['marketing']
            ],

            // 13. 帮帮反馈通知
            [
                'code' => 'feedback_reply',
                'name' => '帮帮反馈通知',
                'title' => '您的反馈有了新进展',
                'content' => '您提交的"#{title}#"有了新的处理进展:\n#{status}#\n#{reply_content}#',
                'category' => 1, // 系统通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'title' => '反馈标题',
                    'status' => '处理状态',
                    'reply_content' => '回复内容'
                ]),
                'allowed_extend_params' => json_encode([
                    'feedback_id' => '反馈ID',
                    'reply_id' => '回复ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['system']
            ],

            // 14. 媒体号更新通知 (支持订阅)
            [
                'code' => 'media_update',
                'name' => '媒体号更新',
                'title' => '#{media_name}#发布了新内容',
                'content' => '#{media_name}#: #{content_title}#\n#{summary}#',
                'category' => 2, // 活动通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'media_name' => '媒体号名称',
                    'content_title' => '内容标题',
                    'summary' => '内容摘要'
                ]),
                'allowed_extend_params' => json_encode([
                    'media_id' => '媒体号ID',
                    'content_id' => '内容ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['subscription']
            ],

            // 15. 圈子热门通知 (支持订阅)
            [
                'code' => 'circle_hot',
                'name' => '圈子热门提醒',
                'title' => '您关注的#{circle_name}#有热门内容',
                'content' => '热门讨论: #{post_title}#\n#{summary}#',
                'category' => 2, // 活动通知
                'delivery_type' => 3, // 用户级别
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'circle_name' => '圈子名称',
                    'post_title' => '帖子标题',
                    'summary' => '内容摘要'
                ]),
                'allowed_extend_params' => json_encode([
                    'circle_id' => '圈子ID',
                    'post_id' => '帖子ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['subscription']
            ],

            // 16. 系统广播通知
            [
                'code' => 'system_broadcast',
                'name' => '系统广播',
                'title' => '系统通知: #{title}#',
                'content' => '#{content}#',
                'category' => 1, // 系统通知
                'delivery_type' => 8, // 广播
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'title' => '通知标题',
                    'content' => '通知内容'
                ]),
                'allowed_extend_params' => json_encode([
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['system']
            ],

            // 17. 新闻广播推送
            [
                'code' => 'news_broadcast',
                'name' => '新闻广播',
                'title' => '重要新闻: #{title}#',
                'content' => '#{title}#\n#{summary}#',
                'category' => 2, // 活动通知
                'delivery_type' => 8, // 广播
                'is_silent' => 0,
                'allowed_params' => json_encode([
                    'title' => '新闻标题',
                    'summary' => '新闻摘要'
                ]),
                'allowed_extend_params' => json_encode([
                    'news_id' => '新闻ID',
                    'redirect_to' => '跳转链接'
                ]),
                'channels' => $this->channelProperties['news']
            ]
        ];

        foreach ($templates as $template) {
            $channels = $template['channels'];
            unset($template['channels']);

            $template['created_at'] = $now;
            $template['updated_at'] = $now;

            // 合并通道配置
            $template = array_merge($template, $channels);

            DB::table('push_templates')->insert($template);
        }
    }
}
