<?php

namespace App\Utils;

use App\Models\AccessToken;
use App\Models\Client;
use App\Models\OAuthUserAuthorization;
use App\Models\OAuthUserBinding;
use App\Models\User;
use Illuminate\Support\Facades\Cache;

/**
 * OAuth缓存服务类
 * 提供OAuth相关数据的缓存访问服务，包括令牌、客户端、用户绑定和授权等信息的缓存管理
 *
 */
class OAuthCacher
{
    private const CACHE_TTL = 7200; // 默认缓存时间1小时
    private const TOKEN_PREFIX = 'oauth:token:';
    private const CLIENT_PREFIX = 'oauth:client:';
    private const CLIENT_KEY_PREFIX = 'oauth:client_key:';
    private const USER_BINDING_PREFIX = 'oauth:user_binding:';
    private const OPEN_ID_TO_UUID_PREFIX = 'oauth:openid_to_uuid:';
    private const UUID_TO_OPEN_ID_PREFIX = 'oauth:uuid_to_openid:';
    private const USER_AUTH_PREFIX = 'oauth:user_auth:';

    private static ?self $instance = null;

    // 获取单例实例
    private static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    // 静态方法代理
    public static function __callStatic(string $method, array $arguments) {
        $instance = self::getInstance();

        // 确保方法存在
        if (!method_exists($instance, $method)) {
            throw new \BadMethodCallException("Method {$method} does not exist.");
        }

        // 将实例方法声明为 public
        return $instance->$method(...$arguments);
    }

    /**
     * 获取访问令牌
     *
     * 根据令牌ID获取未撤销且未过期的访问令牌信息
     *
     * @param string $tokenId 访问令牌ID
     *
     * @return AccessToken|null 返回访问令牌对象，如果令牌不存在、已撤销或已过期则返回null
     *
     */
    public static function getAccessToken(string $tokenId): ?AccessToken {
        $cacheKey = self::TOKEN_PREFIX . $tokenId;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($cacheKey, $tokenId) {
            $token = AccessToken::where('id', $tokenId)
                                ->where('is_revoked', AccessToken::IS_REVOKED_NO)
                                ->where('expires_at', '>', now())
                                ->first();

            if (!$token || $token->is_revoked || $token->expires_at->isPast()) {
                Cache::forget($cacheKey);

                return null;
            }

            return $token;
        });
    }

    /**
     * 通过client_id获取客户端信息
     *
     * 根据客户端ID获取未撤销的客户端信息
     *
     * @param int $clientId 客户端ID
     *
     * @return Client|null 返回客户端对象，如果客户端不存在或已撤销则返回null
     *
     * @example
     * ```php
     * $client = OAuthCacher::getClient(1);
     * if ($client) {
     *     $allowedScopes = $client->allowed_scopes;
     * }
     * ```
     */
    public static function getClient(int $clientId): ?Client {
        $cacheKey = self::CLIENT_PREFIX . $clientId;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($clientId) {
            return Client::where('id', $clientId)
                         ->where('is_revoked', Client::IS_REVOKED_NO)
                         ->first();
        });
    }

    /**
     * 通过client_key获取客户端信息
     *
     * 根据客户端key获取未撤销的客户端信息
     *
     * @param string $clientKey 客户端key
     *
     * @return Client|null 返回客户端对象，如果客户端不存在或已撤销则返回null
     *
     * @example
     * ```php
     * $client = OAuthCacher::getClientByKey('app-key-123');
     * ```
     */
    public static function getClientByKey(string $clientKey): ?Client {
        $cacheKey = self::CLIENT_KEY_PREFIX . $clientKey;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($clientKey) {
            return Client::where('client_key', $clientKey)
                         ->where('is_revoked', Client::IS_REVOKED_NO)
                         ->first();
        });
    }

    /**
     * 通过client_key获取client_id
     *
     * 根据客户端key获取对应的客户端ID
     *
     * @param string $clientKey 客户端key
     *
     * @return int|null 返回客户端ID，如果客户端不存在则返回null
     */
    public static function getClientIdByKey(string $clientKey): ?int {
        $client = self::getClientByKey($clientKey);

        return $client?->id;
    }


    /**
     * 通过client_key获取client_secret
     *
     * 根据客户端key获取对应的客户端ID
     *
     * @param string $clientKey 客户端key
     *
     * @return string|null 返回客户端ID，如果客户端不存在则返回null
     */
    public static function getClientSecretByKey(string $clientKey): ?string {
        $client = self::getClientByKey($clientKey);

        return $client?->client_access_secret;
    }

    /**
     * 验证客户端凭据
     *
     * 验证客户端key和secret是否匹配且客户端未被撤销
     *
     * @param string $clientKey 客户端key
     * @param string $clientSecret 客户端secret
     *
     * @return bool 验证成功返回true，失败返回false
     */
    public static function validateClientCredentials(string $clientKey, string $clientSecret): bool {
        $client = self::getClientByKey($clientKey);
        if (!$client) {
            return false;
        }

        return $client->client_access_secret === $clientSecret && !$client->is_revoked;
    }

    /**
     * 获取客户端允许的权限范围
     *
     * 根据客户端key获取该客户端被允许的所有权限范围列表
     *
     * @param string $clientKey 客户端key
     *
     * @return array 返回权限范围数组，如果客户端不存在则返回空数组
     */
    public static function getAllowedScopesByClientKey(string $clientKey): array {
        $client = self::getClientByKey($clientKey);
        if (!$client) {
            return [];
        }

        return $client->allowed_scopes ?? [];
    }

    /**
     * 获取客户端允许的JS接口
     *
     * 根据客户端key获取该客户端被允许使用的所有JS接口列表
     *
     * @param string $clientKey 客户端key
     *
     * @return array 返回JS接口数组，如果客户端不存在则返回空数组
     */
    public static function getAllowedJsApisByClientKey(string $clientKey): array {
        $client = self::getClientByKey($clientKey);
        if (!$client) {
            return [];
        }

        return $client->allowed_jsapis ?? [];
    }

    /**
     * 根据openId和clientKey获取用户UUID
     *
     * 通过openId和clientKey的组合获取对应的用户UUID
     *
     * @param string $openId 用户的openId
     * @param string $clientKey 客户端key
     *
     * @noinspection PhpMethodMayBeStaticInspection
     *
     */
    public static function getUserUuidByOpenIdAndClientKey(string $openId, string $clientKey): ?string {
        $clientId = self::getClientIdByKey($clientKey);
        if (!$clientId) {
            return null;
        }

        return self::getUserUuidByOpenId($openId, $clientId);
    }

    /**
     * 根据openId和clientId获取用户UUID
     *
     * 通过openId和clientId的组合获取对应的用户UUID，同时验证用户授权状态
     *
     * @param string $openId 用户的openId
     * @param int $clientId 客户端ID
     *
     * @return string|null 返回用户UUID，如果未找到匹配记录或用户未授权则返回null
     */
    public static function getUserUuidByOpenId(string $openId, int $clientId): ?string {
        $cacheKey = self::OPEN_ID_TO_UUID_PREFIX . "{$clientId}:{$openId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($openId, $clientId) {
            $userUuid = OAuthUserBinding::where('open_id', $openId)
                                        ->where('client_id', $clientId)
                                        ->value('user_uuid');

            // 如果找到了用户UUID,检查授权状态
            if ($userUuid && !self::isUserAuthorized($userUuid, $clientId)) {
                return null;
            }

            return $userUuid;
        });
    }

    /**
     * 根据用户UUID获取openId
     *
     * 通过用户UUID和clientId的组合获取对应的openId
     *
     * @param string $userUuid 用户UUID
     * @param int $clientId 客户端ID
     *
     * @return string|null 返回用户openId，如果未找到匹配记录则返回null
     */
    public static function getOpenIdByUserUuid(string $userUuid, int $clientId): ?string {
        $cacheKey = self::UUID_TO_OPEN_ID_PREFIX . "{$clientId}:{$userUuid}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userUuid, $clientId) {
            return OAuthUserBinding::where('user_uuid', $userUuid)
                                   ->where('client_id', $clientId)
                                   ->value('open_id');
        });
    }

    /**
     * 获取用户绑定信息
     *
     * 获取指定用户与客户端的绑定关系信息
     *
     * @param string $userUuid 用户UUID
     * @param int $clientId 客户端ID
     *
     * @return OAuthUserBinding|null 返回绑定关系对象，如果未找到则返回null
     */
    public static function getUserBinding(string $userUuid, int $clientId): ?OAuthUserBinding {
        $cacheKey = self::USER_BINDING_PREFIX . "{$userUuid}:{$clientId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userUuid, $clientId) {
            return OAuthUserBinding::where('user_uuid', $userUuid)
                                   ->where('client_id', $clientId)
                                   ->first();
        });
    }

    /**
     * 根据openId获取用户绑定
     *
     * 通过openId获取用户与客户端的绑定关系信息
     *
     * @param string $openId 用户的openId
     * @param int $clientId 客户端ID
     *
     * @return OAuthUserBinding|null 返回绑定关系对象，如果未找到则返回null
     */
    public static function getUserBindingByOpenId(string $openId, int $clientId): ?OAuthUserBinding {
        return Cache::remember(self::USER_BINDING_PREFIX . "openid:{$clientId}:{$openId}", self::CACHE_TTL, function () use ($openId, $clientId) {
            return OAuthUserBinding::where('open_id', $openId)
                                   ->where('client_id', $clientId)
                                   ->first();
        });
    }

    /**
     * 根据openId获取用户信息
     *
     * 通过openId查找对应的用户完整信息
     *
     * @param string $openId 用户的openId
     * @param int $clientId 客户端ID
     *
     * @return User|null 返回用户对象，如果未找到则返回null
     */
    public static function getUserByOpenId(string $openId, int $clientId): ?User {
        $userUuid = self::getUserUuidByOpenId($openId, $clientId);
        if (!$userUuid) {
            return null;
        }

        return User::where('uuid', $userUuid)
                   ->first();
    }

    /**
     * 检查用户是否已绑定
     *
     * 检查指定用户是否已经与客户端建立绑定关系
     *
     * @param string $userUuid 用户UUID
     * @param int $clientId 客户端ID
     *
     * @return bool 如果已绑定返回true，否则返回false
     */
    public static function isUserBound(string $userUuid, int $clientId): bool {
        return self::getUserBinding($userUuid, $clientId) !== null;
    }

    /**
     * 清除访问令牌缓存
     *
     * 使指定的访问令牌缓存失效
     *
     * @param string $tokenId 访问令牌ID
     */
    public static function invalidateAccessToken(string $tokenId): void {
        Cache::forget(self::TOKEN_PREFIX . $tokenId);
    }

    /**
     * 清除客户端缓存
     *
     * 使指定客户端的所有相关缓存失效
     *
     * @param int $clientId 客户端ID
     */
    public static function invalidateClient(int $clientId): void {
        $client = self::getClient($clientId);
        if ($client) {
            // 清除所有相关缓存
            Cache::forget(self::CLIENT_PREFIX . $clientId);
            Cache::forget(self::CLIENT_KEY_PREFIX . $client->client_key);
        }
    }

    /**
     * 根据key清除客户端缓存
     *
     * 通过客户端key使相关客户端缓存失效
     *
     * @param string $clientKey 客户端key
     */
    public static function invalidateClientByKey(string $clientKey): void {
        $client = self::getClientByKey($clientKey);
        if ($client) {
            self::invalidateClient($client->id);
        }
    }

    /**
     * 清除用户绑定缓存
     *
     * 使指定用户与客户端的绑定关系缓存失效
     *
     * @param string $userUuid 用户UUID
     * @param int $clientId 客户端ID
     */
    public static function invalidateUserBinding(string $userUuid, int $clientId): void {
        $binding = self::getUserBinding($userUuid, $clientId);
        if ($binding) {
            // 清除所有相关缓存
            Cache::forget(self::USER_BINDING_PREFIX . "{$userUuid}:{$clientId}");
            Cache::forget(self::OPEN_ID_TO_UUID_PREFIX . "{$clientId}:{$binding->open_id}");
            Cache::forget(self::UUID_TO_OPEN_ID_PREFIX . "{$clientId}:{$userUuid}");
            Cache::forget(self::USER_BINDING_PREFIX . "openid:{$clientId}:{$binding->open_id}");
        }
    }

    /**
     * 预加载客户端缓存
     *
     * 批量预加载多个客户端的缓存信息
     *
     * @param array $clientKeys 客户端key数组
     * @param array $clientIds 客户端ID数组
     */
    public static function preloadClients(array $clientKeys = [], array $clientIds = []): void {
        foreach ($clientKeys as $clientKey) {
            self::getClientByKey($clientKey);
        }

        foreach ($clientIds as $clientId) {
            self::getClient($clientId);
        }
    }

    /**
     * 验证令牌权限范围
     *
     * 检查访问令牌是否具有指定的权限范围
     *
     * @param string $tokenId 访问令牌ID
     * @param string $scope 待验证的权限范围
     *
     * @return bool 如果令牌具有指定权限则返回true，否则返回false
     */
    public static function validateTokenScope(string $tokenId, string $scope): bool {
        $token = self::getAccessToken($tokenId);
        if (!$token) {
            return false;
        }

        return $token->can($scope);
    }

    /**
     * 批量获取用户绑定
     *
     * 批量获取多个用户与指定客户端的绑定关系
     *
     * @param array $userUuids 用户UUID数组
     * @param int $clientId 客户端ID
     *
     * @return array 返回用户UUID为key，绑定关系对象为value的关联数组
     *
     * @example
     * ```php
     * $bindings = OAuthCacher::batchGetUserBindings(['uuid1', 'uuid2'], 1);
     * foreach ($bindings as $uuid => $binding) {
     *     echo $binding->open_id;
     * }
     * ```
     */
    public static function batchGetUserBindings(array $userUuids, int $clientId): array {
        $results = [];
        foreach ($userUuids as $userUuid) {
            $binding = self::getUserBinding($userUuid, $clientId);
            if ($binding) {
                $results[$userUuid] = $binding;
            }
        }

        return $results;
    }

    /**
     * 检查用户是否已授权
     *
     * 检查指定用户是否已经授权客户端访问其资源
     *
     * @param string $userUuid 用户UUID
     * @param int $clientId 客户端ID
     *
     * @return bool 如果已授权返回true，否则返回false
     */
    public static function isUserAuthorized(string $userUuid, int $clientId): bool {
        $cacheKey = self::USER_AUTH_PREFIX . "{$userUuid}:{$clientId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userUuid, $clientId) {
            return OAuthUserAuthorization::where('user_uuid', $userUuid)
                                         ->where('client_id', $clientId)
                                         ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO)
                                         ->exists();
        });
    }

    /**
     * 获取用户授权范围
     *
     * 获取用户授权给客户端的具体权限范围
     *
     * @param string $userUuid 用户UUID
     * @param int $clientId 客户端ID
     *
     * @return array 返回授权范围数组
     */
    public static function getUserAuthorizedScopes(string $userUuid, int $clientId): array {
        $cacheKey = self::USER_AUTH_PREFIX . "scopes:{$userUuid}:{$clientId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userUuid, $clientId) {
            return OAuthUserAuthorization::where('user_uuid', $userUuid)
                                         ->where('client_id', $clientId)
                                         ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO)
                                         ->value('granted_scopes') ?? [];
        });
    }

    /**
     * 清除用户授权缓存
     *
     * 使指定用户对客户端的授权信息缓存失效
     *
     * @param string $userUuid 用户UUID
     * @param int $clientId 客户端ID
     */
    public static function invalidateUserAuthorization(string $userUuid, int $clientId): void {
        Cache::forget(self::USER_AUTH_PREFIX . "{$userUuid}:{$clientId}");
        Cache::forget(self::USER_AUTH_PREFIX . "scopes:{$userUuid}:{$clientId}");

        // 同时使相关的用户绑定缓存失效,因为授权状态会影响它们的结果
        self::invalidateUserBinding($userUuid, $clientId);
    }
}
