<?php

namespace App\Enums\Push;

enum OppoChannelEnum: string
{
    case DEFAULT = 'upush_default';                  // 默认渠道
    case CONTENT = 'push_oplus_category_content';    // 内容渠道

    public function label(): string {
        return match ($this) {
            self::DEFAULT => '默认渠道',
            self::CONTENT => '内容渠道',
            default => '',
        };
    }

    public static function options(): array {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }
}