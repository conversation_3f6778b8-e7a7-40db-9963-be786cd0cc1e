<?php

namespace App\Listeners;

use App\Events\UserLogoutEvent;
use App\Models\Client;
use App\Utils\OAuthCacher;
use App\Utils\SignatureValidator;
use App\Utils\Tools;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class UserLogoutListener implements shouldQueue
{
    use InteractsWithQueue;

    protected $queue = 'account';

    /**
     * Create the event listener.
     */
    public function __construct() {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserLogoutEvent $event): void {

        $postData = [
            'event'   => 'user_logout',
            'to_user' => $event->getUserUuid(),
            'status'  => 'logout',
        ];

        Client::where('is_revoked', Client::IS_REVOKED_NO)
              ->where('is_workspace_client', Client::IS_WORKSPACE_CLIENT_NO)
              ->get()
              ->each(function (Client $client) use ($postData) {
                  if ($client->is_system != Client::IS_SYSTEM_YES) {
                      if (!OAuthCacher::isUserAuthorized($postData['to_user'], $client->id)) {
                          return;
                      }
                      $toUser = OAuthCacher::getOpenIdByUserUuid($postData['to_user'], $client->id);

                      if (!$toUser) {
                          return;
                      }

                      $postData['to_user'] = $toUser;
                  }
                  $signatureData = [
                          'client_id' => $client->client_key,
                          'timestamp' => time(),
                          'nonce'     => Str::random(),
                      ] + $postData;

                  $signatureValidator = new SignatureValidator($client->client_key, $client->client_access_secret);

                  $signature = $signatureValidator->generateSignature($signatureData);

                  // 验证callback_url是否为有效的url
                  if ($client->callback_url && filter_var($client->callback_url, FILTER_VALIDATE_URL)) {
                      try {
                          Http::withHeaders([
                              Tools::HEADER_SIGN      => $signature,
                              Tools::HEADER_TIMESTAMP => $signatureData['timestamp'],
                              Tools::HEADER_NONCE     => $signatureData['nonce'],
                              Tools::HEADER_CLIENT_ID => $client->client_key,
                          ])
                              ->withOptions([
                                  'verify' => false,
                                  'curl'   => [
                                      CURLOPT_RESOLVE => [
                                          'uc.example.com:80:127.0.0.1',
                                          'kanchangzhou.example.com:80:127.0.0.1',
                                      ],
                                  ],
                              ])
                              ->post($client->callback_url, $postData);
                      } catch (ConnectionException $e) {

                      }

                  }
              });
    }
}
