<?php

namespace App\Services;

use Illuminate\Support\Collection;
use Spatie\Permission\Models\Permission;

class PermissionGroupService
{
    /**
     * 获取所有权限分组
     */
    public function getGroups(): Collection
    {
        $permissions = Permission::all();

        return $permissions->groupBy(function ($permission) {
            return explode('.', $permission->name)[0];
        })->map(function ($permissions, $group) {
            return [
                'name' => $group,
                'permissions' => $permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'description' => $permission->description,
                        'actions' => array_slice(explode('.', $permission->name), 1)
                    ];
                })
            ];
        });
    }

    /**
     * 获取权限树结构
     */
    public function getPermissionTree(): array
    {
        $permissions = Permission::all();
        $tree = [];

        foreach ($permissions as $permission) {
            $parts = explode('.', $permission->name);
            $current = &$tree;

            foreach ($parts as $index => $part) {
                if (!isset($current[$part])) {
                    $current[$part] = [
                        'title' => $part,
                        'key' => implode('.', array_slice($parts, 0, $index + 1))
                    ];

                    if ($index === count($parts) - 1) {
                        $current[$part]['isLeaf'] = true;
                        $current[$part]['id'] = $permission->id;
                        $current[$part]['description'] = $permission->description;
                    } else {
                        $current[$part]['children'] = [];
                    }
                }
                $current = &$current[$part]['children'];
            }
        }

        return $this->formatTree(array_values($tree));
    }

    /**
     * 获取指定分组的所有权限
     */
    public function getGroupPermissions(string $group): Collection
    {
        return Permission::where('name', 'like', $group . '.%')->get();
    }

    /**
     * 检查权限名称格式是否正确
     */
    public function validatePermissionName(string $name): bool
    {
        return count(explode('.', $name)) >= 2;
    }

    /**
     * 格式化树形结构
     */
    private function formatTree(array $tree): array
    {
        $result = [];
        foreach ($tree as $node) {
            $formattedNode = [
                'title' => $node['title'],
                'key' => $node['key']
            ];

            if (isset($node['id'])) {
                $formattedNode['id'] = $node['id'];
                $formattedNode['description'] = $node['description'];
                $formattedNode['isLeaf'] = true;
            }

            if (isset($node['children'])) {
                $formattedNode['children'] = $this->formatTree(array_values($node['children']));
            }

            $result[] = $formattedNode;
        }

        return $result;
    }
}
