<?php

namespace App\Http\AdminControllers;

use App\Utils\Respond;
use Illuminate\Http\Request;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Facades\Auth;

class ActivityLogsController extends AdminBaseController
{
    protected const PERMISSION_MAP = [
        'index'       => '日志.查看列表',
        'modelLogs'   => '日志.查看模型日志',
    ];

    /**
     * 获取活动日志列表
     */
    public function index(Request $request)
    {
        $query = Activity::query()
            ->with(['causer', 'subject'])
            ->when($request->filled('log_name'), function ($query) use ($request) {
                $query->where('log_name', $request->input('log_name'));
            })
            ->when($request->filled('causer_id'), function ($query) use ($request) {
                $query->where('causer_id', $request->input('causer_id'));
            })
            ->when($request->filled('causer_type'), function ($query) use ($request) {
                $query->where('causer_type', $request->input('causer_type'));
            })
            ->when($request->filled('subject_id'), function ($query) use ($request) {
                $query->where('subject_id', $request->input('subject_id'));
            })
            ->when($request->filled('subject_type'), function ($query) use ($request) {
                $query->where('subject_type', $request->input('subject_type'));
            })
            ->when($request->filled('batch_uuid'), function ($query) use ($request) {
                $query->where('batch_uuid', $request->input('batch_uuid'));
            })
            ->when($request->filled('description'), function ($query) use ($request) {
                $query->where('description', 'like', '%' . $request->input('description') . '%');
            })
            ->when($request->filled('start_date'), function ($query) use ($request) {
                $query->whereDate('created_at', '>=', $request->input('start_date'));
            })
            ->when($request->filled('end_date'), function ($query) use ($request) {
                $query->whereDate('created_at', '<=', $request->input('end_date'));
            });

        $logs = $query->orderByDesc('created_at')
            ->paginate($request->input('per_page', 15));

        // 格式化日志数据
        $formattedLogs = $logs->map(function ($log) {
            return $this->formatLogData($log);
        });

        return Respond::success($formattedLogs);
    }

    /**
     * 获取指定模型的活动日志
     */
    public function modelLogs(Request $request)
    {
        $validated = $request->validate([
            'subject_id' => 'required|string',
            'subject_type' => 'required|string',
        ]);

        $query = Activity::query()
            ->with(['causer'])
            ->where('subject_id', $validated['subject_id'])
            ->where('subject_type', $validated['subject_type'])
            ->when($request->filled('log_name'), function ($query) use ($request) {
                $query->where('log_name', $request->input('log_name'));
            })
            ->when($request->filled('start_date'), function ($query) use ($request) {
                $query->whereDate('created_at', '>=', $request->input('start_date'));
            })
            ->when($request->filled('end_date'), function ($query) use ($request) {
                $query->whereDate('created_at', '<=', $request->input('end_date'));
            });

        $logs = $query->orderByDesc('created_at')
            ->paginate($request->input('per_page', 15));

        // 格式化日志数据
        $formattedLogs = $logs->map(function ($log) {
            return $this->formatLogData($log);
        });

        return Respond::success($formattedLogs);
    }

    /**
     * 获取日志类型选项
     */
    public function logOptions()
    {
        $logNames = Activity::distinct()->pluck('log_name')->filter()->values();
        
        return Respond::success([
            'log_names' => $logNames,
        ]);
    }

    /**
     * 格式化日志数据
     */
    protected function formatLogData($log)
    {
        $data = [
            'id' => $log->id,
            'log_name' => $log->log_name,
            'description' => $log->description,
            'created_at' => $log->created_at->format('Y-m-d H:i:s'),
            'properties' => $log->properties,
            'batch_uuid' => $log->batch_uuid,
        ];

        // 添加操作者信息
        if ($log->causer) {
            $data['causer'] = [
                'id' => $log->causer->uuid ?? $log->causer->id,
                'name' => $log->causer->display_name ?? $log->causer->name ?? $log->causer->username ?? 'Unknown',
                'type' => class_basename($log->causer),
            ];
        }

        // 添加主体信息
        if ($log->subject) {
            $data['subject'] = [
                'id' => $log->subject->uuid ?? $log->subject->id,
                'name' => $log->subject->display_name ?? $log->subject->name ?? $log->subject->username ?? 'Unknown',
                'type' => class_basename($log->subject),
            ];
        }

        return $data;
    }
} 