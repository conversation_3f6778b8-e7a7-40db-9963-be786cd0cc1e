<?php

namespace App\Services;

use AlibabaCloud\SDK\Captcha\V20230305\Captcha;
use AlibabaCloud\SDK\Captcha\V20230305\Models\VerifyIntelligentCaptchaRequest;
use App\Models\SmsCode;
use App\Models\User;
use Carbon\Carbon;
use Darabonba\OpenApi\Models\Config;
use Illuminate\Validation\Validator;


class ValidatorExtends
{
    public static function isMobile($attribute, $value, $paramers, $validator) {
        $regexMobileNumber = '/^(?:\+?86)?1(?:3\d{3}|5[^4\D]\d{2}|8\d{3}|7(?:[0-35-9]\d{2}|4(?:0\d|1[0-2]|9\d))|9[0-35-9]\d{2}|6[2567]\d{2}|4[579]\d{2})\d{6}$/i';
        if (!preg_match($regexMobileNumber, $value)) {
            return false;
        }

        return true;
    }

    public static function usernameOrMobileUnique($attribute, $value, $paramers, $validator) {
        $user = User::where(function ($query) use ($value) {
            return $query->where('username', $value)
                         ->orWhere('mobile', $value);
        })
                    ->when(isset($paramers[0]) && $paramers[0], function ($query) use ($paramers) {
                        return $query->where('id', '<>', $paramers[0]);
                    })
                    ->first();

        if ($user) {
            return false;
        }

        return true;
    }

    public static function checkCode($attribute, $value, $paramers, $validator) {
        $mobile = $paramers[0];

        $code = SmsCode::where('mobile', $mobile)
                       ->where('code', $value)
                       ->where('status', SmsCode::VERIFIED_NO)
                       ->orderByDesc('id')
                       ->first();

        if (!$code || !$code->created_at->addMinutes(config('uc.sms.code_expired_ttl'))
                                        ->isAfter(Carbon::now())) {
            return false;
        }

        $code->status = SmsCode::VERIFIED_YES;
        $code->save();

        return true;
    }

    public static function checkRobotAnalyze($attribute, $value, $paramers, $validator) {
        // 如果env 不是为生产环境，直接返回true
        $force = $paramers[0] ?? false;

        if (config('app.env') != 'production' && !$force) {
            return true;
        }

        $scendId = $validator->getData()['captcha_scene'] ?? '';

        if (!$scendId) {
            return false;
        }

        $config = new Config([
            'accessKeyId' => config('uc.safe.afs.access_key_id'),
            'accessKeySecret' => config('uc.safe.afs.access_secret'),
        ]);

        $config->endpoint = 'captcha.cn-shanghai.aliyuncs.com';
        $client = new Captcha($config);

        $verifyIntelligentCaptchaRequest = new VerifyIntelligentCaptchaRequest([
            "captchaVerifyParam" => $value,
            "sceneId" => $scendId,
        ]);

        try {
            $res = $client->verifyIntelligentCaptcha($verifyIntelligentCaptchaRequest);
            $validator->setCustomMessages([
                // 'check_robot_analyze' => $res->body->result->verifyCode,
            ]);
            if ($res->statusCode == 200 && $res->body->code == 'Success' && $res->body->result->verifyResult) {
                return true;
            } else {
                return false;
            }

        } catch (\Exception $error) {
            $validator->setCustomMessages([
                // 'check_robot_analyze' => $error->getMessage()
            ]);
            return false;
        }
    }
}
