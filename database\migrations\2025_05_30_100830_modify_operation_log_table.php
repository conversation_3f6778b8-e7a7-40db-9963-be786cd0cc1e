<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admin_operation_logs', function (Blueprint $table) {
            $table->string('admin_user_uuid',36)->comment('管理员UUID')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_operation_logs', function (Blueprint $table) {
            $table->string('admin_user_uuid',36)->comment('管理员UUID')->change();
        });
    }
};
