<?php

namespace App\Services\JwtWithSm2\Signers;

use App\Utils\GmSm;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key;
use Rtgm\sm\RtSm2;

class SM2Signer implements Signer
{
    protected $userId;
//    protected $sm2;
    protected $withKey;

    public function __construct($userId = null, $withKey = false) {
        $this->userId = $userId;
//        $this->sm2 = new RtSm2('hex', true);
        $this->withKey = $withKey;
    }

    public function setUserId($userId) {
        $this->userId = $userId;

        return $this;
    }

    public function getUserId() {
        return $this->userId;
    }

    public function algorithmId(): string {
        return 'SM2';
    }

    /**
     * @throws \Exception
     */
    public function sign(string $payload, Key $key): string {
        if ($this->withKey) {
            GmSm::configure(privateKey: $key->contents());
//            return $this->sm2->doSign($payload, $key->contents(), $this->getUserId());
        } else {
            GmSm::configure(privateKeyPath: $key->contents());
//            return $this->sm2->doSignOutKey($payload, $key->contents(), $this->getUserId());
        }

        return GmSm::sm2Sign($payload, $this->getUserId());
    }

    /**
     * @throws \Exception
     */
    public function verify(string $expected, string $payload, Key $key, $userId = null): bool {
//        try {
//            if ($this->withKey) {
//                return $this->sm2->verifySign($payload, $expected, $key->contents(), $userId);
//            } else {
//                return $this->sm2->verifySignOutKey($payload, $expected, $key->contents(), $userId);
//            }

            if ($this->withKey) {
                GmSm::configure(publicKey: $key->contents());
            } else {
                GmSm::configure(publicKeyPath: $key->contents());
            }
            return GmSm::sm2VerifySign($payload, $expected, $userId);

//        } catch (\Exception $e) {
//            return false;
//        }
    }
}
