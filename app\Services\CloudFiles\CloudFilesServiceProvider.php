<?php

namespace App\Services\CloudFiles;

use Illuminate\Support\ServiceProvider;
use App\Services\CloudFiles\Contracts\CloudFilesInterface;
use App\Services\CloudFiles\Exceptions\CloudFilesException;

class CloudFilesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // 注册配置
        $this->registerConfiguration();

        // 注册主服务
        $this->app->singleton('cloudfiles', function ($app) {
            return new CloudFilesManager($app);
        });

        // 注册门面
        $this->app->alias('cloudfiles', CloudFiles::class);

        // 注册文件系统扩展
        CloudFiles::registerFilesystem();
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // 发布配置文件
        $this->publishes([
            __DIR__ . '/config/cloudfiles.php' => config_path('cloudfiles.php'),
        ], 'cloudfiles-config');

        // 注册路由中间件
        $this->registerRouteMiddleware();
    }

    /**
     * 注册配置
     *
     * @return void
     */
    protected function registerConfiguration(): void
    {
        $filesystemConfig = $this->app['config']->get('filesystems.disks', []);

        // 尝试从 filesystem 配置加载
        $cloudConfig = CloudFiles::loadCloudFilesConfig($filesystemConfig);

        // 如果找到了配置，设置到 cloudfiles
        if (!empty($cloudConfig)) {
            foreach ($cloudConfig as $key => $value) {
                $this->app['config']->set('cloudfiles.' . $key, $value);
            }
        } else {
            // 否则加载独立配置文件
            $this->mergeConfigFrom(
                __DIR__ . '/config/cloudfiles.php',
                'cloudfiles'
            );
        }
    }

    /**
     * 注册到 Laravel Filesystem
     *
     * @return void
     * @throws CloudFilesException
     */
    protected function registerFilesystemExtensions(): void
    {
        foreach (['oss', 'cos', 'obs', 'qiniu'] as $driver) {
            $this->registerFilesystemDriver($driver);
        }
    }

    /**
     * 注册指定驱动到 Filesystem
     *
     * @param string $driver
     *
     * @return void
     * @throws CloudFilesException
     */
    protected function registerFilesystemDriver(string $driver): void
    {
        $this->app['filesystem']->extend($driver, function ($app, $config) use ($driver) {
            $parsedConfig = CloudFiles::parseFilesystemConfig($config);

            try {
                $client = $app['cloudfiles']->driver($driver);
                return CloudFiles::createFilesystemAdapter($client, $parsedConfig);
            } catch (CloudFilesException $e) {
                throw new CloudFilesException(
                    "Failed to initialize {$driver} filesystem: " . $e->getMessage(),
                    $e->getCode(),
                    $e
                );
            }
        });
    }

    /**
     * 注册路由中间件
     *
     * @return void
     */
    protected function registerRouteMiddleware(): void
    {
        // 注册命名中间件
//        $this->app['router']->aliasMiddleware('cloud.callback', \App\Http\Middleware\CloudFilesCallback::class);

        // 注册中间件组
//        $this->app['router']->middlewareGroup('cloud', [
//            'cloud.callback'
//        ]);
    }

    /**
     * 获取服务提供者提供的服务
     *
     * @return array
     */
    public function provides(): array
    {
        return [
            'cloudfiles',
            CloudFiles::class,
        ];
    }

    /**
     * 获取服务提供者延迟加载的服务
     *
     * @return array
     */
    public function when(): array
    {
        return [
            'Illuminate\Filesystem\FilesystemServiceProvider',
        ];
    }
}
