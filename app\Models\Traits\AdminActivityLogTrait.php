<?php

namespace App\Models\Traits;

use App\Utils\Tools;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Contracts\Activity;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Request;

/**
 * 后台管理活动日志记录通用 Trait
 * 结合了基础日志、批量日志和隐私处理功能
 */
trait AdminActivityLogTrait
{
    use LogsActivityTrait, BatchLogsActivityTrait, SanitizesActivityLogTrait;

    /**
     * 获取活动日志选项
     *
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly($this->getLogAttributes())
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => $this->getDescriptionForEvent($eventName))
            ->useLogName($this->getLogName())
            ->dontLogIfAttributesChangedOnly(['updated_at']);
    }

    /**
     * 在活动被记录前处理
     *
     * @param Activity $activity
     * @param string $eventName
     * @return void
     */
    public function tapActivity(Activity $activity, string $eventName)
    {
        // 添加额外的属性
        $extraProperties = [
            'ip_address' => Tools::getClientIp(),
            // 'user_agent' => Tools::getBrowser(),
            'enterprise_id' => $this->enterprise_id ?? null,
            'operation_type' => $this->getOperationType(),
            // 'route' => request()->route() ? request()->route()->getName() : null,
            'url' => request()->fullUrl(),
        ];
        
        // 合并属性
        $properties = $activity->properties->toArray();
        $properties = array_merge($properties, $extraProperties);
        
        // 处理敏感字段
        $sanitizedProperties = $this->sanitizeActivityLogProperties($properties);
        // 更新活动属性
        $activity->properties = $this->convert_to_utf8_recursively($sanitizedProperties);
        
    }


    public function convert_to_utf8_recursively($data)
    {
        if (is_string($data)) {
            // 使用 mb_convert_encoding 清理并确保是有效的 UTF-8
            return mb_convert_encoding($data, 'UTF-8', 'UTF-8');
        }
    
        if (is_array($data)) {
            foreach ($data as &$value) {
                $value = $this->convert_to_utf8_recursively($value);
            }
            return $data;
        }
    
        if (is_object($data)) {
            // 如果是对象，则迭代其属性
            $vars = get_object_vars($data);
            foreach ($vars as $key => $value) {
                $data->$key = $this->convert_to_utf8_recursively($value);
            }
            return $data;
        }
    
        return $data;
    }

    /**
 * 递归检查数组中所有字符串字段的字符编码。
 *
 * @param array $data 要检查的数组
 * @return array 返回一个关联数组，键是字段的路径，值是检测到的编码。
 */
public function check_field_encodings(array $data): array
{
    $results = [];
    // 定义一个常见的编码列表，以提高检测准确性。顺序很重要。
    $encodingList = ['UTF-8', 'GBK', 'GB2312', 'BIG5', 'ISO-8859-1', 'ASCII'];

    // 使用一个内部递归函数来构建字段路径
    $walker = function ($array, $parentKey = '') use (&$walker, &$results, $encodingList) {
        foreach ($array as $key => $value) {
            // 构建完整的字段路径，例如：'attributes.username'
            $currentKey = $parentKey ? "{$parentKey}.{$key}" : $key;

            if (is_string($value)) {
                // 为字符串值检测编码
                // 第三个参数 `true` 表示严格检测
                $encoding = mb_detect_encoding($value, $encodingList, true);
                $results[$currentKey] = $encoding ?: '检测失败或二进制';
            } elseif (is_array($value) && !empty($value)) {
                // 如果是数组，则递归进入
                $walker($value, $currentKey);
            }
            // 其他类型（如 null, int, bool）没有编码，直接忽略
        }
    };

    $walker($data);

    return $results;
}
    /**
     * 获取要记录的属性
     *
     * @return array
     */
    protected function getLogAttributes(): array
    {
        if (property_exists($this, 'logAttributes') && is_array($this->logAttributes)) {
            return $this->logAttributes;
        }

        // 默认记录 fillable 字段
        return $this->getFillable();
    }

    /**
     * 获取日志名称
     *
     * @return string
     */
    protected function getLogName(): string
    {
        // 如果模型有自定义 logName 属性，则使用它
        if (property_exists($this, 'logName') && !empty($this->logName)) {
            return $this->logName;
        }

        // 否则使用模型基础名称作为日志名称
        return strtolower(class_basename($this));
    }

    /**
     * 获取操作类型
     *
     * @return string
     */
    protected function getOperationType(): string
    {
        return strtolower(class_basename($this)) . '_management';
    }

    /**
     * 获取模型标签
     *
     * @return string
     */
    // protected function getModelLabel(): string
    // {
    //     $modelLabels = [
    //         'AdminUser' => '管理员',
    //         'Role' => '角色',
    //         'Permission' => '权限',
    //         'AdminDepartment' => '部门',
    //         'Client' => '客户端',
    //         'Enterprise' => '企业',
    //         'User' => '用户',
    //     ];

    //     $className = class_basename($this);
    //     return $modelLabels[$className] ?? $className;
    // }

    /**
     * 获取敏感字段列表
     *
     * @return array
     */
    protected function getSensitiveFields(): array
    {
        return property_exists($this, 'logSensitiveFields') 
            ? $this->logSensitiveFields 
            : [
                'password', 'password_confirmation', 'current_password',
                'secret', 'token', 'api_key', 'private_key',
                'mobile', 'phone', 'email', 'address',
                'id_card', 'id_number', 'bank_account', 'credit_card'
            ];
    }

    protected function shouldLogEvent(string $eventName): bool
    {
        // 1. 在这里添加您自己的前置规则。
        // 如果当前环境是控制台，或者请求的路由不匹配 'admin/*'，则直接返回 false。
        if (App::runningInConsole() || ! Request::is('admin/*')) {
            return false;
        }

        // 2. 如果您自己的规则通过了，那么调用被我们重命名后的 Trait 原始方法。
        // 使用 $this->traitShouldLogEvent($eventName) 来执行原始检查。
        return $this->traitShouldLogEvent($eventName);
    }
} 