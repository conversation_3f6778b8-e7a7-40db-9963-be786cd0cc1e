<?php

namespace App\Services\JwtWithSm2;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\JWTAuth\JWT;
use <PERSON><PERSON>\JWTAuth\JWTGuard;
use <PERSON><PERSON>\JWTAuth\Manager;

class SMJWTServiceProvider extends ServiceProvider
{
    public function register() {
        // 动态注册JWT驱动
        $this->registerJwtDrivers();

        // 保持向后兼容的旧式注册（如果新配置不存在）
        $this->registerLegacyDrivers();
    }

    /**
     * 动态注册JWT驱动
     */
    protected function registerJwtDrivers()
    {
        $drivers = config('auth.drivers', []);

        foreach ($drivers as $driverName => $driverConfig) {
            $this->registerJwtProvider($driverName, $driverConfig);
            $this->registerAuthGuard($driverName, $driverConfig);
        }
    }

    /**
     * 注册JWT Provider
     */
    protected function registerJwtProvider(string $driverName, array $config)
    {
        $providerServiceName = "tymon.jwt.provider.jwt.{$driverName}";

        $this->app->alias($providerServiceName, $config['provider_class']);

        $this->app->singleton($providerServiceName, function ($app) use ($config) {
            $providerClass = $config['provider_class'];

            return new $providerClass(
                $config['secret'],
                $config['algorithm'],
                $config['keys'],
                null,
                $config['keys']['with_key'] ?? false
            );
        });
    }

    /**
     * 注册认证Guard
     */
    protected function registerAuthGuard(string $driverName, array $driverConfig)
    {
        $this->app['auth']->extend($driverName, function ($app, $name, array $config) use ($driverName, $driverConfig) {
            // 获取配置的用户provider
            $provider = $app['auth']->createUserProvider($config['provider']);

            // 创建自定义的Payload Factory来支持驱动特定的TTL配置
            $payloadFactory = $this->createPayloadFactory($app, $driverConfig);

            // 创建JWT Manager
            $jwtProviderServiceName = "tymon.jwt.provider.jwt.{$driverName}";
            $jwtManager = new Manager(
                $app[$jwtProviderServiceName],
                $app['tymon.jwt.blacklist'],
                $payloadFactory
            );

            // 配置JWT Manager
            $jwtManager->setBlacklistEnabled((bool)($driverConfig['blacklist_enabled'] ?? true))
                       ->setPersistentClaims($driverConfig['persistent_claims'] ?? []);

            // 创建JWT实例
            $jwt = new JWT($jwtManager, $app['tymon.jwt.parser']);
            $jwt->lockSubject($driverConfig['lock_subject'] ?? true);

            // 创建Guard实例
            $guard = new JWTGuard($jwt, $provider, $app['request']);
            $app->refresh('request', $guard, 'setRequest');

            return $guard;
        });
    }

    /**
     * 创建支持驱动特定配置的Payload Factory
     */
    protected function createPayloadFactory($app, array $driverConfig)
    {
        // 获取原始的Payload Factory
        $originalFactory = $app['tymon.jwt.payload.factory'];

        // 克隆原始Factory以避免影响其他驱动
        $factory = clone $originalFactory;

        // 设置驱动特定的TTL配置
        if (isset($driverConfig['ttl'])) {
            $factory->setTTL($driverConfig['ttl']);
        }

        if (isset($driverConfig['refresh_ttl'])) {
            $factory->setRefreshTTL($driverConfig['refresh_ttl']);
        }

        return $factory;
    }

    /**
     * 保持向后兼容的旧式注册
     */
    protected function registerLegacyDrivers()
    {
        // 如果新配置中没有定义uc驱动，则使用旧的注册方式
        if (!config('auth.drivers.uc')) {
            $this->registerLegacyUcDriver();
        }

        // 如果新配置中没有定义ucadmin驱动，则使用旧的注册方式
        if (!config('auth.drivers.ucadmin')) {
            $this->registerLegacyUcAdminDriver();
        }
    }

    /**
     * 注册旧式UC驱动（向后兼容）
     */
    protected function registerLegacyUcDriver()
    {
        $this->app->alias('tymon.jwt.provider.jwt.sm', SMProvider::class);

        $this->app->singleton('tymon.jwt.provider.jwt.sm', function () {
            $secret = config('jwt.secret');
            $algorithm = config('jwt.algo', SMProvider::ALGO_SM2);
            $keys = config('jwt.keys.sm2');

            return new SMProvider($secret, $algorithm, $keys, null, config('jwt.keys.sm2.with_key'));
        });

        $this->app['auth']->extend('uc', function ($app, $name, array $config) {
            $provider = $app['auth']->createUserProvider($config['provider']);

            $jwtManager = new Manager($app['tymon.jwt.provider.jwt.sm'], $app['tymon.jwt.blacklist'], $app['tymon.jwt.payload.factory']);
            $jwtManager->setBlacklistEnabled((bool)config('jwt.blacklist_enabled'))
                       ->setPersistentClaims(config('jwt.persistent_claims'));

            $jwt = new JWT($jwtManager, $app['tymon.jwt.parser']);
            $jwt->lockSubject(config('jwt.lock_subject'));

            $guard = new JWTGuard($jwt, $provider, $app['request']);
            $app->refresh('request', $guard, 'setRequest');

            return $guard;
        });
    }

    /**
     * 注册旧式UC Admin驱动（向后兼容）
     */
    protected function registerLegacyUcAdminDriver()
    {
        $this->app->alias('tymon.jwt.provider.jwt.sm-admin', SMProvider::class);

        $this->app->singleton('tymon.jwt.provider.jwt.sm-admin', function () {
            $secret = config('jwt.secret');
            $algorithm = config('jwt.algo', SMProvider::ALGO_SM2);
            $keys = config('jwt.keys.sm2_admin');

            return new SMProvider($secret, $algorithm, $keys, null, config('jwt.keys.sm2_admin.with_key'));
        });

        $this->app['auth']->extend('ucadmin', function ($app, $name, array $config) {
            $provider = $app['auth']->createUserProvider($config['provider']);

            $jwtManager = new Manager($app['tymon.jwt.provider.jwt.sm-admin'], $app['tymon.jwt.blacklist'], $app['tymon.jwt.payload.factory']);
            $jwtManager->setBlacklistEnabled((bool)config('jwt.blacklist_enabled'))
                       ->setPersistentClaims(config('jwt.persistent_claims'));

            $jwt = new JWT($jwtManager, $app['tymon.jwt.parser']);
            $jwt->lockSubject(config('jwt.lock_subject'));

            $guard = new JWTGuard($jwt, $provider, $app['request']);
            $app->refresh('request', $guard, 'setRequest');

            return $guard;
        });
    }

    public function boot() {

    }
}
