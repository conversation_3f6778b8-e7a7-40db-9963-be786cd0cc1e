<?php

namespace App\Services\CloudFiles\Contracts;

interface CloudFilesInterface
{
    /**
     * 配置驱动
     *
     * @param array $config 配置信息
     *
     * @return $this
     */
    public function config(array $config);

    /**
     * 获取文件完整路径
     *
     * @param string $path 文件路径
     *
     * @return string
     */
    public function getPath(string $path): string;

    /**
     * 获取文件访问URL
     *
     * @param string $path 文件路径
     *
     * @return string
     */
    public function getUrl(string $path): string;

    /**
     * 获取临时访问URL
     *
     * @param string $path 文件路径
     * @param int $expire 过期时间(秒)
     * @param array $options 其他选项
     *
     * @return string
     */
    public function signatureUrl(string $path, int $expire = 3600, array $options = []): string;

    /**
     * 获取上传策略
     * 用于客户端直传场景
     *
     * @param string $callbackRoute 回调路由
     * @param string $dir 目录
     * @param int $expire 过期时间(秒)
     * @param int $contentLengthRange 内容长度范围
     *
     * @return array
     */
    public function policy(string $callbackRoute = '', string $dir = '', int $expire = 300, int $contentLengthRange = 1048576000): array;

    /**
     * 验证上传回调
     *
     * @return bool
     */
    public function verify(): bool;

    /**
     * 写入文件
     *
     * @param string $path 目标路径
     * @param string|resource $contents 文件内容
     * @param array|null $config 配置选项
     *
     * @return bool
     */
    public function write(string $path, $contents, ?array $config = null): bool;

    /**
     * 使用流写入文件
     *
     * @param string $path 目标路径
     * @param resource $resource 文件资源
     * @param array|null $config 配置选项
     *
     * @return bool
     */
    public function writeStream(string $path, $resource, ?array $config = null): bool;

    /**
     * 写入本地文件
     *
     * @param string $path 目标路径
     * @param string $filePath 本地文件路径
     * @param array|null $config 配置选项
     *
     * @return bool
     */
    public function writeFile(string $path, string $filePath, ?array $config = null): bool;

    /**
     * Base64写入文件
     *
     * @param string $path 目标路径
     * @param string $base64Data Base64编码的数据
     *
     * @return bool
     */
    public function base64(string $path, string $base64Data): bool;

    /**
     * 更新文件
     *
     * @param string $path 目标路径
     * @param string|resource $contents 文件内容
     * @param array|null $config 配置选项
     *
     * @return bool
     */
    public function update(string $path, $contents, ?array $config = null): bool;

    /**
     * 使用流更新文件
     *
     * @param string $path 目标路径
     * @param resource $resource 文件资源
     * @param array|null $config 配置选项
     *
     * @return bool
     */
    public function updateStream(string $path, $resource, ?array $config = null): bool;

    /**
     * 删除文件
     * 支持单个文件和批量删除
     *
     * @param string|array $path 文件路径或路径数组
     *
     * @return bool
     */
    public function delete($path): bool;

    /**
     * 复制文件
     *
     * @param string $path 源文件路径
     * @param string $newPath 目标路径
     *
     * @return bool
     */
    public function copy(string $path, string $newPath): bool;

    /**
     * 移动/重命名文件
     *
     * @param string $path 源文件路径
     * @param string $newPath 目标路径
     *
     * @return bool
     */
    public function rename(string $path, string $newPath): bool;

    /**
     * 删除目录
     *
     * @param string $dirname 目录名
     *
     * @return bool
     */
    public function deleteDir(string $dirname): bool;

    /**
     * 创建目录
     *
     * @param string $dirname 目录名
     *
     * @return bool
     */
    public function createDir(string $dirname): bool;

    /**
     * 判断文件是否存在
     *
     * @param string $path 文件路径
     *
     * @return bool
     */
    public function has(string $path): bool;

    /**
     * 读取文件内容
     *
     * @param string $path 文件路径
     *
     * @return array
     */
    public function read(string $path): array;

    /**
     * 读取文件流
     *
     * @param string $path 文件路径
     *
     * @return array
     */
    public function readStream(string $path): array;

    /**
     * 列出目录内容
     *
     * @param string $dirname 目录名
     * @param bool $recursive 是否递归
     *
     * @return array
     */
    public function listContents(string $dirname = '', bool $recursive = false): array;

    /**
     * 获取文件元数据
     *
     * @param string $path 文件路径
     *
     * @return array
     */
    public function getMetadata(string $path): array;

    /**
     * 设置文件访问权限
     *
     * @param string $path 文件路径
     * @param string $visibility 访问权限
     *
     * @return bool
     */
    public function setVisibility(string $path, string $visibility): bool;

    /**
     * 获取文件访问权限
     *
     * @param string $path 文件路径
     *
     * @return array
     */
    public function getVisibility(string $path): array;

    /**
     * 分片上传
     *
     * @param string $path 目标路径
     * @param string $filePath 本地文件路径
     * @param array $options 上传选项
     *
     * @return string|bool 成功返回文件URL，失败返回false
     */
    public function multiUpload(string $path, string $filePath, array $options = []): bool|string;

    /**
     * 创建软链接
     * 注意: 并非所有云存储都支持此功能
     *
     * @param string $path 目标路径
     * @param string $target 链接目标
     *
     * @return bool
     * @throws \App\Services\CloudFiles\Exceptions\CloudFilesException
     */
    public function createSymlink(string $path, string $target): bool;

    /**
     * 读取软链接
     * 注意: 并非所有云存储都支持此功能
     *
     * @param string $path 软链接路径
     *
     * @return string|null 返回链接目标路径,不存在则返回null
     * @throws \App\Services\CloudFiles\Exceptions\CloudFilesException
     */
    public function readSymlink(string $path): ?string;
}
