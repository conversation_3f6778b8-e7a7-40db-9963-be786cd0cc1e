<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Events\AdminDepartmentUpdateEvent;
use App\Exceptions\DepartmentException;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\DepartmentResource;
use App\Models\AdminDepartment;
use App\Models\Permission;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class AdminDepartmentsController extends AdminBaseController
{
    protected $validationMessages = [
        'name.required'        => '部门名称不能为空',
        'name.string'          => '部门名称必须是字符串',
        'name.max'             => '部门名称不能超过50个字符',
        'code.required'        => '部门编码不能为空',
        'code.string'          => '部门编码必须是字符串',
        'code.max'             => '部门编码不能超过50个字符',
        'code.unique'          => '部门编码已存在',
        'parent_id.exists'     => '上级部门不存在',
        'parent_id.required'   => '上级部门不能为空',
        'sort.integer'         => '排序值必须是整数',
        'status.required'      => '状态不能为空',
        'status.in'            => '状态值无效',
        'remark.string'        => '备注必须是字符串',
        'roles.array'          => '角色数据格式不正确',
        'roles.*.exists'       => '角色不存在',
        'permissions.array'    => '角色数据格式不正确',
        'permissions.*.exists' => '权限不存在',
    ];

    protected const PERMISSION_MAP = [
        'index'   => '部门管理.列表',
        'show'    => '部门管理.查看',
        'store'   => '部门管理.新建',
        'update'  => '部门管理.编辑',
        'destroy' => '部门管理.删除',
        'move'    => '部门管理.移动',
    ];

    /**
     * 获取部门列表
     */
    public function index(Request $request)
    {
        $query = AdminDepartment::query()
            ->with(['roles'])
            ->withCount('admins')
            ->when($request->input('keyword'), function ($query, $keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                        ->orWhere('code', 'like', "%{$keyword}%");
                });
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->input('status'));
            });

        // 如果指定了父部门ID，则获取其子部门
        if ($request->filled('parent_id')) {
            $parent = AdminDepartment::findOrFail($request->input('parent_id'));
            $query = $parent->descendants();

            // 如果只需要直接子部门
            if ($request->boolean('direct_children', true)) {
                $query->where('level', $parent->level + 1);
            }
        }

        $departments = $query->get();

        return Respond::success(DepartmentResource::collection($departments));
    }

    /**
     * 获取部门选项
     * @return \Illuminate\Http\JsonResponse
     */
    public function options()
    {
        return Respond::success([
            'status'    => AdminDepartment::$statusMap,
            'is_leader' => AdminDepartment::$isLeaderMap,
        ]);
    }


    public function show($id)
    {
        $department = AdminDepartment::with(['roles', 'permissions', 'parentDepartment'])
            ->findOrFail($id);

        //        $department->setRelation('parent', $department->parent());

        return Respond::success(DepartmentResource::make($department));
    }

    /**
     * 存储新部门
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name'          => 'required|string|max:50',
            'code'          => 'required|string|max:50|unique:' . AdminDepartment::class,
            'parent_id'     => 'nullable',
            'sort'          => 'nullable|integer',
            'status'        => 'required|in:0,1',
            'remark'        => 'nullable|string',
            'roles'         => 'nullable|array',
            'roles.*'       => 'sometimes|exists:' . Role::class . ',id',
            'permissions'   => 'nullable|array',
            'permissions.*' => 'sometimes|exists:' . Permission::class . ',id',
        ], $this->validationMessages);

        //        try {
        //            DB::beginTransaction();

        if ($validated['parent_id'] ?? false) {
            $parent = AdminDepartment::findOrFail($validated['parent_id']);

            // 为新节点腾出空间
            AdminDepartment::where('rgt', '>=', $parent->rgt)
                ->increment('rgt', 2);
            AdminDepartment::where('lft', '>', $parent->rgt)
                ->increment('lft', 2);

            $department = AdminDepartment::create([
                'name'      => $validated['name'],
                'code'      => $validated['code'],
                'parent_id' => $validated['parent_id'] ?? 0,
                'lft'       => $parent->rgt,
                'rgt'       => $parent->rgt + 1,
                'level'     => $parent->level + 1,
                'sort'      => $validated['sort'] ?? 0,
                'status'    => $validated['status'],
                'remark'    => $validated['remark'] ?? "",
            ]);
        } else {
            // 创建根节点
            $maxRgt = AdminDepartment::max('rgt') ?: 0;
            $department = AdminDepartment::create([
                'name'      => $validated['name'],
                'code'      => $validated['code'],
                'parent_id' => 0,
                'lft'       => $maxRgt + 1,
                'rgt'       => $maxRgt + 2,
                'level'     => 0,
                'sort'      => $validated['sort'] ?? 0,
                'status'    => $validated['status'],
                'remark'    => $validated['remark'] ?? "",
            ]);
        }

        // 为部门分配角色
        if (isset($validated['roles'])) {
            $department->syncRoles(array_map('intval', $validated['roles']));
        }

        // 为部门分配权限
        if (isset($validated['permissions'])) {
            $department->syncPermissions(array_map('intval', $validated['permissions']));
        }

        //            DB::commit();

        return Respond::success(DepartmentResource::make($department));
        //        } catch (\Exception $e) {
        //            DB::rollBack();
        //            throw new DepartmentException(ErrorCodeEnum::SYSTEM_ERROR);
        //        }
    }


    /**
     * 更新部门信息
     */
    public function update(Request $request, $id)
    {
        $department = AdminDepartment::findOrFail($id);

        $validated = $request->validate([
            'name'          => 'required|string|max:50',
            'code'          => [
                'required',
                'string',
                'max:50',
                Rule::unique('admin_departments')
                    ->ignore($id),
            ],
            'parent_id'     => [
                'nullable',
                'exists:' . AdminDepartment::class . ',id',
                function ($attribute, $value) use ($department) {
                    return $department->canMoveTo($value);
                },
            ],
            'sort'          => 'nullable|integer',
            'status'        => 'required|in:0,1',
            'remark'        => 'nullable|string',
            'roles'         => 'nullable|array',
            'roles.*'       => 'sometimes|exists:' . Role::class . ',id',
            'permissions'   => 'nullable|array',
            'permissions.*' => 'sometimes|exists:' . Permission::class . ',id',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            // 如果更改了父级部门,调用move方法进行移动
            if (isset($validated['parent_id']) && $validated['parent_id'] != $department->parent()?->id) {
                $this->moveNode($department, $validated['parent_id']);
            }

            // 更新基本信息
            $department->update([
                'name'      => $validated['name'],
                // 'code'      => $validated['code'],
                'parent_id' => $validated['parent_id'] ?? 0,
                'sort'      => $validated['sort'] ?? 0,
                'status'    => $validated['status'],
                'remark'    => $validated['remark'],
            ]);


            // 为部门分配角色
            if (isset($validated['roles'])) {
                $department->syncRoles(array_map('intval', $validated['roles']));
            }

            // 为部门分配权限
            if (isset($validated['permissions'])) {
                $department->syncPermissions(array_map('intval', $validated['permissions']));
            }

            DB::commit();

            event(new AdminDepartmentUpdateEvent($department));

            return Respond::success(DepartmentResource::make($department));
        } catch (DepartmentException $e) {
            DB::rollBack();
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DepartmentException(ErrorCodeEnum::SYSTEM_ERROR, [$e->getMessage()]);
        }
    }

    /**
     * 删除部门
     */
    public function destroy($id)
    {
        $department = AdminDepartment::findOrFail($id);

        try {
            DB::beginTransaction();

            // 检查是否存在子部门
            if ($department->hasChildren()) {
                throw new DepartmentException(ErrorCodeEnum::DEPARTMENT_HAS_CHILDREN);
            }

            // 检查是否存在关联用户
            if ($department->admins()
                ->exists()
            ) {
                throw new DepartmentException(ErrorCodeEnum::DEPARTMENT_HAS_ADMINS);
            }

            // 计算节点宽度
            $width = $department->rgt - $department->lft + 1;

            // 删除节点
            $department->delete();

            event(new AdminDepartmentUpdateEvent($department));

            // 更新其他节点的左右值
            AdminDepartment::where('lft', '>', $department->rgt)
                ->decrement('lft', $width);
            AdminDepartment::where('rgt', '>', $department->rgt)
                ->decrement('rgt', $width);

            DB::commit();

            return Respond::success();
        } catch (DepartmentException $e) {
            DB::rollBack();
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DepartmentException(ErrorCodeEnum::SYSTEM_ERROR, [$e->getMessage()]);
        }
    }

    /**
     * 移动部门
     * @throws DepartmentException
     */
    public function move(Request $request, $id)
    {
        $department = AdminDepartment::findOrFail($id);

        $validated = $request->validate([
            'parent_id' => [
                'required',
                'exists:' . AdminDepartment::class . ',id',
                function ($attribute, $value) use ($department) {
                    return $department->canMoveTo($value);
                },
            ],
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $this->moveNode($department, $validated['parent_id']);

            DB::commit();

            event(new AdminDepartmentUpdateEvent($department));

            return Respond::success();
        } catch (DepartmentException $e) {
            DB::rollBack();
            throw $e;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw new DepartmentException(ErrorCodeEnum::SYSTEM_ERROR, [$e->getMessage()]);
        }
    }

    /**
     * 移动部门节点的核心逻辑
     *
     * @param AdminDepartment $department 要移动的部门
     * @param int|null $parentId 目标父级ID
     *
     * @throws DepartmentException
     */
    protected function moveNode(AdminDepartment $department, $parentId = null)
    {
        // 保存当前节点信息
        $oldLft = $department->lft;
        $oldRgt = $department->rgt;
        $width = $oldRgt - $oldLft + 1;
        $distance = 0;

        if ($parentId) {
            $newParent = AdminDepartment::findOrFail($parentId);

            // 再次验证新父级的合法性
            if (!$department->canMoveTo($parentId)) {
                throw new DepartmentException(ErrorCodeEnum::INVALID_PARENT_NODE);
            }

            $newLevel = $newParent->level + 1;

            // 计算新的左值位置
            $newLft = $newParent->rgt;

            // 如果是向右移动，需要考虑空位的影响
            if ($oldLft < $newLft) {
                $newLft -= $width;
                // 调整移动距离，考虑删除原节点的影响
                $distance = $newLft - $oldLft;
            } else {
                // 向左移动
                $distance = $newLft - $oldLft;
            }

            // 临时存储节点数据
            AdminDepartment::where('lft', '>=', $oldLft)
                ->where('rgt', '<=', $oldRgt)
                ->update([
                    'lft' => DB::raw('lft + 1000000'),
                    'rgt' => DB::raw('rgt + 1000000'),
                ]);

            // 关闭原来的空隙
            AdminDepartment::where('lft', '>', $oldRgt)
                ->update(['lft' => DB::raw('lft - ' . $width)]);
            AdminDepartment::where('rgt', '>', $oldRgt)
                ->update(['rgt' => DB::raw('rgt - ' . $width)]);

            // 为新位置腾出空间
            AdminDepartment::where('lft', '>=', $newLft)
                ->update(['lft' => DB::raw('lft + ' . $width)]);
            AdminDepartment::where('rgt', '>=', $newLft)
                ->update(['rgt' => DB::raw('rgt + ' . $width)]);

            // 移动节点到新位置
            AdminDepartment::where('lft', '>=', 1000000)
                ->update([
                    'lft'   => DB::raw('lft - 1000000 + ' . $distance),
                    'rgt'   => DB::raw('rgt - 1000000 + ' . $distance),
                    'level' => DB::raw('level - ' . $department->level . ' + ' . $newLevel),
                ]);

            $department->parent_id = $parentId;
            $department->save();
        } else {
            // 移动到根节点
            $maxRgt = AdminDepartment::max('rgt');
            $newLft = $maxRgt + 1;
            $distance = $newLft - $oldLft;

            // 临时移动到安全区域
            AdminDepartment::where('lft', '>=', $oldLft)
                ->where('rgt', '<=', $oldRgt)
                ->update([
                    'lft' => DB::raw('lft + 1000000'),
                    'rgt' => DB::raw('rgt + 1000000'),
                ]);

            // 关闭原来的空隙
            AdminDepartment::where('lft', '>', $oldRgt)
                ->update(['lft' => DB::raw('lft - ' . $width)]);
            AdminDepartment::where('rgt', '>', $oldRgt)
                ->update(['rgt' => DB::raw('rgt - ' . $width)]);

            // 移动到新位置
            AdminDepartment::where('lft', '>=', 1000000)
                ->update([
                    'lft'   => DB::raw('lft - 1000000 + ' . $distance),
                    'rgt'   => DB::raw('rgt - 1000000 + ' . $distance),
                    'level' => DB::raw('level - ' . $department->level),
                ]);

            $department->parent_id = null;
            $department->save();
        }

        // 刷新当前模型实例的属性
        $department->refresh();
    }

    /**
     * 批量导入部门
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchImport(Request $request)
    {
        $validated = $request->validate([
            'departments' => 'required|string',
        ], [
            'departments.required' => '部门数据不能为空',
            'departments.string' => '部门数据必须是字符串',
        ]);

        $lines = explode("\n", $validated['departments']);
        $lines = array_map('rtrim', $lines); // 移除每行末尾的空白字符
        $lines = array_filter($lines); // 移除空行

        $result = [
            'success' => 0,
            'skipped' => 0,
            'failed' => 0,
            'details' => [],
        ];

        try {
            DB::beginTransaction();

            // 用于跟踪每个缩进级别对应的父部门ID
            $parentStack = [null]; // 初始为根级别
            $lastIndent = 0;

            foreach ($lines as $line) {
                // 计算当前行的缩进级别（每4个空格为一级）
                $indent = 0;
                while (mb_substr($line, $indent, 1) === ' ') {
                    $indent++;
                }
                $indent = intval($indent / 4);

                // 提取部门名称
                $name = trim($line);

                // 如果缩进比上一行小，需要回退父级栈
                if ($indent < $lastIndent) {
                    $diff = $lastIndent - $indent;
                    for ($i = 0; $i < $diff; $i++) {
                        array_pop($parentStack);
                    }
                }
                // 如果缩进比上一行大超过1级，说明数据有问题
                elseif ($indent > $lastIndent + 1) {
                    $result['failed']++;
                    $result['details'][] = [
                        'name' => $name,
                        'status' => 'failed',
                        'message' => '缩进错误，不能跳级'
                    ];
                    continue;
                }

                // 获取当前部门的父ID
                $parentId = end($parentStack);

                // 检查同级同名部门是否已存在
                $existingDepartment = AdminDepartment::where('name', $name)
                    ->where('parent_id', $parentId)
                    ->first();

                if ($existingDepartment) {
                    $result['skipped']++;
                    $result['details'][] = [
                        'name' => $name,
                        'status' => 'skipped',
                        'message' => '同级同名部门已存在'
                    ];

                    // 即使跳过创建，也需要将其ID加入父级栈，以便后续子部门能正确挂载
                    if ($indent >= $lastIndent) {
                        $parentStack[] = $existingDepartment->id;
                    }

                    $lastIndent = $indent;
                    continue;
                }

                // 创建新部门
                try {
                    if ($parentId) {
                        $parent = AdminDepartment::findOrFail($parentId);

                        // 为新节点腾出空间
                        AdminDepartment::where('rgt', '>=', $parent->rgt)
                            ->increment('rgt', 2);
                        AdminDepartment::where('lft', '>', $parent->rgt)
                            ->increment('lft', 2);

                        $department = AdminDepartment::create([
                            'name' => $name,
                            'code' => $this->generateUniqueCode($name),
                            'parent_id' => $parentId,
                            'lft' => $parent->rgt,
                            'rgt' => $parent->rgt + 1,
                            'level' => $parent->level + 1,
                            'sort' => 0,
                            'status' => AdminDepartment::STATUS_ENABLED,
                            'remark' => "",
                        ]);
                    } else {
                        // 创建根节点
                        $maxRgt = AdminDepartment::max('rgt') ?: 0;
                        $department = AdminDepartment::create([
                            'name' => $name,
                            'code' => $this->generateUniqueCode($name),
                            'parent_id' => null,
                            'lft' => $maxRgt + 1,
                            'rgt' => $maxRgt + 2,
                            'level' => 0,
                            'sort' => 0,
                            'status' => AdminDepartment::STATUS_ENABLED,
                            'remark' => "",
                        ]);
                    }

                    $result['success']++;
                    $result['details'][] = [
                        'name' => $name,
                        'status' => 'success',
                        'id' => $department->id
                    ];

                    // 如果当前行的缩进大于等于上一行，将其ID加入父级栈
                    if ($indent >= $lastIndent) {
                        $parentStack[] = $department->id;
                    }

                    $lastIndent = $indent;
                } catch (\Exception $e) {
                    $result['failed']++;
                    $result['details'][] = [
                        'name' => $name,
                        'status' => 'failed',
                        'message' => $e->getMessage()
                    ];
                }
            }

            DB::commit();
            return Respond::success($result);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DepartmentException(ErrorCodeEnum::SYSTEM_ERROR, [$e->getMessage()]);
        }
    }

    /**
     * 生成唯一的部门编码
     * 
     * @param string $name 部门名称
     * @return string 唯一编码
     */
    protected function generateUniqueCode($name)
    {
        // 基础编码：使用部门名称拼音首字母+时间戳后6位
        $code = $this->getPinyinInitials($name) . substr(time(), -6);

        // 检查编码是否已存在，如果存在则添加随机数
        $exists = AdminDepartment::where('code', $code)->exists();
        if ($exists) {
            $code .= rand(100, 999);
        }

        return $code;
    }

    /**
     * 获取中文字符串的拼音首字母
     * 
     * @param string $str 中文字符串
     * @return string 拼音首字母
     */
    protected function getPinyinInitials($str)
    {
        // 简单实现，实际项目中可以使用专门的拼音转换库
        // 这里仅作示例，返回字符串的前3个字符的大写形式
        $str = preg_replace('/[^\p{L}\p{N}]/u', '', $str); // 移除特殊字符
        return strtoupper(substr($str, 0, 3));
    }
}
