<?php

namespace App\Models;

use App\Enums\OAuthJsApiEnum;
use App\Enums\OAuthScopeEnum;
use App\Services\CloudFiles\Facades\CloudFiles;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string|null $enterprise_id 关联企业ID
 * @property int|null $category_id 分类ID
 * @property string $name
 * @property array<array-key, mixed>|null $icon 图标
 * @property string $provider
 * @property string|null $description
 * @property string $client_key
 * @property string $client_access_key
 * @property string $client_access_secret
 * @property array<array-key, mixed>|null $auth_safe_domains 安全域名
 * @property string|null $callback_url 回调地址
 * @property string|null $default_redirect_url 默认跳转地址
 * @property array<array-key, mixed>|null $white_ips 白名单IP
 * @property string $client_type
 * @property int $is_system 是否系统级
 * @property array<array-key, mixed>|null $allowed_scopes
 * @property array<array-key, mixed>|null $allowed_admin_scopes 允许的管理员权限范围
 * @property array<array-key, mixed>|null $allowed_jsapis
 * @property array<array-key, mixed>|null $disabled_jsapis 禁用的 JSAPI
 * @property int $is_revoked
 * @property int|null $status 状态:2待审核,1审核通过,0审核不通过
 * @property string|null $created_by 创建人ID
 * @property string|null $updated_by 更新人ID
 * @property string|null $review_by 审核人ID
 * @property string|null $review_at 审核时间
 * @property string|null $review_remark 审核备注
 * @property int $is_workspace_client 是否为工作区客户端
 * @property int $show_in_workspace 是否在工作区展示
 * @property int|null $show_in_matrix 是否在矩阵中展示
 * @property string|null $workspace_redirect_url 工作区重定向地址
 * @property int|null $sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AdminUser> $accessibleAdmins
 * @property-read int|null $accessible_admins_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AdminDepartment> $accessibleDepartments
 * @property-read int|null $accessible_departments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AuthCode> $authCodes
 * @property-read int|null $auth_codes_count
 * @property-read \App\Models\ClientCategory|null $category
 * @property-read mixed $display_icon
 * @property-read \App\Models\Enterprise|null $enterprise
 * @property-read mixed $is_workspace_client_text
 * @property-read mixed $revoked_text
 * @property-read mixed $show_in_workspace_text
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OAuthUserAuthorization> $userAuthorizations
 * @property-read int|null $user_authorizations_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OAuthUserBinding> $userBindings
 * @property-read int|null $user_bindings_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client byEnterprise($enterpriseId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereAllowedAdminScopes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereAllowedJsapis($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereAllowedScopes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereAuthSafeDomains($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereCallbackUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereClientAccessKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereClientAccessSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereClientKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereClientType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereDefaultRedirectUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereDisabledJsapis($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereEnterpriseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereIsRevoked($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereIsSystem($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereIsWorkspaceClient($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereReviewAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereReviewBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereReviewRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereShowInMatrix($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereShowInWorkspace($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereWhiteIps($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client whereWorkspaceRedirectUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Client withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperClient
 */
class Client extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'oauth_clients';

    protected $hidden = [
        'client_access_key',
        'client_access_secret',
    ];

    protected $fillable = [
        'name',
        'icon',
        'provider',
        'description',
        'client_key',
        'auth_safe_domains',
        'callback_url',
        'client_type',
        'allowed_scopes',
        'allowed_admin_scopes',
        'allowed_jsapis',
        'disabled_jsapis',
        'is_revoked',
        'default_redirect_url',
        'white_ips',
        'is_workspace_client',
        'show_in_workspace',
        'workspace_redirect_url',
        'enterprise_id',
        'category_id',
        'show_in_matrix',
        'enterprise_id',
    ];

    protected $casts = [
        'auth_safe_domains'    => 'json',
        'icon'                 => 'array',
        'allowed_scopes'       => 'json',
        'allowed_admin_scopes' => 'json',
        'allowed_jsapis'       => 'json',
        'disabled_jsapis'      => 'json',
        'white_ips'            => 'json',
    ];

    protected $appends = [
        'display_icon',
        'revoked_text',
    ];

    // const CLIENT_TYPE_BACKEND = 'backend';
    const CLIENT_TYPE_MINIAPP = 'miniapp';
    const CLIENT_TYPE_H5 = 'h5';

    public static array $clientTypeMap = [
        // self::CLIENT_TYPE_BACKEND => '后台管理',
        self::CLIENT_TYPE_MINIAPP => '小程序',
        self::CLIENT_TYPE_H5      => 'H5',
    ];

    const IS_SYSTEM_NO = 0;
    const IS_SYSTEM_YES = 1;

    public static array $isSystemMap = [
        self::IS_SYSTEM_YES => '是',
        self::IS_SYSTEM_NO  => '否',
    ];

    const IS_REVOKED_NO = 0;
    const IS_REVOKED_YES = 1;

    public static array $revokedMap = [
        self::IS_REVOKED_YES => '是',
        self::IS_REVOKED_NO  => '否',
    ];

    const SHOW_IN_WORKSPACE_NO = 0;
    const SHOW_IN_WORKSPACE_YES = 1;

    public static array $showInWorkspaceMap = [
        self::SHOW_IN_WORKSPACE_YES => '是',
        self::SHOW_IN_WORKSPACE_NO  => '否',
    ];

    const SHOW_IN_MATRIX_NO = 0;
    const SHOW_IN_MATRIX_YES = 1;

    public static array $showInMatrixMap = [
        self::SHOW_IN_MATRIX_YES => '是',
        self::SHOW_IN_MATRIX_NO  => '否',
    ];

    const IS_WORKSPACE_CLIENT_NO = 0;
    const IS_WORKSPACE_CLIENT_YES = 1;

    public static array $isWorkspaceClientMap = [
        self::IS_WORKSPACE_CLIENT_YES => '是',
        self::IS_WORKSPACE_CLIENT_NO  => '否',
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    public static array $statusMap = [
        self::STATUS_ACTIVE   => '启用',
        self::STATUS_INACTIVE => '禁用',
    ];

    protected function isWorkspaceClientText(): Attribute {
        return Attribute::make(get: fn() => self::$isWorkspaceClientMap[$this->is_workspace_client] ?? '未知');
    }

    protected function showInWorkspaceText(): Attribute {
        return Attribute::make(get: fn() => self::$showInWorkspaceMap[$this->show_in_workspace] ?? '未知');
    }

    protected function revokedText(): Attribute {
        return Attribute::make(get: fn() => self::$revokedMap[$this->is_revoked] ?? '未知');
    }

    protected function displayIcon(): Attribute {
        return Attribute::make(get: fn() => $this->icon ? CloudFiles::getFileUrl($this->icon['provider'], $this->icon['path'], config('uc.avatar_expired_ttl')) : CloudFiles::getFileUrl('url', config('uc.default_avatar'), config('uc.avatar_expired_ttl')));
    }

    public function authCodes() {
        return $this->hasMany(AuthCode::class, 'client_id', 'id');
    }

    public function tokens() {
        return $this->hasMany(AccessToken::class, 'client_id', 'id');
    }

    public function hasGrantType($grantType) {
        if (!isset($this->attributes['grant_types']) || !is_array($this->grant_types)) {
            return true;
        }

        return in_array($grantType, $this->grant_types);
    }

    public function confidential() {
        return !empty($this->client_access_secret);
    }

    public function userBindings() {
        return $this->hasMany(OAuthUserBinding::class);
    }

    public function userAuthorizations() {
        return $this->hasMany(OAuthUserAuthorization::class);
    }

    public function scopeActive($query) {
        return $query->where('is_revoked', self::IS_REVOKED_NO);
    }

    public function scopeByEnterprise($query, $enterpriseId) {
        return $query->where('enterprise_id', $enterpriseId);
    }

    public function isUserBound($userUuid) {
        return $this->userBindings()
                    ->where('user_uuid', $userUuid)
                    ->exists();
    }

    public function getUserOpenId($userUuid) {
        return $this->userBindings()
                    ->where('user_uuid', $userUuid)
                    ->value('open_id');
    }

    public static function availableScopes(): array {
        return OAuthScopeEnum::globalOptions();
    }

    public static function availableAdminScopes(): array {
        return OAuthScopeEnum::adminOptions();
    }

    public static function availableJsApis(): array {
        return OAuthJsApiEnum::systemOptions();
    }

    public function category()
    {
        return $this->belongsTo(ClientCategory::class, 'category_id', 'id');
    }

    // 获取当前客户端允许的 scopes 及其说明
    public function getAllowedScopesWithLabels(): Collection {
        return collect($this->allowed_scopes)->map(function ($scope) {
            return [
                'key'   => $scope,
                'value' => OAuthScopeEnum::from($scope)
                                         ->label(),
            ];
        });
    }

    public function getAllowedAdminScopesWithLabels(): Collection {
        return collect($this->allowed_admin_scopes)->map(function ($scope) {
            return [
                'key'   => $scope,
                'value' => OAuthScopeEnum::from($scope)
                                         ->label(),
            ];
        });
    }

    // 获取当前客户端允许的 jsapis 及其说明
    public function getAllowedJsApisWithLabels(): Collection {
        return collect($this->allowed_jsapis)->map(function ($jsapi) {
            return [
                'key'   => $jsapi,
                'value' => OAuthJsApiEnum::from($jsapi)
                                         ->label(),
            ];
        });
    }

    // 验证 scope 是否有效
    public function validateScope(string $scope): bool {
        return in_array($scope, array_column(OAuthScopeEnum::cases(), 'value'));
    }

    // 验证 jsapi 是否有效
    public function validateJsApi(string $jsapi): bool {
        return in_array($jsapi, array_column(OAuthJsApiEnum::cases(), 'value'));
    }
    

    // 检查是否拥有特定 scope 权限
    public function hasScope(string $scope): bool {
        if (!$this->validateScope($scope)) {
            return false;
        }

        return in_array($scope, $this->allowed_scopes ?? []);
    }

    public function hasAdminScope(string $scope): bool {
        if (!$this->validateScope($scope)) {
            return false;
        }

        return in_array($scope, $this->allowed_admin_scopes ?? []);
    }

    // 检查是否拥有特定 jsapi 权限
    public function hasJsApi(string $jsapi): bool {
        if (!$this->validateJsApi($jsapi)) {
            return false;
        }

        return in_array($jsapi, $this->allowed_jsapis ?? []);
    }

    // 添加到 Client 模型
    // 可访问此客户端的部门多态关联
    public function accessibleDepartments() {
        return $this->morphedByMany(AdminDepartment::class, 'accessible', 'oauth_client_accessibles', 'oauth_client_id', 'accessible_id')
                    ->withTimestamps();
    }

    // 可访问此客户端的管理员多态关联
    public function accessibleAdmins() {
        return $this->morphedByMany(AdminUser::class, 'accessible', 'oauth_client_accessibles', 'oauth_client_id', 'accessible_id')
                    ->withTimestamps();
    }

    // 获取所有可访问此客户端的管理员（包括通过部门继承的）
    public function getAllAccessibleAdmins() {
        // 直接可访问的管理员
        $directAdmins = $this->accessibleAdmins;

        // 获取有权限访问的所有部门ID
        $departmentIds = $this->accessibleDepartments->pluck('id')
                                                     ->toArray();

        // 找出所有这些部门的子部门
        $allDepartmentIds = AdminDepartment::whereIn('id', $departmentIds)
                                           ->get()
                                           ->flatMap(function ($department) {
                                               return AdminDepartment::where('lft', '>=', $department->lft)
                                                                     ->where('rgt', '<=', $department->rgt)
                                                                     ->pluck('id')
                                                                     ->toArray();
                                           })
                                           ->unique()
                                           ->toArray();

        // 获取这些部门中的所有管理员
        $departmentAdminUuids = \DB::table('admin_department_admin_user')
                                   ->whereIn('admin_department_id', $allDepartmentIds)
                                   ->pluck('admin_user_uuid')
                                   ->unique()
                                   ->toArray();

        // 获取部门管理员
        $departmentAdmins = AdminUser::whereIn('uuid', $departmentAdminUuids)
                                     ->where('status', AdminUser::STATUS_ENABLED)
                                     ->get();

        // 合并去重
        return $directAdmins->merge($departmentAdmins)
                            ->unique('uuid');
    }

    /**
     * 企业关联
     */
    public function enterprise()
    {
        return $this->belongsTo(Enterprise::class, 'enterprise_id', 'id');
    }

    /**
     * 根据应用关联的企业获取部门列表
     */
    public function getEnterpriseAdminDepartments()
    {
        if (!$this->enterprise_id) {
            return collect();
        }
        
        return AdminDepartment::where('enterprise_id', $this->enterprise_id)
            ->where('status', AdminDepartment::STATUS_ENABLED)
            ->get();
    }
    
    /**
     * 根据应用关联的企业获取管理员用户列表
     */
    public function getEnterpriseAdminUsers()
    {
        if (!$this->enterprise_id) {
            return collect();
        }
        
        return AdminUser::where('enterprise_id', $this->enterprise_id)
            ->where('status', AdminUser::STATUS_ENABLED)
            ->get();
    }
    
    /**
     * 检查是否关联了企业
     */
    public function hasEnterprise(): bool
    {
        return !empty($this->enterprise_id);
    }
}
