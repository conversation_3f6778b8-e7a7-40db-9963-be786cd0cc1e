<?php
namespace App\Utils;

class OpenIDGenerater {
    private const PRIME_FACTOR = 1597;
    private const SHIFT_FACTOR = 3721;

    private $letterMap = [
        0 => 'A', 1 => 'B', 2 => 'C', 3 => 'D', 4 => 'E', 5 => 'F',
        6 => 'G', 7 => 'H', 8 => 'I', 9 => 'J', 10 => 'K', 11 => 'L',
        12 => 'M', 13 => 'N', 14 => 'O', 15 => 'P', 16 => 'Q', 17 => 'R',
        18 => 'S', 19 => 'T', 20 => 'U', 21 => 'V', 22 => 'W', 23 => 'X',
        24 => 'Y', 25 => 'Z', 26 => 'a', 27 => 'b', 28 => 'c', 29 => 'd',
        30 => 'e', 31 => 'f', 32 => 'g', 33 => 'h', 34 => 'i', 35 => 'j',
        36 => 'k', 37 => 'l', 38 => 'm', 39 => 'n', 40 => 'o', 41 => 'p',
        42 => 'q', 43 => 'r', 44 => 's', 45 => 't', 46 => 'u', 47 => 'v',
        48 => 'w', 49 => 'x', 50 => 'y', 51 => 'z'
    ];

    private function transformClientId(int $clientId): int
    {
        $value = $clientId;
        $value = ($value * self::PRIME_FACTOR + self::SHIFT_FACTOR) % 140608;
        return $value ?: 1;
    }

    public function generatePrefix(int $clientId): string
    {
        $transformed = $this->transformClientId($clientId);

        $chars = [];
        for ($i = 0; $i < 3; $i++) {
            $index = $transformed % 52;
            $transformed = (int)($transformed / 52);
            $chars[] = $this->letterMap[$index];
        }

        return implode('', array_reverse($chars));
    }

    public function generateOpenID(int $clientId, string $userUuid): string
    {
        $prefix = $this->generatePrefix($clientId);

        $randomBytes = random_bytes(32);
        $rawString = $userUuid . '|' . microtime(true) . '|' . bin2hex($randomBytes);
        $hash = hash('sha256', $rawString);
        $suffix = substr($hash, 0, 36);

        return $prefix . '_' . $suffix;
    }
}
