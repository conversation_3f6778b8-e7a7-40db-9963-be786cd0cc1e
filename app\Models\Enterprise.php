<?php

namespace App\Models;

use App\Services\CloudFiles\Facades\CloudFiles;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

/**
 * @property string $id
 * @property string $name 企业名称
 * @property string|null $code 企业代码
 * @property string|null $description 企业描述
 * @property string|null $contact_person 联系人
 * @property string|null $contact_phone 联系电话
 * @property string|null $address 地址
 * @property array<array-key, mixed>|null $logo 企业logo
 * @property int $status 状态:1启用,0禁用
 * @property string|null $settings 设置
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AdminUser> $adminUsers
 * @property-read int|null $admin_users_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Client> $clients
 * @property-read int|null $clients_count
 * @property-read \Kalnoy\Nestedset\Collection<int, \App\Models\EnterpriseDepartment> $departments
 * @property-read int|null $departments_count
 * @property-read mixed $display_logo
 * @property-read mixed $status_text
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Client> $workspaceClients
 * @property-read int|null $workspace_clients_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereContactPerson($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereLogo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Enterprise withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperEnterprise
 */
class Enterprise extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        'name',
        'code',
        'description',
        'contact_person',
        'contact_phone',
        'address',
        'logo',
        'status',
    ];

    protected $casts = [
        'logo' => 'array',
    ];

    protected $appends = [
        'display_logo',
        'status_text',
    ];

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    public static array $statusMap = [
        self::STATUS_ENABLED  => '启用',
        self::STATUS_DISABLED => '禁用',
    ];

    /**
     * 模型初始化事件
     */
    protected static function boot()
    {
        parent::boot();

        // 创建前生成UUID
        static::creating(function ($model) {
            $model->id = (string) Str::uuid();
        });
    }

    /**
     * 状态文本属性
     */
    protected function statusText(): Attribute
    {
        return Attribute::make(get: fn() => self::$statusMap[$this->status] ?? '未知');
    }

    /**
     * 显示logo属性
     */
    protected function displayLogo(): Attribute
    {
        return Attribute::make(get: fn() => $this->logo ? CloudFiles::getFileUrl($this->logo['provider'], $this->logo['path'], config('uc.avatar_expired_ttl')) : CloudFiles::getFileUrl('url', config('uc.default_avatar'), config('uc.avatar_expired_ttl')));
    }

    /**
     * 关联管理员用户
     */
    public function adminUsers()
    {
        return $this->hasMany(AdminUser::class, 'enterprise_id', 'id');
    }

    /**
     * 关联部门
     */
    public function departments()
    {
        return $this->hasMany(EnterpriseDepartment::class, 'enterprise_id', 'id');
    }

    public function clients()
    {
        return $this->hasMany(Client::class, 'enterprise_id', 'id');
    }

    /**
     * 关联的Workspace应用
     */
    public function workspaceClients()
    {
        return $this->hasMany(Client::class, 'enterprise_id', 'id')
            ->where('is_workspace_client', Client::IS_WORKSPACE_CLIENT_YES);
    }

    /**
     * 关联企业管理员
     */
    public function enterpriseAdmins()
    {
        return $this->hasMany(EnterpriseAdmin::class, 'enterprise_id', 'id');
    }

    /**
     * 关联启用的企业管理员
     */
    public function activeEnterpriseAdmins()
    {
        return $this->enterpriseAdmins()->enabled();
    }

    /**
     * 检查企业是否激活
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ENABLED;
    }
}