<?php

namespace App\Utils;

/**
 * URL构建器，用于生成各种模块的跳转链接
 *
 * @method static string rongmei(string $type, string|int $id = '') 生成融媒模块链接
 * @method static string changguanhao(string $type, string|int $id = '') 生成常观号模块链接
 * @method static string quanzi(string $type, string|int $id = '') 生成圈子模块链接
 * @method static string external(string $url, array $extraData = []) 生成外部链接跳转
 * @method static string wxMiniApp(string $appId, ?string $programId, string $page, array $extraData = []) 生成微信小程序链接
 * @method static string cgMiniApp(string $appId, ?string $page, array $extraData = []) 生成常观小程序链接
 * @method static string aliMiniApp(string $appId, ?string $page, array $extraData = []) 生成支付宝小程序链接
 * @method static string bangbang(string $type, string|int $id = '') 生成帮帮模块链接
 * @method static string haosh<PERSON><PERSON>o(string $type, string|int $id = '') 生成好生活模块链接
 * @method static bool isValidRedirectTo(string $url) 是否为合法链接, 用于判断是否为跳转链接
 *
 * @method string rongmei(string $type, string|int $id = '') 生成融媒模块链接
 * @method string changguanhao(string $type, string|int $id = '') 生成常观号模块链接
 * @method string quanzi(string $type, string|int $id = '') 生成圈子模块链接
 * @method string external(string $url, array $extraData = []) 生成外部链接跳转
 * @method string wxMiniApp(string $appId, ?string $page, array $extraData = []) 生成微信小程序链接
 * @method string cgMiniApp(string $appId, ?string $page, array $extraData = []) 生成常观小程序链接
 * @method string aliMiniApp(string $appId, ?string $page, array $extraData = []) 生成支付宝小程序链接
 * @method string bangbang(string $type, string|int $id = '') 生成帮帮模块链接
 * @method string haoshenghuo(string $type, string|int $id = '') 生成好生活模块链接
 * @method bool isValidRedirectTo(string $url) 是否为合法链接, 用于判断是否为跳转链接
 *
 */
class RedirectToBuilder
{
    private const SCHEME = 'changguan://';

    private const MODULE_RONGMEI = 'rongmei';
    private const MODULE_BANGBANG = 'bangbang';
    private const MODULE_QUANZI = 'quanzi';
    private const MODULE_CHANGGUANHAO = 'changguanhao';
    private const MODULE_HAOSHENGHUO = 'haoshenghuo';
    private const MODULE_QIANBAO = 'qianbao';
    private const MODULE_WXMINIAPP = 'wxminiapp';
    private const MODULE_CGMINIAPP = 'cgminiapp';
    private const MODULE_ALIMINIAPP = 'aliminiapp';
    private const MODULE_EXTERNAL = 'external';

    /**
     * @var string|null 当前模块名
     */
    private $module;

    /**
     * @var string|null 内容类型
     */
    private $type;

    /**
     * @var string|int|null 内容ID
     */
    private $id;

    /**
     * @var string|null 小程序页面
     */
    private $page;

    /**
     * @var array 小程序额外参数
     */
    private $extraData;

    /**
     * @var string|null 微信小程序ID
     */
    private $miniProgramId;

    /**
     * @var string|null 小程序AppID
     */
    private $miniProgramAppId;

    /**
     * @var string|null 外部跳转URL
     */
    private $url;

    public function __construct() {
        $this->reset();
    }

    /**
     * @param string $method
     * @param array $arguments
     *
     * @return string
     * @throws \BadMethodCallException
     */
    public static function __callStatic($method, $arguments) {
        $instance = new self();

        return $instance->$method(...$arguments);
    }

    /**
     * @param string $method
     * @param array $arguments
     *
     * @return string
     * @throws \BadMethodCallException
     */
    public function __call($method, $arguments) {
        if (empty($method)) {
            return '';
        }

        if (method_exists($this, "handle{$method}")) {
            return $this->{"handle{$method}"}(...$arguments);
        }

        throw new \BadMethodCallException("Method {$method} does not exist");
    }

    /**
     * 跳转融媒文稿模块
     *
     * @param string $type 内容类型
     * @param string|int $id
     *
     * @return string
     */
    private function handleRongmei(string $type, string|int $id = ''): string {
        $this->module = self::MODULE_RONGMEI;
        $this->type = $type;
        $this->id = $id;

        return $this->buildUrl();
    }

    /**
     * 跳转常观号模块
     *
     * @param string $type 内容类型
     * @param string|int $id
     *
     * @return string
     */
    private function handleChangguanhao(string $type, string|int $id = ''): string {
        $this->module = self::MODULE_CHANGGUANHAO;
        $this->type = $type;
        $this->id = $id;

        return $this->buildUrl();
    }

    /**
     * 跳转帮帮模块
     *
     * @param string $type
     * @param string|int $id
     *
     * @return string
     */
    private function handleBangbang(string $type, string|int $id = ''): string {
        $this->module = self::MODULE_BANGBANG;
        $this->type = $type;
        $this->id = $id;

        return $this->buildUrl();
    }

    /**
     * 跳转好生活模块
     *
     * @param string $type
     * @param string|int $id
     *
     * @return string
     */
    private function handleHaoshenghuo(string $type, string|int $id = ''): string {
        $this->module = self::MODULE_HAOSHENGHUO;
        $this->type = $type;
        $this->id = $id;

        return $this->buildUrl();
    }

    /**
     * 跳转钱包模块
     *
     * @param string $type
     * @param string|int $id
     *
     * @return string
     */
    private function handleQianbao(string $type, string|int $id = ''): string {
        $this->module = self::MODULE_QIANBAO;
        $this->type = $type;
        $this->id = $id;

        return $this->buildUrl();
    }

    /**
     * 跳转圈子模块
     *
     * @param string $type 内容类型
     * @param string|int $id
     *
     * @return string
     */
    private function handleQuanzi(string $type, string|int $id = ''): string {
        $this->module = self::MODULE_QUANZI;
        $this->type = $type;
        $this->id = $id;

        return $this->buildUrl();
    }

    /**
     * 处理外部链接生成
     *
     * @param string $url 外部跳转URL
     *
     * @return string
     */
    private function handleExternal(string $url, array $extraData = []): string {
        $this->module = self::MODULE_EXTERNAL;
        $this->extraData = $extraData;
        $this->url = $url;

        return $this->buildUrl();
    }

    /**
     * 处理微信小程序链接生成
     *
     * @param string $appId 小程序原始ID, 例如gh_1234567890ab
     * @param string|null $page 小程序页面路径（不含参数）
     * @param array $extraData 额外参数，会被展开到页面路径中
     *
     * @return string
     */
    private function handleWxMiniApp(string $appId, ?string $page = '', array $extraData = []): string {
        $this->module = self::MODULE_WXMINIAPP;
        $this->miniProgramAppId = $appId;
        $this->page = $page;
        $this->extraData = $extraData;

        return $this->buildUrl();
    }

    /**
     * 处理常观小程序链接生成
     *
     * @param string $appId 小程序AppID
     * @param string|null $page 小程序页面路径（不含参数）
     * @param array $extraData 额外参数，会被展开到页面路径中
     *
     * @return string
     */
    private function handleCgMiniApp(string $appId, ?string $page = '', array $extraData = []): string {
        $this->module = self::MODULE_CGMINIAPP;
        $this->miniProgramAppId = $appId;
        $this->page = $page;
        $this->extraData = $extraData;

        return $this->buildUrl();
    }

    /**
     * 处理支付宝小程序链接生成
     *
     * @param string $appId 小程序AppID
     * @param string|null $page 小程序页面路径（不含参数）
     * @param array $extraData 额外参数，会被展开到页面路径中
     *
     * @return string
     */
    private function handleAliMiniApp(string $appId, ?string $page = '', array $extraData = []): string {
        $this->module = self::MODULE_ALIMINIAPP;
        $this->miniProgramAppId = $appId;
        $this->page = $page;
        $this->extraData = $extraData;

        return $this->buildUrl();
    }

    /**
     * 是否为合法链接, 用于判断是否为跳转链接
     *
     * @param string $url
     *
     * @return bool
     */

    private function handleIsValidRedirectTo(string $url): bool {
        return str_starts_with($url, self::SCHEME);
    }

    private function reset(): void {
        $this->module = null;
        $this->type = null;
        $this->id = null;
        $this->page = null;
        $this->extraData = [];
        $this->miniProgramId = null;
        $this->miniProgramAppId = null;
        $this->url = null;
    }

    /**
     * 构建小程序完整路径，包含页面路径和参数
     */
    private function buildMiniAppPath(): string {
        if (empty($this->page)) {
            return '';
        }

        // 如果没有额外参数，直接返回页面路径
        if (empty($this->extraData)) {
            return $this->page;
        }

        // 构建查询字符串
        $queryString = http_build_query($this->extraData);

        // 如果页面路径中已经包含参数，使用&连接，否则使用?
        $separator = str_contains($this->page, '?') ? '&' : '?';

        return $this->page . $separator . $queryString;
    }

    /**
     * 向URL添加查询参数
     *
     * @param string $url 原始URL
     * @param array $params 要添加的查询参数
     *
     * @return string 添加参数后的URL
     */
    private function addQueryParams($url, $params = []) {
        if (empty($params)) {
            return $url;
        }
        // 解析URL
        $urlParts = parse_url($url);

        // 检查是否有fragment，且fragment中包含查询参数
        $fragmentQuery = [];
        $fragmentPath = '';

        if (isset($urlParts['fragment'])) {
            $fragmentParts = explode('?', $urlParts['fragment'], 2);
            $fragmentPath = $fragmentParts[0];

            // 如果fragment中包含查询参数
            if (count($fragmentParts) > 1) {
                parse_str($fragmentParts[1], $fragmentQuery);

                // 合并fragment中的查询参数与新参数（新参数优先）
                $mergedFragmentParams = array_merge($fragmentQuery, $params);

                // 更新fragment
                $urlParts['fragment'] = $fragmentPath . '?' . http_build_query($mergedFragmentParams);

                // 已经处理完成，返回结果
                return $this->reBuildUrl($urlParts);
            }
        }

        // 如果没有在fragment中找到查询参数，则处理常规URL查询参数
        $existingParams = [];
        if (isset($urlParts['query'])) {
            parse_str($urlParts['query'], $existingParams);
        }

        // 合并参数，新参数优先
        $mergedParams = array_merge($existingParams, $params);

        // 更新查询部分
        $urlParts['query'] = http_build_query($mergedParams);

        // 重新构建URL
        return $this->reBuildUrl($urlParts);
    }

    /**
     * 从parse_url返回的部分重新构建URL
     *
     * @param array $parts parse_url函数返回的URL部分
     *
     * @return string 完整的URL
     */
    private function reBuildUrl($parts) {
        $scheme = isset($parts['scheme']) ? $parts['scheme'] . '://' : '';
        $host = $parts['host'] ?? '';
        $port = isset($parts['port']) ? ':' . $parts['port'] : '';
        $user = $parts['user'] ?? '';
        $pass = isset($parts['pass']) ? ':' . $parts['pass'] : '';
        $pass = ($user || $pass) ? "$pass@" : '';
        $path = $parts['path'] ?? '';
        $query = isset($parts['query']) && !empty($parts['query']) ? '?' . $parts['query'] : '';
        $fragment = isset($parts['fragment']) ? '#' . $parts['fragment'] : '';

        return "$scheme$user$pass$host$port$path$query$fragment";
    }

    /**
     * @return string
     */
    private function buildUrl(): string {
        $url = self::SCHEME;

        switch ($this->module) {
            case self::MODULE_EXTERNAL:
                $newUrl = $this->addQueryParams($this->url, $this->extraData);

                return $url . $this->module . '?' . http_build_query(['url' => $newUrl]);

            case self::MODULE_WXMINIAPP:
            case self::MODULE_CGMINIAPP:
            case self::MODULE_ALIMINIAPP:
                $url .= $this->module . '/' . $this->miniProgramAppId;
                $path = $this->buildMiniAppPath();
                if ($path) {
                    $url .= '?' . http_build_query(['path' => $path]);
                }

                return $url;

            default:
                $url .= $this->module . '/' . $this->type;
                if ($this->id) {
                    $url .= '?' . http_build_query(['id' => $this->id]);
                }

                return $url;
        }
    }
}
