<?php

namespace App\Models\Casts;

use App\Utils\GmSm;
use Illuminate\Contracts\Database\Eloquent\Castable;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Rtgm\sm\RtSm2;
use Rtgm\util\MyAsn1;

class Sm2EncryptedCast implements Castable
{
    const TYPE_STRING = 'string';
    const TYPE_ARRAY = 'array';
    const TYPE_OBJECT = 'object';
    const TYPE_COLLECTION = 'collection';

    public static function castUsing(array $arguments)
    {
        return new class($arguments) implements CastsAttributes
        {
            protected $type;
            protected $useCache;
            protected $publicKey;
            protected $privateKey;
            protected $cachePrefix;
            protected $sm2;


            const TYPE_STRING = 'string';
            const TYPE_ARRAY = 'array';
            const TYPE_OBJECT = 'object';
            const TYPE_COLLECTION = 'collection';
            /**
             * @param  array  $arguments
             */
            public function __construct(array $arguments)
            {
                $this->type = $arguments[0] ?? self::TYPE_STRING;
                $this->useCache = $arguments[1] ?? true;
                $this->cachePrefix = Config::get('sm2.cache.prefix', 'sm2_key_');
                //                $this->sm2 = new RtSm2();
                //                $this->loadKeys();
                GmSm::configure(publicKey: config('sm2.data_pub_key'), privateKey: config('sm2.data_priv_key'));
            }

            /**
             *
             * @param  \Illuminate\Database\Eloquent\Model  $model
             * @param  string  $key
             * @param  mixed  $value
             * @param  array  $attributes
             * @return mixed
             */
            public function get($model, string $key, $value, array $attributes)
            {
                if (!isset($attributes[$key])) {
                    return $this->getDefaultValue();
                }

                $decrypted = $this->decrypt($attributes[$key]);

                return $this->transformValue($decrypted);
            }

            /**
             *
             * @param  \Illuminate\Database\Eloquent\Model  $model
             * @param  string  $key
             * @param  mixed  $value
             * @param  array  $attributes
             * @return array
             */
            public function set($model, string $key, $value, array $attributes)
            {
                if (is_null($value)) {
                    return [$key => null];
                }

                $prepared = $this->prepareValue($value);
                $encrypted = $this->encrypt($prepared);

                return [$key => $encrypted];
            }

            /**
             * 预处理数据
             *
             * @param  mixed  $value
             * @return string
             */
            protected function prepareValue($value): string
            {
                if (is_null($value)) {
                    return '';
                }

                return match ($this->type) {
                    self::TYPE_ARRAY => 'array:' . serialize(is_array($value) ? $value : (array) $value),
                    self::TYPE_OBJECT => 'object:' . serialize(is_object($value) ? $value : (object) $value),
                    self::TYPE_COLLECTION => 'collection:' . serialize($value instanceof Collection ? $value->all() : collect($value)->all()),
                    default => (string) $value,
                };
            }

            /**
             * 转换数据
             *
             * @param  string  $value
             * @return mixed
             */
            protected function transformValue(string $value)
            {
                if (empty($value)) {
                    return $this->getDefaultValue();
                }

                $prefix = substr($value, 0, strpos($value, ':') ?: 0);

                return match ($prefix) {
                    'array' => unserialize(substr($value, 6)),
                    'object' => unserialize(substr($value, 7)),
                    'collection' => new Collection(unserialize(substr($value, 11))),
                    default => $value,
                };
            }

            /**
             * 获取默认值
             *
             * @return mixed
             */
            protected function getDefaultValue()
            {
                return match ($this->type) {
                    self::TYPE_ARRAY => [],
                    self::TYPE_OBJECT => new \stdClass,
                    self::TYPE_COLLECTION => new Collection,
                    default => '',
                };
            }

            /**
             * 加密数据
             *
             * @param  string  $value
             * @return string
             */
            protected function encrypt(string $value): string
            {
                // 检查是否已经加密
                if (str_starts_with($value, 'sm2:')) {
                    return $value;
                }

                // 检查缓存
                if ($this->useCache) {
                    $cacheKey = $this->cachePrefix . md5($value);
                    if ($cached = Cache::get($cacheKey)) {
                        return $cached;
                    }
                }

                try {
                    // SM2直接加密整个数据
                    $encrypted = GmSm::sm2Encrypt($value);
                    //                    $encrypted = $this->sm2->doEncrypt($value, $this->publicKey);
                    if (!$encrypted) {
                        throw new \RuntimeException('SM2 encryption failed');
                    }

                    $result = 'sm2:' . base64_encode($encrypted);

                    // 保存到缓存
                    if ($this->useCache) {
                        Cache::put(
                            $cacheKey,
                            $result,
                            now()->addMinutes(Config::get('sm2.cache.ttl'))
                        );
                    }

                    return $result;
                } catch (\Exception $e) {
                    throw new \RuntimeException('Encryption error: ' . $e->getMessage());
                }
            }

            /**
             * 解密数据并添加缓存支持
             * 
             * @param string $value 要解密的数据
             * @param bool $useCache 是否使用缓存（默认启用）
             * @param int $cacheTtl 缓存过期时间（秒）
             * @return mixed|null 解密后的数据
             */
            protected function decrypt($value)
            {
                // 检查参数是否为空
                if (empty($value)) {
                    return null;
                }

                // 生成缓存键（使用加密值的哈希作为键以避免键过长）
                $cacheKey = 'decrypt_' . md5($value);

                // 如果启用缓存，尝试从缓存获取
                if ($this->useCache && Cache::has($cacheKey)) {
                    return Cache::get($cacheKey);
                }

                try {
                    $encrypted = base64_decode(substr($value, 4));

                    $decrypted = GmSm::sm2Decrypt($encrypted);

                    // 将结果存入缓存
                    if ($this->useCache) {
                        Cache::put($cacheKey, $decrypted, now()->addMinutes(Config::get('sm2.cache.ttl')));
                    }

                    return $decrypted;
                } catch (\Exception $e) {
                    throw new \RuntimeException('Decryption error: ' . $e->getMessage());
                }
            }

            /**
             * 加载密钥
             *
             * @return void
             */
            protected function loadKeys(): void
            {
                $publicKeyPath = Config::get('sm2.data_pub_key');
                $privateKeyPath = Config::get('sm2.data_priv_key');

                if (empty($publicKeyPath) || empty($privateKeyPath)) {
                    throw new \RuntimeException('SM2 key paths not configured');
                }

                try {
                    $decodedPrivKey = MyAsn1::decode_file($privateKeyPath);
                    $this->privateKey = $decodedPrivKey[1];

                    $decodedPubKey = MyAsn1::decode_file($publicKeyPath);
                    $this->publicKey = $decodedPubKey[1];
                } catch (\Exception $e) {
                    throw new \RuntimeException('Failed to load SM2 keys: ' . $e->getMessage());
                }
            }
        };
    }

    /**
     * 创建一个字符串类型转换实例。
     *
     * @param  bool  $useCache
     * @return string
     */
    public static function string(bool $useCache = true): string
    {
        return static::class . ':' . self::TYPE_STRING . ',' . $useCache;
    }

    /**
     * 创建一个数组类型转换实例。
     *
     * @param  bool  $useCache
     * @return string
     */
    public static function array(bool $useCache = true): string
    {
        return static::class . ':' . self::TYPE_ARRAY . ',' . $useCache;
    }

    /**
     * 创建一个对象类型转换实例。
     *
     * @param  bool  $useCache
     * @return string
     */
    public static function object(bool $useCache = true): string
    {
        return static::class . ':' . self::TYPE_OBJECT . ',' . $useCache;
    }

    /**
     * 创建一个集合类型转换实例。
     *
     * @param  bool  $useCache
     * @return string
     */
    public static function collection(bool $useCache = true): string
    {
        return static::class . ':' . self::TYPE_COLLECTION . ',' . $useCache;
    }
}
