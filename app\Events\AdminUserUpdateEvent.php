<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\AdminUser;
class AdminUserUpdateEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $adminUser;
    public $field;
    /**
     * Create a new event instance.
     */
    public function __construct(AdminUser $adminUser, $field)
    {
        $this->adminUser = $adminUser;
        $this->field = $field;
    }

    public function getAdminUser()
    {
        return $this->adminUser;
    }

    public function getField()
    {
        return $this->field;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
