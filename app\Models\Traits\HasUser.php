<?php

namespace App\Models\Traits;

use App\Models\AdminUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HasUser
{
    const USER_TYPE_USER = 'user';
    const USER_TYPE_ADMIN = 'admin';

    public static array $userTypeMap = [
        self::USER_TYPE_USER  => '用户',
        self::USER_TYPE_ADMIN => '管理员',
    ];

    protected function userTypeText(): Attribute {
        return Attribute::make(get: fn() => self::$userTypeMap[$this->user_type] ?? '未知');
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo {
        if ($this->user_type === self::USER_TYPE_ADMIN) {
            return $this->belongsTo(AdminUser::class, 'user_uuid', 'uuid');
        } else {
            return $this->belongsTo(User::class, 'user_uuid', 'uuid');
        }
    }
}
