<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('sms_codes', function (Blueprint $table) {
            $table->id();
            $table->string('mobile');
            $table->string('code');
            $table->unsignedInteger('status');
            $table->string('ip')
                  ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('sms_codes');
    }
};
