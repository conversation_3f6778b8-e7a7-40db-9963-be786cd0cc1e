# Enterprise 企业授权系统设计文档

## 概述

Enterprise 系统是一个企业级的OAuth授权管理系统，类似于企业微信的模式，允许企业开发三方应用使用用户中心的OAuth来进行授权，获取企业的部门、企业的联系人，允许企业管理自有的三方应用。

## 系统架构

### 用户角色层级

1. **系统管理员** (`AdminUser` with `type = 'system'`)
   - 管理所有企业
   - 审核企业应用
   - 系统级权限管理

2. **企业拥有者** (`AdminUser` with `type = 'enterprise'`)
   - 使用 `enterprise` guard 进行认证
   - 管理企业基本信息
   - 管理企业管理员
   - 设置企业角色（但不能编辑权限值）

3. **企业管理员** (`EnterpriseAdmin`)
   - 使用 `enterprise_admin` guard 进行认证
   - 必须绑定前端用户 (`User`)
   - 一个用户只能绑定一个企业的一个管理员
   - 一个用户可以绑定多个企业
   - 不能既是企业拥有者又是企业管理员

## 核心模型

### 1. Enterprise (企业)
```php
- id: UUID
- name: 企业名称
- code: 企业代码
- description: 企业描述
- contact_person: 联系人
- contact_phone: 联系电话
- address: 地址
- logo: 企业logo
- status: 状态 (1启用, 0禁用)
```

### 2. EnterpriseAdmin (企业管理员)
```php
- id: UUID
- enterprise_id: 所属企业ID
- user_id: 绑定的用户ID
- status: 状态 (1启用, 0禁用)
- remark: 备注
- created_by: 创建人
- updated_by: 更新人
```

### 3. Client (OAuth客户端/企业应用)
```php
- enterprise_id: 关联企业ID
- status: 审核状态 (2待审核, 1审核通过, 0审核不通过)
- created_by: 创建人ID
- review_by: 审核人ID
- review_at: 审核时间
- review_remark: 审核备注
```

## 权限系统

### Guard 配置
- `enterprise`: 企业拥有者认证
- `enterprise_admin`: 企业管理员认证

### 角色权限
使用 `spatie/laravel-permission` 包，企业角色使用 `guard_name = 'enterprise'`

#### 默认角色
1. **企业超级管理员**: 拥有所有企业权限
2. **企业应用管理员**: 管理企业应用
3. **企业部门管理员**: 管理企业部门和联系人

#### 权限分组
- 企业管理员管理
- 企业角色管理
- 企业应用管理
- 企业部门管理
- 企业联系人管理

## 应用审核流程

### 创建流程
1. 企业创建应用时，状态自动设为 `待审核`
2. 应用必须指定一个企业管理员作为拥有者
3. 可以为应用分配多个企业管理员进行协管

### 审核流程
1. 系统管理员审核企业应用
2. 审核通过后，应用状态变为 `审核通过`
3. 审核拒绝后，应用状态变为 `审核不通过`
4. 审核通过的应用会自动加载到OAuth缓存中

## API 接口

### 认证接口
```
POST /api/enterprise/auth/enterprise/login          # 企业拥有者登录
POST /api/enterprise/auth/enterprise/refresh        # 刷新token
POST /api/enterprise/auth/enterprise-admin/login    # 企业管理员登录
POST /api/enterprise/auth/enterprise-admin/refresh  # 刷新token
```

### 企业拥有者接口 (需要 enterprise guard)
```
GET  /api/enterprise/enterprise/profile             # 获取个人信息
POST /api/enterprise/enterprise/logout              # 退出登录
PUT  /api/enterprise/enterprise/change-password     # 修改密码

# 企业管理员管理
GET  /api/enterprise/enterprise/admins              # 获取管理员列表
POST /api/enterprise/enterprise/admins              # 创建管理员
GET  /api/enterprise/enterprise/admins/{id}         # 获取管理员详情
PUT  /api/enterprise/enterprise/admins/{id}         # 更新管理员
DELETE /api/enterprise/enterprise/admins/{id}       # 删除管理员

# 企业角色管理
GET  /api/enterprise/enterprise/roles               # 获取角色列表
POST /api/enterprise/enterprise/roles               # 创建角色
GET  /api/enterprise/enterprise/roles/{id}          # 获取角色详情
PUT  /api/enterprise/enterprise/roles/{id}          # 更新角色
DELETE /api/enterprise/enterprise/roles/{id}        # 删除角色
```

### 企业管理员接口 (需要 enterprise_admin guard)
```
GET  /api/enterprise/enterprise-admin/profile       # 获取个人信息
POST /api/enterprise/enterprise-admin/logout        # 退出登录
PUT  /api/enterprise/enterprise-admin/change-password # 修改密码
```

### 企业应用管理
```
GET  /api/enterprise/enterprises/{id}/clients       # 获取应用列表
POST /api/enterprise/enterprises/{id}/clients       # 创建应用
GET  /api/enterprise/enterprises/{id}/clients/pending # 获取待审核应用
POST /api/enterprise/enterprises/{id}/clients/{id}/approve # 审核通过
POST /api/enterprise/enterprises/{id}/clients/{id}/reject  # 审核拒绝
```

## 服务类

### EnterpriseAdminService
- 企业管理员绑定/解绑用户
- 企业管理员权限管理
- 客户端分配管理

### EnterpriseClientService
- 企业应用创建
- 应用审核流程
- 应用管理员分配

## 数据库表结构

### 核心表
- `enterprises`: 企业信息
- `enterprise_admins`: 企业管理员
- `enterprise_admin_clients`: 企业管理员与客户端关联
- `admin_oauth_client`: 管理员与OAuth客户端关联（拥有者关系）

### 权限表
- `roles`: 角色表（包含企业角色）
- `permissions`: 权限表（包含企业权限）
- `model_has_roles`: 模型角色关联
- `role_has_permissions`: 角色权限关联

## 使用示例

### 1. 企业拥有者登录
```php
POST /api/enterprise/auth/enterprise/login
{
    "username": "enterprise_owner",
    "password": "password"
}
```

### 2. 创建企业管理员
```php
POST /api/enterprise/enterprise/admins
{
    "enterprise_id": "uuid",
    "user_id": "user_uuid",
    "status": 1,
    "remark": "备注"
}
```

### 3. 创建企业应用
```php
POST /api/enterprise/enterprises/{enterpriseId}/clients
{
    "name": "应用名称",
    "client_type": "miniapp",
    "description": "应用描述"
}
```

## 注意事项

1. **用户绑定限制**: 一个用户只能绑定一个企业的一个管理员账号
2. **角色权限**: 企业拥有者可以设置角色但不能编辑权限值
3. **应用审核**: 企业创建的应用必须经过系统管理员审核
4. **缓存管理**: 审核通过的应用会自动加载到OAuth缓存中
5. **安全性**: 所有企业相关操作都需要相应的权限验证

## 错误码说明

Enterprise系统使用了专门的错误码范围：

### 企业相关错误码 (172000-172999)
- `172001`: 企业不存在
- `172002`: 企业状态异常
- `172003`: 企业管理员不存在
- `172004`: 企业管理员已存在
- `172005`: 该用户已绑定企业管理员
- `172006`: 该用户已是企业拥有者
- `172007`: 该管理员还有关联的客户端
- `172008`: 企业应用不存在
- `172009`: 应用不是待审核状态
- `172010`: 应用已被审核
- `172011`: 应用还有活跃的授权
- `172012`: 企业角色不存在
- `172013`: 该角色还有用户在使用
- `172014`: 企业权限不足
- `172015`: 无效的审核状态
- `172016`: 该应用已有拥有者

### 管理员验证错误码 (173000-173999)
- `173001`: 数据验证失败

## 部署说明

1. 运行数据库迁移：
```bash
php artisan migrate
```

2. 初始化企业权限和角色：
```bash
# 方式一：使用专门的命令
php artisan enterprise:init-permissions

# 方式二：使用seeder
php artisan db:seed --class=EnterprisePermissionSeeder
```

3. 配置JWT密钥（如果使用SM2加密）
4. 配置相关环境变量

## 权限初始化

系统提供了 `EnterprisePermissionSeeder` 来初始化企业相关的权限和角色：

### 默认角色
1. **企业超级管理员**: 拥有所有企业权限
2. **企业应用管理员**: 管理企业应用
3. **企业部门管理员**: 管理企业部门和联系人
4. **企业人事管理员**: 管理企业管理员和角色

### 权限分组
- 企业管理员管理 (7个权限)
- 企业角色管理 (8个权限)
- 企业应用管理 (8个权限)
- 企业部门管理 (5个权限)
- 企业联系人管理 (6个权限)
