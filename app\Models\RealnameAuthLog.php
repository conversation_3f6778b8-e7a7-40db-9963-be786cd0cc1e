<?php

namespace App\Models;

use App\Models\Casts\Sm2EncryptedCast;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $outer_order_no
 * @property int $type
 * @property int $user_id
 * @property mixed $cert_name
 * @property mixed $cert_no
 * @property string $certify_id
 * @property string $request_id
 * @property string|null $passed
 * @property string|null $identity_info
 * @property string|null $device_token
 * @property mixed|null $material_info
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereCertName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereCertNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereCertifyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereDeviceToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereIdentityInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereMaterialInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereOuterOrderNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog wherePassed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RealnameAuthLog whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperRealnameAuthLog
 */
class RealnameAuthLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'outer_order_no',
        'type',
        'user_id',
        'cert_name',
        'cert_no',
        'certify_id',
        'request_id',
        'passed',
        'identity_info',
        'device_token',
        'material_info',
    ];

    const TYPE_FACE = 1;
    const TYPE_IDCARD = 2;

    const PASSED_YES = 'YES';
    const PASSED_NO = 'NO';

    const TYPE_BIND = 1;
    const TYPE_UNBIND = 2;

    protected function casts() {
        return [
            'cert_name' => Sm2EncryptedCast::string(),
            'cert_no' => Sm2EncryptedCast::string(),
            'material_info' => Sm2EncryptedCast::array(),
        ];
    }
}
