<?php

namespace App\Jobs;

use App\Models\PushMessage;
use App\Models\UserMessage;
use App\Models\UserSubscription;
use App\Models\UserDevice;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessSubscriptionPushJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const BATCH_SIZE = 500;

    protected PushMessage $pushMessage;

    public function __construct(PushMessage $pushMessage) {
        $this->pushMessage = $pushMessage;
        $this->queue = 'push';
    }

    public function handle() {
        try {
            if (!$this->pushMessage->isPending()) {
                return;
            }

            $this->pushMessage->update(['status' => PushMessage::STATUS_PROCESSING]);

            // 获取订阅用户列表
            $batchNo = 0;
            UserSubscription::where('oauth_client_subscription_id', $this->pushMessage->oauth_client_subscription_id)
                            ->where('status', UserSubscription::STATUS_ENABLE)
                            ->chunkById(self::BATCH_SIZE, function ($subscribers) use (&$batchNo) {
                                $batchNo++;
                                $targets = $subscribers->pluck('user_uuid')
                                                       ->toArray();
                                if (!empty($targets)) {
                                    // 创建用户消息
                                    $records = $subscribers->map(function ($subscriber) {
                                        return [
                                            'user_uuid'       => $subscriber->user_uuid,
                                            'push_message_id' => $this->pushMessage->id,
                                            'is_read'         => UserMessage::IS_READ_NO,
                                            'created_at'      => Carbon::now(),
                                            'updated_at'      => Carbon::now(),
                                        ];
                                    })
                                                           ->toArray();
                                    UserMessage::insert($records);

                                    ProcessPushMessageJob::dispatch($this->pushMessage, $targets, $batchNo);
                                }
                            });

            $this->pushMessage->update(['status' => PushMessage::STATUS_EXECUTED]);

        } catch (\Exception $e) {
            //            Log::error('Subscription push processing failed', [
            //                'message_id' => $this->pushMessage->id,
            //                'error'      => $e->getMessage(),
            //            ]);

            $this->pushMessage->update([
                'status'    => PushMessage::STATUS_FAILED,
                'error_msg' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
