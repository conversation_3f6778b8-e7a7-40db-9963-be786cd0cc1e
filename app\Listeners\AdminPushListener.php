<?php

namespace App\Listeners;

use App\Enums\OAuthScopeEnum;
use App\Events\AdminPushEvent;
use App\Models\Client;
use App\Utils\OAuthCacher;
use App\Utils\SignatureValidator;
use App\Utils\Tools;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class AdminPushListener implements ShouldQueue
{
    use InteractsWithQueue;

    public $queue = 'account';

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(AdminPushEvent $event): void
    {
        $adminUser = $event->getAdminUser();
        
        $postData = [
            'event'   => 'adminuser_push',
            'action'  => $event->getAction(),
            // 'to_user' => $adminUser->uuid,
            'data'    => [
                'union_id'  => $adminUser->uuid,
                'nickname'  => $adminUser->nickname,
                'true_name' => $adminUser->true_name,
                'mobile'    => $adminUser->mobile,
                'avatar'    => $adminUser->display_avatar,
                'departments' => $adminUser->departments ? $adminUser->departments->map(function ($department) {
                    return [
                        'id' => $department->id,
                        'name' => $department->name,
                        'code' => $department->code,
                        'parent' => $department->parent_id,
                    ];
                })->toArray(): [],
                'shot_mobile' => $adminUser->shot_mobile,
            ],
        ];

        $client = OAuthCacher::getClient($event->getClientId());

        if ($client->is_revoked == Client::IS_REVOKED_YES || $client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES || !$client->callback_url || !filter_var($client->callback_url, FILTER_VALIDATE_URL)) {
           return;
        }

        if (!$client->hasAdminScope(OAuthScopeEnum::ADMINPUSH->value)) {
            return;
        }

        if (!$adminUser->canAccessOauthClient($client->id)) {
            $postData['action'] = 'unaccess';
        }

        // if (!OAuthCacher::isUserAuthorized($postData['to_user'], $client->id)) {
        //     return;
        // }

        $openId = OAuthCacher::getOpenIdByUserUuid($adminUser->uuid, $client->id);
        
        if (!$openId) {
            return;
        }

        $postData['data']['open_id'] = $openId;

        $signatureData = [
            'client_id' => $client->client_key,
            'timestamp' => time(),
            'nonce'     => Str::random(),
        ] + $postData;

        $signatureValidator = new SignatureValidator($client->client_key, $client->client_access_secret);

        $signature = $signatureValidator->generateSignature($signatureData);

        try {
            $res = Http::withHeaders([
                Tools::HEADER_SIGN      => $signature,
                Tools::HEADER_TIMESTAMP => $signatureData['timestamp'],
                Tools::HEADER_NONCE     => $signatureData['nonce'],
                Tools::HEADER_CLIENT_ID => $client->client_key,
            ])
                ->withOptions([
                    'verify' => false,
                    'curl'   => [
                        CURLOPT_RESOLVE => [
                            'uc.example.com:80:127.0.0.1',
                            'kanchangzhou.example.com:80:127.0.0.1',
                        ],
                    ],
                ])
                ->post($client->callback_url, $postData);
        } catch (ConnectionException $e) {
        }
    }
}
