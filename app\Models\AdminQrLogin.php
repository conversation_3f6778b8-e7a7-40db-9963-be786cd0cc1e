<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $id
 * @property string $code 二维码唯一标识
 * @property int $status 状态：0-未扫码，1-已扫码待确认，2-已确认登录，3-已取消，4-已过期
 * @property int|null $admin_id 确认登录的管理员ID
 * @property string|null $user_uuid 扫码的前端用户UUID
 * @property string|null $client_ip 客户端IP
 * @property string|null $user_agent 客户端UA
 * @property string|null $scanned_at 扫码时间
 * @property string|null $confirmed_at 确认时间
 * @property string $expired_at 过期时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\AdminUser|null $admin
 * @property-read string $status_text
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereAdminId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereClientIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereConfirmedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereScannedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereUserAgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AdminQrLogin whereUserUuid($value)
 * @mixin \Eloquent
 * @mixin IdeHelperAdminQrLogin
 */
class AdminQrLogin extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'admin_qr_login';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'status',
        'admin_id',
        'user_uuid',
        'client_ip',
        'user_agent',
        'device_id',
        'scanned_at',
        'confirmed_at',
        'expired_at',
    ];

    /**
     * 应该被转换为日期的属性
     *
     * @var array<int, string>
     */
    protected $dates = [
        'scanned_at',
        'confirmed_at',
        'expired_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 状态常量
     */
    const STATUS_PENDING = 0;    // 未扫码
    const STATUS_SCANNED = 1;    // 已扫码待确认
    const STATUS_CONFIRMED = 2;  // 已确认登录
    const STATUS_CANCELED = 3;   // 已取消
    const STATUS_EXPIRED = 4;    // 已过期
    const STATUS_USED = 5;

    public static $statusMap = [
        self::STATUS_PENDING => '未扫码',
        self::STATUS_SCANNED => '已扫码待确认',
        self::STATUS_CONFIRMED => '已确认登录',
        self::STATUS_CANCELED => '已取消',
        self::STATUS_EXPIRED => '已过期',
        self::STATUS_USED => '已使用',
    ];

    protected $appends = ['status_text'];

    public function getStatusTextAttribute(): string
    {
        return self::$statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 关联管理员
     */
    public function admin()
    {
        return $this->belongsTo(AdminUser::class, 'admin_id');
    }

    /**
     * 关联前端用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_uuid', 'uuid');
    }

    /**
     * 检查是否已过期
     */
    public function isExpired(): bool
    {
        return $this->status === self::STATUS_EXPIRED || now()->gt($this->expired_at);
    }

    /**
     * 检查是否可以确认登录
     */
    public function canConfirm(): bool
    {
        return $this->status === self::STATUS_SCANNED && !$this->isExpired();
    }
}
