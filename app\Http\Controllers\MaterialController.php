<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Models\Material;
use App\Services\CloudFiles\Facades\CloudFiles;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class MaterialController extends Controller
{
    protected $validationMessages = [
        'driver.required'    => '存储驱动不能为空',
        'filename.required'  => '文件名不能为空',
        'format.required'    => '文件格式不能为空',
        'size.required'      => '文件大小不能为空',
        'mime_type.required' => '文件类型不能为空',
    ];

    /**
     * 获取文件上传策略和配置
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function policy(Request $request) {
        // 获取当前使用的云存储驱动
        //        $driver = $request->input('driver', config('cloudfiles.default', 'oss'));

        $driver = config('cloudfiles.default', 'oss');
        // 生成文件目录
        $dir = 'cguc/' . date('Ymd') . '/';
        // 生成回调地址
        $callbackUrl = route('api.materials.upload.callback', ['driver' => $driver]);

        try {
            // 获取上传策略
            $result = CloudFiles::driver($driver)
                                ->policy(callbackRoute: $callbackUrl, dir: $dir, contentLengthRange: 10 * 1024 * 1024);

            // 返回完整的上传配置
            return Respond::success([
                'filename'   => Str::uuid()
                                   ->toString(),
                'provider'   => $driver,  // 存储提供商
                'upload_url' => $result['host'], // 上传域名
                'dir'        => $result['dir'],  // 文件目录
                'policy'     => $result['policy'],  // 上传策略
                'signature'  => $result['signature'],  // 签名
                'callback'   => $result['callback'] ?? '',  // 回调配置
                'access_key' => $result['AccessKeyId'] ?? $result['OSSAccessKeyId'] ?? '',  // 访问密钥ID
                'expire'     => $result['expire'],  // 过期时间
                // 针对不同云存储的特定参数
                //                'provider_config' => $this->getProviderConfig($driver),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code'    => 500,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 处理云存储回调
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function callback(Request $request, $driver) {

        try {
            // 获取当前使用的云存储驱动
            //        $driver = $request->input('provider', config('cloudfiles.default', 'oss'));

            // 验证回调请求
            if (CloudFiles::driver($driver)
                          ->verify()) {
                // 获取回调信息
                $filename = $request->input('filename');
                $format = $request->input('format');
                $size = $request->input('size');
                $mimeType = $request->input('mimeType');
                $width = $request->input('width');
                $height = $request->input('height');

                // TODO: 这里可以将文件信息保存到数据库

                $material = Material::create([
                    'uuid'      => Str::uuid(),
                    'path'      => $filename,
                    'provider'  => $driver,
                    'format'    => $format,
                    'size'      => $size,
                    'mime_type' => $mimeType,
                    'width'     => $width,
                    'height'    => $height,
                ]);

                return Respond::success([
                    'filename'  => $material->uuid,
                    'format'    => $format,
                    'size'      => $size,
                    'mime_type' => $mimeType,
                    'width'     => $width,
                    'height'    => $height,
                ]);
            }

            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '回调验证失败', [], 403);
        } catch (\Exception $e) {
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, $e->getMessage(), [], 500);
        }
    }

    /**
     * 获取不同云存储的特定配置
     *
     * @param string $driver
     *
     * @return array
     */
    protected function getProviderConfig(string $driver): array {
        return match ($driver) {
            'oss' => [
                'success_action_status' => '200',
                'upload_field'          => 'file',
                'response_type'         => 'json',
            ],
            'cos' => [
                'success_action_status' => '200',
                'upload_field'          => 'file',
                'response_type'         => 'json',
            ],
            'obs' => [
                'success_action_status' => '200',
                'upload_field'          => 'file',
                'response_type'         => 'json',
            ],
            'qiniu' => [
                'upload_field'  => 'file',
                'response_type' => 'json',
                'token_field'   => 'token',
            ],
            default => []
        };
    }
}
