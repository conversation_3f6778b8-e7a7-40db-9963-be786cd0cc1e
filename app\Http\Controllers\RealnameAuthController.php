<?php

namespace App\Http\Controllers;


use AlibabaCloud\SDK\Cloudauth\V20190307\Cloudauth;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\DescribeFaceVerifyRequest;
use AlibabaCloud\SDK\Cloudauth\V20190307\Models\InitFaceVerifyRequest;
use App\Enums\ErrorCodeEnum;
use App\Exceptions\UserException;
use App\Models\RealnameAuthLog;
use App\Models\User;
use App\Utils\Respond;
use Carbon\Carbon;
use Darabonba\OpenApi\Models\Config;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Medz\IdentityCard\China\Identity;

class RealnameAuthController extends Controller
{
    protected $validationMessages = [
        'metaInfos.required'       => '设备信息不能为空',
        'true_name.required'       => '真实姓名不能为空',
        'true_name.max'            => '真实姓名不能超过10个字符',
        'identity_number.required' => '身份证号不能为空',
        'identity_number.max'      => '身份证号不能超过18个字符',
        'identity_number.min'      => '身份证号不能少于12个字符',
        'certifyId.required'       => '认证ID不能为空',
    ];

    //    public function __construct() {
    //        $this->closeFaceverify();
    //        throw new FaceVerifyException(FaceVerifyException::FACEVERIFY_CLOSE);
    //    }
    //
    //    /**
    //     * @throws FaceVerifyException
    //     */
    //    protected function closeFaceverify() {
    //        if (!config('kuser.face_verify.enable')) {
    //            throw new FaceVerifyException(FaceVerifyException::FACEVERIFY_CLOSE);
    //        }
    //    }

    /**
     * @throws UserException
     * @throws ValidationException
     */
    public function bindInit(Request $request) {
        if (Auth::guard('api')
                ->user()
                ->isRealnameAuthConfirmed()) {
            throw new UserException(ErrorCodeEnum::REALNAME_BINDED);
        }

        $this->validate($request, [
            'metaInfos'       => 'required',
            'true_name'       => 'required|max:10',
            'identity_number' => [
                'required',
                'max:18',
                'min:12',
                function ($attr, $value, $fail) {
                    $identity = new Identity($value);
                    if (!$identity->legal()) {
                        return $fail('身份证格式不正确');
                    }
                },
            ],
        ], $this->validationMessages);

        if (User::where('identity_hash', Str::of($request->input('identity_number'))
                                            ->pipe('md5'))
                ->exists()) {
            throw new UserException(ErrorCodeEnum::IDENTITY_BINDED);
        }


        return $this->aliFaceVerifyInit($request->input('metaInfos'), $request->input('true_name'), $request->input('identity_number'));
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     * @throws UserException
     * @throws ValidationException
     */
    public function unbindInit(Request $request) {
        $user = Auth::guard('api')
                    ->user();
        if (!$user->isRealnameAuthConfirmed()) {
            throw new UserException(ErrorCodeEnum::REALNAME_UNBINDED);
        }

        $this->validate($request, [
            'metaInfos' => 'required|string',
        ], $this->validationMessages);

        return $this->aliFaceVerifyInit($request->input('metaInfos'), $user->realname_auth_data[User::REALNAME_AUTH_NAME], $user->realname_auth_data[User::REALNAME_AUTH_IDENTITY], RealnameAuthLog::TYPE_UNBIND);
    }

    /**
     * @throws UserException
     */
    public function faceVerifyCallback(Request $request) {
        if (!Cache::get($request->input('callbackToken'))) {
            return Respond::error(ErrorCodeEnum::REALNAME_FAILED);
        }

        Cache::forget($request->input('callbackToken'));

        $faceLog = RealnameAuthLog::where('certify_id', $request->input('certifyId'))
                                  ->orderByDesc('id')
                                  ->first();

        if (!$faceLog) {
            return Respond::error(ErrorCodeEnum::REALNAME_FAILED);
        }

        $this->aliFaceDescribe($request->input('certifyId'), $faceLog);

    }

    /**
     * @throws ValidationException
     * @throws UserException
     */
    public function checkResult(Request $request) {
        $this->validate($request, [
            'certifyId' => 'required',
        ], $this->validationMessages);

        $faceLog = RealnameAuthLog::where('certify_id', $request->input('certifyId'))
                                  ->where('user_id', Auth::guard('api')
                                                         ->user()->id)
                                  ->orderByDesc('id')
                                  ->first();

        if (!$faceLog) {
            return Respond::error(ErrorCodeEnum::REALNAME_FAILED);
        }

        return $this->aliFaceDescribe($request->input('certifyId'), $faceLog);
    }

    /**
     * @param $certifyId
     * @param $faceLog
     *
     * @return JsonResponse
     * @throws UserException
     */
    protected function aliFaceDescribe($certifyId, $faceLog) {
        if ($faceLog->passed) {
            return Respond::success(['passed' => (int)($faceLog->passed == 'T')]);
        }

        try {
            $config = new Config([
                'accessKeyId'     => config('services.realname_auth.aliyun.access_key_id'),
                'accessKeySecret' => config('services.realname_auth.aliyun.access_secret'),
            ]);
            $config->endpoint = config('services.realname_auth.aliyun.endpoint');

            $client = new Cloudauth($config);

            $describeFaceVerify = new DescribeFaceVerifyRequest([
                "certifyId" => $certifyId,
                "sceneId"   => config('services.realname_auth.aliyun.scene_id'),
            ]);

            $res = $client->describeFaceVerify($describeFaceVerify);

            if ($res->statusCode == 200 && $res->body->code == 200) {
                if ($faceLog->type == RealnameAuthLog::TYPE_BIND) {
                    if ($res->body->resultObject->passed == 'T') {
                        $user = User::where('id', $faceLog->user_id)
                                    ->first();
                        $realnameAuthData = $user->realname_auth_data;
                        $realnameAuthData['name'] = $faceLog->cert_name;
                        $realnameAuthData['identity'] = $faceLog->cert_no;
                        $realnameAuthData['date'] = Carbon::now()
                                                          ->toDateTimeString();

                        $user->identity_hash = Str::of($faceLog->cert_no)
                                                  ->pipe('md5');
                        $user->realname_auth_data = $realnameAuthData;
                        $user->realname_auth_confirmed = User::CONFIRMED_YES;
                        $user->save();

                    }
                } else {
                    if ($res->body->resultObject->passed == 'T') {
                        $user = User::where('id', $faceLog->user_id)
                                    ->first();

                        $user->identity_hash = null;
                        $user->realname_auth_data = null;
                        $user->realname_auth_confirmed = User::CONFIRMED_NO;
                        $user->save();
                    }
                }

                $faceLog->passed = $res->body->resultObject->passed;
                $faceLog->identity_info = '';
                $faceLog->device_token = $res->body->resultObject->deviceToken;
                $faceLog->material_info = $res->body->resultObject->materialInfo;
                $faceLog->save();

                return Respond::success(['passed' => (int)($res->body->resultObject->passed == 'T')]);
            } else {
                return Respond::success(['passed' => 0]);
            }
        } catch (ClientException|ServerException $exception) {
            // 延时队列重试一次
            throw new UserException(ErrorCodeEnum::REALNAME_FAILED, [
                'message' => $exception->getMessage(),
                'code'    => $exception->getCode(),
            ]);
        }
    }

    /**
     * @throws UserException
     */
    private function aliFaceVerifyInit($metaInfos, $trueName, $identityNumber, $type = RealnameAuthLog::TYPE_BIND) {
        try {
            $outerOrderNo = Str::random(32);
            $callbackToken = Str::random(32);
            $callbackUrl = secure_url(route('face-verify.callback', '', false));

            Cache::put($callbackToken, true, Carbon::now()
                                                   ->addHours(2));

            $config = new Config([
                'accessKeyId'     => config('services.realname_auth.aliyun.access_key_id'),
                'accessKeySecret' => config('services.realname_auth.aliyun.access_secret'),
            ]);

            $config->endpoint = 'cloudauth.aliyuncs.com';

            $client = new Cloudauth($config);

            $initFaceVerify = new InitFaceVerifyRequest([
                "sceneId"       => config('services.realname_auth.aliyun.scene_id'),
                "outerOrderNo"  => $outerOrderNo,
                "productCode"   => "ID_PRO",
                "certType"      => "IDENTITY_CARD",
                "certName"      => $trueName,
                "certNo"        => $identityNumber,
                "metaInfo"      => $metaInfos,
                "userId"        => Auth::guard('api')
                                       ->user()->uuid,
                "callbackToken" => $callbackToken,
                "callbackUrl"   => $callbackUrl,
            ]);

            $res = $client->initFaceVerify($initFaceVerify);

            if ($res->statusCode == 200 && $res->body->code == 200) {
                RealnameAuthLog::create([
                    'user_id'        => Auth::guard('api')
                                            ->user()->id,
                    'type'           => $type,
                    'outer_order_no' => $outerOrderNo,
                    'cert_name'      => $trueName,
                    'cert_no'        => $identityNumber,
                    'request_id'     => $res->body->requestId,
                    'certify_id'     => $res->body->resultObject->certifyId,
                ]);

                return Respond::success(['certify_id' => $res->body->resultObject->certifyId]);
            } else {
                throw new UserException('实人认证初始化失败');
            }

        } catch (ClientException|ServerException $exception) {
            throw new UserException('实人认证初始化失败');
        }
    }

}
