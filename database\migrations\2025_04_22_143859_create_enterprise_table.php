<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enterprises', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->comment('企业名称');
            $table->string('code')->nullable()->comment('企业代码');
            $table->text('description')->nullable()->comment('企业描述');
            $table->string('contact_person')->nullable()->comment('联系人');
            $table->string('contact_phone')->nullable()->comment('联系电话');
            $table->string('address')->nullable()->comment('地址');
            $table->json('logo')->nullable()->comment('企业logo');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->timestamps();
            $table->softDeletes();

            $table->index('code');
        }); 

        // 创建企业部门表
        Schema::create('enterprise_departments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('enterprise_id')->comment('所属企业ID');
            $table->string('name')->comment('部门名称');
            $table->string('code')->comment('部门编码');
            $table->uuid('parent_id')->nullable()->comment('父部门ID');
            $table->integer('_lft')->default(0);
            $table->integer('_rgt')->default(0);
            $table->integer('sort')->nullable()->default(0);
            $table->string('remark')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index('parent_id');
            $table->index('enterprise_id');
            $table->index(['_lft', '_rgt','parent_id']);
        });

        Schema::create('enterprise_contacts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('enterprise_id')->comment('所属企业ID');
            $table->uuid('department_id')->nullable()->comment('所属部门ID');
            $table->tinyInteger('is_leader')->nullable()->default(0)->comment('是否为部门负责人');
            $table->string('name')->comment('联系人姓名');
            $table->string('display_name')->nullable()->comment('显示姓名');
            $table->string('mobile')->nullable()->comment('手机号');
            $table->string('short_mobile')->nullable()->comment('短号');
            $table->string('office_phone')->nullable()->comment('办公电话');
            $table->string('title')->nullable()->comment('职位');
            $table->string('email')->nullable()->comment('邮箱');
            $table->string('remark')->nullable()->comment('备注');
            $table->tinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->timestamps();
            $table->softDeletes();

            $table->index('enterprise_id');
            $table->index('department_id');
        });

        // 修改admin_users表，添加enterprise_id字段
        Schema::table('admin_users', function (Blueprint $table) {
            $table->uuid('enterprise_id')->nullable()->after('uuid')->comment('所属企业ID');
            $table->string('type')->nullable()->after('enterprise_id')->comment('用户类型:system,enterprise');

            $table->index('enterprise_id');
        });

        // 修改oauth_clients表，添加enterprise_id字段
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->uuid('enterprise_id')->nullable()->after('id')->comment('关联企业ID');

            $table->index('enterprise_id');
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enterprises');
        Schema::dropIfExists('enterprise_departments');
        Schema::dropIfExists('enterprise_contacts');
        
        Schema::table('admin_users', function (Blueprint $table) {
            $table->dropColumn('enterprise_id');
            $table->dropColumn('type');
        });
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->dropColumn('enterprise_id');
        });
    }
};
