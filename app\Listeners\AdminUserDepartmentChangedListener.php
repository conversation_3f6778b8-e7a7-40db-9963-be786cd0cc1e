<?php

namespace App\Listeners;

use App\Events\AdminUserDepartmentChangedEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class AdminUserDepartmentChangedListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(AdminUserDepartmentChangedEvent $event): void
    {
        //
    }
}
