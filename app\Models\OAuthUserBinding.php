<?php

namespace App\Models;

use App\Models\Traits\HasUser;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $open_id
 * @property string $user_uuid
 * @property string|null $user_type 用户类型
 * @property int $client_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Client|null $client
 * @property-read \App\Models\User|null $user
 * @property-read mixed $user_type_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding whereClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding whereOpenId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding whereUserType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OAuthUserBinding whereUserUuid($value)
 * @mixin \Eloquent
 * @mixin IdeHelperOAuthUserBinding
 */
class OAuthUserBinding extends Model
{
    use HasFactory;
    use HasUser;

    protected $table = 'oauth_user_bindings';

    protected $fillable = [
        'user_uuid',
        'user_type',
        'client_id',
        'open_id',
    ];

//    public function user() {
//        return $this->belongsTo(User::class, 'user_uuid', 'uuid');
//    }

    public function client() {
        return $this->belongsTo(Client::class);
    }
}
