<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Enums\PushMessageDeliveryEnum;
use App\Exceptions\AuthException;
use App\Exceptions\UserException;
use App\Http\Resources\UserResource;
use App\Http\Resources\WorkspaceClientResource;
use App\Models\AdminUser;
use App\Models\Client;
use App\Models\Material;
use App\Models\User;
use App\Models\UserChangeLog;
use App\Models\UserDevice;
use App\Utils\GmSm;
use App\Utils\Respond;
use App\Utils\Tools;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use Overtrue\LaravelSocialite\Socialite;

class UserController extends Controller
{

    protected $validationMessages = [
        'password.required_without'    => '未使用验证码时密码不能为空',
        'code.required_without'        => '未使用密码时验证码不能为空',
        'old_password.required_unless' => '未使用手机验证码时原密码不能为空',
        'find_token.required_with'     => '重置密码时令牌不能为空',
        'field.required'               => '修改字段不能为空',
        'field.max'                    => '修改字段不能超过10个字符',
        'field.in'                     => '不支持修改该字段',
        'value.required'               => '修改值不能为空',
        'old_code.required'            => '原手机验证码不能为空',
        'old_code.check_code'          => '原手机验证码不正确',
        'mobile.required'              => '手机号不能为空',
        'mobile.is_mobile'             => '请输入正确的手机号格式',
        'code.required'                => '验证码不能为空',
        'code.check_code'              => '验证码不正确',
        'new_password.required'        => '新密码不能为空',
        'new_password.confirmed'       => '两次输入的密码不一致',
        'new_password.min'             => '密码不能少于8个字符',
        'new_password.regex'           => '密码必顫包含大小写字母、数字、特殊字符(!@#$%^&*?-_)',
        'device_token.required'        => '设备token不能为空',
        'platform_type.required'       => '平台类型不能为空',
        'platform_type.in'             => '不支持的平台类型',
    ];

    /**
     * 我的
     * @return void
     */
    public function my()
    {
        /** @var \App\Models\User $user */
        $user = Auth::guard('api')
            ->user();

        $user->load('bindAdminUser');

        return Respond::success(UserResource::make($user));
    }

    /**
     * 更新
     * @return void
     * @throws ValidationException
     */
    public function updateAccount(Request $request)
    {
        /** @var \App\Models\User $user */
        $user = Auth::guard('api')
            ->user();

        // 审核字段列表
        $auditFields = ['nickname', 'avatar', 'comment', 'username'];

        // 所有字段的验证规则
        $this->validate($request, [
            'field' => 'required|max:20|in:gender,avatar,nickname,comment,birthday,username', // username max length
            'value' => [
                'required',
                function ($attribute, $value, $fail) use ($request, $user) {
                    $field = $request->input('field');
                    switch ($field) {
                        case 'gender':
                            if (!in_array($value, [
                                User::GENDER_UNKNOWN,
                                User::GENDER_MALE,
                                User::GENDER_FEMALE,
                            ])) {
                                $fail('请选择正确性别字段');
                            }
                            break;
                        case 'birthday':
                            if (!preg_match('/^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\d|3[01])$/', $value)) {
                                $fail('生日必须是 YYYY-MM-DD 格式');
                                break;
                            }
                            // 验证是否是有效日期
                            try {
                                $date = Carbon::createFromFormat('Y-m-d', $value);
                                if (!$date || $date->format('Y-m-d') !== $value || $date->isFuture()) {
                                    $fail('请输入有效的出生日期');
                                }
                            } catch (\Exception $e) {
                                $fail('请输入有效的出生日期');
                            }
                            break;
                        case 'nickname':
                            if (mb_strlen($value) > 16) {
                                $fail('昵称不能超过16个字符');
                            }
                            break;
                        case 'comment':
                            if (mb_strlen($value) > 100) {
                                $fail('个性签名不能超过100个字符');
                            }
                            break;
                        case 'avatar':
                            $material = Material::where('uuid', $value)->first();
                            if (!$material) {
                                $fail('无效的头像资源');
                            }
                            break;
                        case 'username':
                            if (mb_strlen($value) < 4 || mb_strlen($value) > 20) { // 根据 admin store 调整
                                $fail('用户名长度必须在4到20个字符之间');
                            }
                            if (User::where('username', $value)
                                ->where('uuid', '!=', $user->uuid) // 使用 uuid
                                ->exists()
                            ) {
                                $fail('用户名已被占用');
                            }
                            // 检查是否有待审核或已批准的用户名修改记录 (防止重复修改)
                            if (UserChangeLog::where('user_id', $user->id)
                                ->where('change_key', 'username')
                                ->whereIn('status', [UserChangeLog::STATUS_PENDING, UserChangeLog::STATUS_APPROVED])
                                ->exists()
                            ) {
                                $fail('您已提交过用户名修改申请，请等待审核或联系管理员。');
                            }
                            break;
                    }
                },
            ],
        ], $this->validationMessages);

        $field = $request->input('field');
        $value = $request->input('value');

        // 如果新旧值相同，直接返回
        $currentValue = $user->$field;
        // 特殊处理 avatar，比较 uuid
        if ($field === 'avatar') {
            $currentValue = $user->avatar['uuid'] ?? null;
        }

        if ($currentValue == $value) {
            return Respond::success(UserResource::make($user));
        }

        $oldValue = $user->getOriginal($field);

        // 如果字段需要审核
        if (in_array($field, $auditFields)) {

            // 特殊处理 avatar 的旧值存储
            if ($field === 'avatar') {
                $oldValue = json_encode($oldValue ?: ['path' => config('uc.default_avatar'), 'provider' => 'url', 'uuid' => '']); // 存JSON
            }

            // 查找是否已有待审核记录
            $pendingLog = UserChangeLog::where('user_id', $user->id)
                ->where('change_key', $field)
                ->where('status', UserChangeLog::STATUS_PENDING)
                ->first();

            if ($pendingLog) {
                // 如果新提交的值和旧值一样，相当于撤销修改申请 (或者也可以不允许撤销，直接报错)
                $originalOldValue = ($field === 'avatar') ? json_decode($pendingLog->old_value, true) : $pendingLog->old_value;
                $originalOldValueForCompare = ($field === 'avatar') ? ($originalOldValue['uuid'] ?? null) : $originalOldValue;

                if ($value == $originalOldValueForCompare) {
                    $pendingLog->delete(); // 删除待审核记录
                    // 恢复 confirmed 状态 (如果需要)
                    $confirmedField = $field . '_confirmed';
                    if (property_exists($user, $confirmedField)) {
                        $user->$confirmedField = User::CONFIRMED_YES;
                        $user->save();
                    }
                    return Respond::success(UserResource::make($user->refresh()));
                } else {

                    if ($field === 'avatar') {
                        $material = Material::where('uuid', $value)->first();
                    }
                    // 更新待审核记录的新值
                    $pendingLog->update([
                        'new_value' => $field === 'avatar' ? json_encode(['path' => $material->path, 'provider' => $material->provider, 'uuid' => $material->uuid]) : $value, // Avatar 存 uuid
                        'ip'        => Tools::getClientIp(),
                    ]);
                }
            } else {
                if ($field === 'avatar') {
                    $material = Material::where('uuid', $value)->first();
                }
                // 创建新的待审核记录
                UserChangeLog::create([
                    'user_id'    => $user->id,
                    'change_key' => $field,
                    'old_value'  => $oldValue ?? '',
                    'new_value'  => $field === 'avatar' ? json_encode(['path' => $material->path, 'provider' => $material->provider, 'uuid' => $material->uuid]) : $value, // Avatar 存 uuid
                    'ip'         => Tools::getClientIp(),
                    'status'     => UserChangeLog::STATUS_PENDING,
                ]);
            }

            // 更新 User 表的确认状态为"未确认"
            $confirmedField = $field . '_confirmed';
            if ($user->hasAttribute($confirmedField)) {
                $user->$confirmedField = User::CONFIRMED_NO;
                $user->save();
            } else if ($field === 'username') {
                // 用户名没有 confirmed 字段，无需操作
            }
        } else {
            // 不需要审核的字段，直接更新 User 表
            $user->$field = $value;
            $user->save();

            // (可选) 如果希望记录非审核字段的变更历史，也可以在这里创建一条已批准的 ChangeLog
            // UserChangeLog::create([... 'status' => UserChangeLog::STATUS_APPROVED ...]);
            UserChangeLog::create([
                'user_id' => $user->id,
                'change_key' => $field,
                'old_value' => $oldValue ?? '',
                'new_value' => $value,
                'status' => UserChangeLog::STATUS_APPROVED,
                'ip' => Tools::getClientIp(),
            ]);
        }

        // 返回最新的用户信息（包含未审核的提示状态）
        return Respond::success(UserResource::make($user->refresh()));
    }

    /**
     * 注销
     * @return void
     */
    public function revokeAccount()
    {
        /** @var \App\Models\User $user */
        $user = Auth::guard('api')
            ->user();

        $this->validate(request(), [
            'code' => 'required|checkCode:' . $user->mobile,
        ], $this->validationMessages);

        $user->status = User::STATUS_DISABLED;
        $user->save();

        Auth::guard('api')
            ->logout(true);

        $user->delete();

        return Respond::success();
    }

    public function bindThirdAccount(Request $request, $type)
    {
        $this->validate($request, [
            'third_code' => 'required',
        ], $this->validationMessages);

        /** @var \App\Models\User $user */
        $user = Auth::guard('api')
            ->user();

        // 判断是否已经绑定过
        if ($user->wx_unionid || ($user->wx_openids['app'] ?? '')) {
            throw new UserException(ErrorCodeEnum::WECHAT_BINDED);
        }

        try {
            $thirdUser = Socialite::create($type)
                ->userFromCode($request->input('third_code'));
        } catch (ValidationException $exception) {
            throw new AuthException(ErrorCodeEnum::THIRD_AUTH_FAILED);
        }

        if (!$thirdUser->getId()) {
            throw new AuthException(ErrorCodeEnum::THIRD_AUTH_FAILED);
        }

        $unionid = key_exists('unionid', $thirdUser->getRaw()) ? $thirdUser->getRaw()['unionid'] : null;

        // 判断是否已经有登陆过
        $wxUser = User::where('wx_unionid', $unionid)
            ->orWhere('wx_openids->app', $thirdUser->getId())
            ->first();

        if ($wxUser && $wxUser->isMobileConfirmed()) {
            throw new AuthException(ErrorCodeEnum::WECHAT_HASBEEN_BINDED);
        }

        $wxOpenids = $user->wx_openids;

        $wxOpenids['app'] = $thirdUser->getId();

        $thirdParty = $user->third_party;
        $thirdParty['app_wechat_nickname'] = $thirdUser->getNickname();
        $thirdParty['app_wechat_access_token'] = $thirdUser->getAccessToken();
        $thirdParty['app_wechat_refresh_token'] = $thirdUser->getRefreshToken();
        $thirdParty['app_wechat_expires_in'] = $thirdUser->getExpiresIn();

        $user->wx_unionid = $unionid ?: '';
        $user->wx_openids = $wxOpenids;
        $user->third_party = $thirdParty;
        $user->save();

        $wxUser && $wxUser->delete();

        return Respond::success();
    }

    public function unbindThirdAccount(Request $request, $type)
    {
        /** @var \App\Models\User $user */
        $user = Auth::guard('api')
            ->user();

        $wxOpenIds = $user->wx_openids;
        unset($wxOpenIds['app']);

        $thirdParty = $user->third_party;
        unset($thirdParty['app_wechat_nickname']);
        unset($thirdParty['app_wechat_access_token']);
        unset($thirdParty['app_wechat_refresh_token']);
        unset($thirdParty['app_wechat_expires_in']);


        $user->wx_unionid = '';
        $user->wx_openids = $wxOpenIds;
        $user->third_party = $thirdParty;
        $user->save();

        return Respond::success();
    }

    public function changeMobile(Request $request)
    {
        /** @var \App\Models\User $user */
        $user = Auth::guard('api')
            ->user();

        $this->validate($request, [
            'old_code' => 'required|checkCode:' . $user->mobile,
            'mobile'   => 'required|isMobile',
            'code'     => 'required|checkCode:' . $request->input('mobile'),
        ], $this->validationMessages);

        if ($user->mobile == $request->input('mobile')) {
            throw new AuthException(ErrorCodeEnum::PHONE_ALREADY_BIND);
        }

        if (User::where('mobile', $request->input('mobile'))->where('uuid', '!=', $user->uuid)->exists()) {
            throw new AuthException(ErrorCodeEnum::PHONE_ALREADY_BIND);
        }

        $oldMobile = $user->mobile;

        $user->mobile = $request->input('mobile');
        $user->save();

        UserChangeLog::create([
            'user_id'    => $user->id,
            'change_key' => 'mobile',
            'old_value'  => $oldMobile,
            'new_value'  => $request->input('mobile'),
            'status'     => UserChangeLog::STATUS_APPROVED,
            'ip'         => Tools::getClientIp(),
        ]);

        return Respond::success();
    }

    public function changePwd(Request $request)
    {
        /** @var \App\Models\User $user */
        $user = Auth::guard('api')
            ->user();
        $this->validate($request, [
            // 'old_password' => [
            //     'required',
            //     function ($attr, $value, $fail) use ($user) {
            //         if (!Hash::check($value, $user->password)) {
            //             return $fail('原密码错误');
            //         }
            //     },
            // ],
            'new_password' => [
                'required',
                'confirmed',
                'min:8',
                'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
                // 密码必顫包含大小写字母、数字、特殊字符
            ],
            'code'         => 'required|checkCode:' . $user->mobile,
        ], $this->validationMessages);

        $user->password = bcrypt($request->input('new_password'));
        $user->save();

        return Respond::success();
    }

    public function findPwdOne(Request $request)
    {
        $this->validate($request, [
            'mobile' => 'required|isMobile|exists:users,mobile',
            'code'   => 'required|checkCode:' . $request->input('mobile'),
        ], $this->validationMessages);

        $findPwdToken = bcrypt(\Str::random(16));
        \Cache::put($findPwdToken, $request->input('mobile'), Carbon::now()
            ->addMinutes(10));

        return Respond::success(['find_token' => $findPwdToken]);
    }

    public function findPwdTwo(Request $request)
    {
        $this->validate($request, [
            'find_token'   => [
                'required',
                function ($attribute, $value, $fail) {
                    if (!Cache::has($value)) {
                        return $fail('操作超时');
                    }
                },
            ],
            'new_password' => [
                'required',
                'confirmed',
                'min:8',
                'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
                // 密码必顫包含大小写字母、数字、特殊字符]
            ],
        ], $this->validationMessages);

        $user = User::where('mobile', Cache::get($request->input('find_token')))
            ->first();

        if (!$user || $user->status != User::STATUS_ACTIVE) {
            throw new UserException(ErrorCodeEnum::USER_STATUS_ERROR);
        }

        $user->password = bcrypt($request->input('new_password'));
        $user->save();

        Cache::forget($request->input('find_token'));

        return Respond::success();
    }

    public function decryptSensitive(Request $request)
    {
        return Respond::success();
    }


    public function bindDevice(Request $request)
    {
        $user = Auth::guard('api')
            ->user();

        $validated = $request->validate([
            'device_token'  => 'required',
            'platform_type' => 'required|in:' . implode(',', [
                PushMessageDeliveryEnum::DELIVERY_ANDROID->value,
                PushMessageDeliveryEnum::DELIVERY_IOS->value,
                PushMessageDeliveryEnum::DELIVERY_HARMONY->value,
            ]),
        ]);

        UserDevice::updateOrCreate([
            'user_uuid'     => $user->uuid,
            'device_id'     => Tools::getDeviceId(),
            'platform_type' => $validated['platform_type'],
        ], [
            'user_uuid'         => $user->uuid,
            'device_id'         => Tools::getDeviceId(),
            'device_token'      => $validated['device_token'],
            'platform_type'     => $validated['platform_type'],
            'device_type'       => Tools::getDeviceType(),
            'app_id'            => Tools::getAppId(),
            'client_type'       => Tools::getPlatform(),
            'client_version'    => Tools::getAppVersion(),
            'device_os'         => Tools::getOS(),
            'device_os_version' => Tools::getOSVersion(),
            'is_active'         => UserDevice::IS_ACTIVE_YES,
        ]);

        // 检查是否已经绑定过
        //        if (UserDevice::where('device_id', Tools::getDeviceId())
        //                      ->where('user_uuid', $user->uuid)
        //                      ->exists()) {
        //            UserDevice::where('user_uuid', $user->uuid)
        //                      ->where('device_id', Tools::getDeviceId())
        //                      ->where('is_active', UserDevice::IS_ACTIVE_NO)
        //                      ->where('platform_type', $validated['platform_type'])
        //                      ->update([
        //                          'is_active' => UserDevice::IS_ACTIVE_YES,
        //                      ]);
        //
        //            return Respond::success();
        //        }
        //
        //        $userDevice = UserDevice::create([
        //            'user_uuid' => $user->uuid,
        //            'device_id' => Tools::getDeviceId(),
        //            'device_token' => $validated['device_token'],
        //            'platform_type' => $validated['platform_type'],
        //            'device_type' => Tools::getDeviceType(),
        //            'app_id' => Tools::getAppId(),
        //            'client_type' => Tools::getPlatform(),
        //            'client_version' => Tools::getAppVersion(),
        //            'device_os' => Tools::getOS(),
        //            'device_os_version' => Tools::getOSVersion(),
        //            'is_active' => UserDevice::IS_ACTIVE_YES,
        //        ]);

        return Respond::success();
    }

    public function unbindDevice(Request $request)
    {
        $user = Auth::guard('api')
            ->user();

        $validated = $request->validate([
            'device_token'  => 'required',
            'platform_type' => 'required|in:' . implode(',', [
                PushMessageDeliveryEnum::DELIVERY_ANDROID->value,
                PushMessageDeliveryEnum::DELIVERY_IOS->value,
                PushMessageDeliveryEnum::DELIVERY_HARMONY->value,
            ]),
        ]);

        UserDevice::where('user_uuid', $user->uuid)
            ->where('device_id', Tools::getDeviceId())
            ->where('is_active', UserDevice::IS_ACTIVE_YES)
            ->update([
                'is_active' => UserDevice::IS_ACTIVE_NO,
            ]);

        return Respond::success();
    }

    public function qrBindAdminUserView()
    {
        return view('bindAdmin');
    }

    public function qrBindAdminUser(Request $request)
    {
        $validated = $request->validate([
            'qr_code' => 'required|string',
        ]);
        $user = Auth::guard('api')
            ->user();

        // 生成锁key
        $lockKey = 'qr_bind:' . md5($validated['qr_code']);

        // 获取锁实例,10秒后自动释放
        $lock = Cache::lock($lockKey, 10);

        try {
            // 尝试获取锁
            if (!$lock->get()) {
                return Respond::error('二维码正在处理中或已被使用');
            }

            // 检查是否已被使用
            if (Cache::has($lockKey . ':used')) {
                return Respond::error('二维码已被使用');
            }

            GmSm::configure(publicKey: config('sm2.data_pub_key'), privateKey: config('sm2.data_priv_key'));

            try {
                $qrData = json_decode(GmSm::sm2Decrypt($validated['qr_code']), true);
            } catch (\Throwable $e) {
                return Respond::error('非法参数');
            }

            if (!$qrData) {
                return Respond::error('非法参数');
            }

            if ($qrData['timestamp'] + 60 * 10 < time()) {
                return Respond::error('二维码已过期');
            }

            $adminUser = AdminUser::where('uuid', $qrData['uuid'])
                ->first();

            if (!$adminUser || !$adminUser->isActive()) {
                return Respond::error('管理员不存在或已禁用');
            }

            $adminUser->bind_user_uuid = $user->uuid;
            $adminUser->save();

            // 标记二维码已使用
            Cache::forever($lockKey . ':used', true);

            return Respond::success();
        } finally {
            // 释放锁
            $lock->release();
        }
    }

    /**
     * 工作台应用列表
     * 获取用户绑定的管理员用户可访问的应用列表
     * 只返回允许在前端工作台展示的应用
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function workspace()
    {
        /** @var \App\Models\User $user */
        $user = Auth::guard('api')
            ->user();

        // 获取绑定的管理员用户
        $adminUser = $user->bindAdminUser;

        if (!$adminUser) {
            return Respond::success([]);
        }

        // 获取管理员用户可访问的所有应用，并只保留允许在前端工作台展示的应用
        $clients = $adminUser->getAllAccessibleOauthClientsWithCache()
            ->where('is_revoked', Client::IS_REVOKED_NO)
            ->where('show_in_workspace', Client::SHOW_IN_WORKSPACE_YES)
            ->where('is_workspace_client', Client::IS_WORKSPACE_CLIENT_YES)
            ->sortByDesc('sort')
            ->values();

        return Respond::success(WorkspaceClientResource::collection($clients));
    }
}
