<?php

namespace App\Utils;

use Rtgm\sm\RtSm3;

/**
 * 签名验证器类
 * 用于生成和验证请求签名，确保数据的完整性和安全性
 */
class SignatureValidator
{
    private string $clientId;
    private string $clientSecret;
    private string $signatureStr = '';

    /**
     * 需要在签名计算中排除的字段列表
     */
    private array $excludeFields = [
        'file',    // 文件上传字段
        'files',   // 多文件上传字段
        'signature', // 签名字段本身
    ];

    /**
     * 构造函数
     * @param string $clientId 客户端ID
     * @param string $clientSecret 客户端密钥
     */
    public function __construct(string $clientId, string $clientSecret) {
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
    }

    /**
     * 验证签名是否有效
     * @param array $data 要验证的数据
     * @param string|null $signature 待验证的签名，如果为null则从data中获取
     * @return bool 签名是否匹配
     */
    public function verifySignature(array $data, ?string $signature = null): bool {
        $signToVerify = $signature ?? ($data['signature'] ?? '');
        $generatedSign = $this->generateSignature($data);

        //if(strtoupper($signToVerify) !== strtoupper($generatedSign)){
        //    $generatedSign = $this->generateSignature($data,true);
        //    return strtoupper($signToVerify) === strtoupper($generatedSign);
        //}

        return strtoupper($signToVerify) === strtoupper($generatedSign);
    }

    /**
     * 生成签名
     * @param array $data 要签名的数据
     * @return string 生成的签名字符串
     */
    public function generateSignature(array $data,$withRawUrlencode = false): string {
        // 过滤掉以下划线开头的字段
        $data = array_filter($data, fn($key) => !str_starts_with($key, '_'), ARRAY_FILTER_USE_KEY);
        // 过滤掉排除字段
        $data = $this->filterExcludedFields($data);
        // 处理各种数据类型的值
        $data = $this->processValues($data,$withRawUrlencode);

        // 按键名排序
        ksort($data);

        // 构建签名部分
        $signParts = [];
        foreach ($data as $key => $value) {
            $signParts[] = "{$key}={$value}";
        }

        // 组装最终签名字符串：clientId + 参数 + clientSecret
        $this->signatureStr = "{$this->clientId}&" . implode('&', $signParts) . "&{$this->clientSecret}";

        // 使用SM3算法生成签名
        return (new RtSm3())->digest($this->signatureStr, 1);
    }

    /**
     * 处理数据值，包括数组和基本类型的值
     * @param array $data 要处理的数据
     * @param $withRawUrlencode 使用原始urlEncode
     * @return array 处理后的数据
     */
    private function processValues(array $data,$withRawUrlencode=false): array {
        return array_map(function ($value) use($withRawUrlencode){
            if (is_array($value)) {
                if (empty($value)) {
                    if($withRawUrlencode){
                        // return rawurlencode("[]");
                    }
                    // return urlencode("[]");
                    return "[]";
                }
                // 对数组进行递归排序
                $value = $this->sortArrayRecursively($value);
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            } else {
                $value = $this->normalizeValue($value);
            }
            if($withRawUrlencode){
                // return rawurlencode($value);
            }
            // return urlencode($value);
            return $value;
        }, $data);
    }

    /**
     * 递归排序数组
     * @param array $array 要排序的数组
     * @return array 排序后的数组
     */
    private function sortArrayRecursively(array $array): array {
        // 处理数组中的每个值
        foreach ($array as &$value) {
            if (is_array($value)) {
                $value = $this->sortArrayRecursively($value);
            } else {
                $value = $this->normalizeValue($value);
            }
        }
        unset($value);

        // 只对关联数组进行键名排序
        if ($this->isAssociativeArray($array)) {
            ksort($array);
        }

        return $array;
    }

    /**
     * 判断是否为关联数组
     * @param array $array 要判断的数组
     * @return bool 是否为关联数组
     */
    private function isAssociativeArray(array $array): bool {
        if (empty($array)) return false;

        return array_keys($array) !== range(0, count($array) - 1);
    }

    /**
     * 标准化各种类型的值为字符串
     * @param mixed $value 要标准化的值
     * @return string 标准化后的字符串
     */
    private function normalizeValue($value): string {
        // 处理布尔值
        if (is_bool($value)) {
            return $value ? "1" : "0";
        }

        // 处理空值
        if ($value === null || trim($value) === '') {
            return "";
        }

        // 处理字符串
        if (is_string($value)) {
            return $value;
        }

        // 处理数字（整数和浮点数）
        if (is_numeric($value)) {
            if (is_float($value) || str_contains((string)$value, '.')) {
                // 移除末尾的零和小数点
                $value = rtrim(rtrim((string)$value, '0'), '.');
                // 如果是整数值的浮点数，转换为整数
                if (floor((float)$value) == $value) {
                    $value = (string)intval($value);
                }
            }

            return (string)$value;
        }

        return (string)$value;
    }

    /**
     * 过滤排除字段
     * @param array $data 要过滤的数据
     * @return array 过滤后的数据
     */
    private function filterExcludedFields(array $data): array {
        return array_filter($data, function ($value, $key) {
            // 排除指定字段
            if (in_array($key, $this->excludeFields)) {
                return false;
            }

            // 保留布尔值
            if (is_bool($value)) {
                return true;
            }

            // 保留非空值和数字
            return !empty($value) || is_numeric($value);
        }, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * 设置要排除的字段
     * @param array $fields 要排除的字段数组
     */
    public function setExcludeFields(array $fields): void {
        $this->excludeFields = array_merge($this->excludeFields, $fields);
    }

    /**
     * 获取最后生成的签名原始字符串
     * @return string 签名原始字符串
     */
    public function getSignatureStr(): string {
        // 隐藏clientSecret
        return str_replace($this->clientSecret, '***access_secret_key***', $this->signatureStr);
    }
}
