<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('oauth_client_accessibles', function (Blueprint $table) {
            $table->unsignedBigInteger('oauth_client_id');
            $table->string('accessible_id');
            $table->string('accessible_type');
            $table->timestamps();

            $table->primary(['oauth_client_id', 'accessible_id', 'accessible_type']);

            $table->index(['accessible_id', 'accessible_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oauth_client_accessibles');
    }
};
