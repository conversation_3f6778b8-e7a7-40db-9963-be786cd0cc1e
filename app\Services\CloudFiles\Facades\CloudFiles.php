<?php

namespace App\Services\CloudFiles\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Services\CloudFiles\Contracts\CloudFilesInterface driver(string $driver = null)
 * @method static void extend(string $driver, \Closure $callback)
 * @method static string getFileUrl(string $provider, string $path, ?int $expiration = null) 根据provider和path获取文件URL。如果provider为url则直接返回path，否则返回对应云存储的文件URL或签名URL
 *
 * @see \App\Services\CloudFiles\CloudFilesManager
 */
class CloudFiles extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'cloudfiles';
    }
}
