<?php

use App\Http\Controllers\{
    Au<PERSON><PERSON><PERSON><PERSON><PERSON>,
    User<PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON><PERSON>roller,
    User<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON><PERSON>roller,
    <PERSON>r<PERSON><PERSON>age<PERSON><PERSON>roller,
    <PERSON>name<PERSON><PERSON><PERSON><PERSON>roller,
    MaterialController,
    QuickBindController
};

use App\Http\AdminControllers\AuthController as AdminAuthController;

/*
|--------------------------------------------------------------------------
| API 路由配置
|--------------------------------------------------------------------------
|
| 这些路由由 RouteServiceProvider 自动加载
| 已在 app.php 中统一配置了 api 前缀和 auth:api 中间件
|
*/

/*
|--------------------------------------------------------------------------
| 认证相关路由
|--------------------------------------------------------------------------
*/

Route::prefix('auth')
    ->group(function () {
        // 退出登录
        Route::delete('logout', [AuthController::class, 'logout']);
        // 刷新token
        Route::put('refresh', [AuthController::class, 'refresh']);
    });

/*
|--------------------------------------------------------------------------
| 用户相关路由
|--------------------------------------------------------------------------
*/
Route::prefix('user')
    ->group(function () {
        // 个人资料管理
        Route::get('me', [UserController::class, 'my']);                          // 获取个人信息
        Route::put('update', [UserController::class, 'updateAccount']);           // 更新账号信息
        Route::delete('revoke', [UserController::class, 'revokeAccount']);        // 注销账号
        Route::post('change-mobile', [UserController::class, 'changeMobile']);    // 更换手机号
        Route::post('change-pwd', [UserController::class, 'changePwd']);          // 修改密码
        Route::post('bind-admin-user', [UserController::class, 'qrBindAdminUser']); // 绑定管理员账号

        // 第三方账号管理
        Route::post('/bind-third-account/{platform}', [UserController::class, 'bindThirdAccount'])
            ->whereIn('platform', ['wechat']);     // 绑定第三方账号
        Route::delete('/unbind-third-account/{platform}', [UserController::class, 'unbindThirdAccount'])
            ->whereIn('platform', ['wechat']);     // 解绑第三方账号

        // 设备管理
        Route::prefix('device')
            ->group(function () {
                Route::post('/bind', [UserController::class, 'bindDevice']);           // 绑定设备
                Route::post('/unbind', [UserController::class, 'unbindDevice']);       // 解绑设备
            });

        // 工作台应用
        Route::get('/workspace', [UserController::class, 'workspace']);                 // 获取工作台应用列表

        // 应用授权管理
        Route::prefix('authorizations')
            ->group(function () {
                Route::get('/', [UserAuthorizationController::class, 'authorizedApplications']);       // 获取已授权应用列表
                Route::get('/{authorizationId}', [
                    UserAuthorizationController::class,
                    'applicationDetail',
                ]);    // 获取授权详情
                Route::delete('/{authorizationId}/revoke', [
                    UserAuthorizationController::class,
                    'revokeAuthorization',
                ]);  // 撤销授权
            });

        // 人脸验证
        Route::prefix('face-verify')
            ->group(function () {
                Route::post('init', [RealnameAuthController::class, 'bindInit']);     // 初始化人脸绑定
                Route::post('unbind', [RealnameAuthController::class, 'unbindInit']); // 解绑人脸
                Route::post('check', [RealnameAuthController::class, 'checkResult']); // 验证结果查询
            });
    });

/*
|--------------------------------------------------------------------------
| 短信服务路由
|--------------------------------------------------------------------------
*/
Route::prefix('sms')
    ->group(function () {
        Route::post('send-to-self', [SmsController::class, 'sendToSelf']);       // 发送短信到当前用户手机号
    });

/*
|--------------------------------------------------------------------------
| OAuth2.0 认证路由
|--------------------------------------------------------------------------
*/
Route::middleware('signature:app')
    ->prefix('oauth2')
    ->group(function () {
        Route::get('check-auth-status', [OAuthController::class, 'checkAuthStatus']);     // 检查授权状态
        Route::post('authorize', [OAuthController::class, 'authorize']);                  // 确认授权
    });

/*
|--------------------------------------------------------------------------
| 消息推送路由
|--------------------------------------------------------------------------
*/
Route::prefix('push/my')
    ->group(function () {
        // 消息管理
        Route::get('/messages', [UserMessageController::class, 'getUserMessages']);           // 获取用户消息列表
        Route::get('/message-category-info/{category}', [UserMessageController::class, 'getMessageCategoryInfo']); // 获取消息分类信息
        Route::get('unread-counts', [UserMessageController::class, 'getUnreadCounts']);   // 获取未读消息数量
        Route::post('mark-as-read', [UserMessageController::class, 'markAsRead']);        // 标记消息为已读
        Route::post('mark-all-as-read', [UserMessageController::class, 'markAllAsRead']); // 标记所有消息为已读
    });

/*
|--------------------------------------------------------------------------
| 图片上传路由
|--------------------------------------------------------------------------
*/

Route::prefix('materials')
    ->group(function () {
        Route::get('upload/policy', [MaterialController::class, 'policy']);
    });

/*
|--------------------------------------------------------------------------
| 管理员扫码登录相关路由
|--------------------------------------------------------------------------
*/
Route::middleware('signature:app')
    ->prefix('auth-qr')
    ->group(function () {
        Route::post('scan', [AdminAuthController::class, 'scanQrCode']);       // 扫码
        Route::post('confirm', [AdminAuthController::class, 'confirmQrLogin']); // 确认登录
    });

/*
|--------------------------------------------------------------------------
| 工作台扫码绑定企业联系人相关路由
|--------------------------------------------------------------------------
*/
Route::middleware('signature:app')
    ->prefix('workspace/quick-bind')
    ->group(function () {
        Route::get('admin-user-info', [QuickBindController::class, 'getAdminUserInfo']); // 获取企业联系人信息
        Route::post('confirm-bind', [QuickBindController::class, 'confirmBindAdminUser']); // 确认绑定企业联系人
    });
