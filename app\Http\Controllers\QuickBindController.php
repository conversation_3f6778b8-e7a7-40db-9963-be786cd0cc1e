<?php

namespace App\Http\Controllers;

use App\Models\AdminUser;
use App\Utils\Respond;
use Illuminate\Http\Request;

class QuickBindController extends Controller
{
    public function quickBindAdminUserView()
    {
        return view('quickBindAdminUser');
    }

    /**
     * 根据用户信息获取adminUser内容
     */
    public function getAdminUserInfo()
    {
        $user = auth('api')->user();

        $adminUser = AdminUser::where('mobile', $user->mobile)
            ->where('status', AdminUser::STATUS_ENABLED)
            ->first();

        if (!$adminUser) {
            return Respond::success([
                'can_bind' => false,
                'msg' => '未找到对应企业联系人,请联系管理增加.',
            ]);
        }

        if($adminUser->bind_user_uuid == $user->uuid){
            return Respond::success([
                'can_bind' => false,
                'msg' => '已绑定当前用户',
                'department' => $adminUser->departments?->first()?->name,
                'true_name' => mb_substr($adminUser->true_name, 0, 1) . '**',
                'mobile' => substr($adminUser->mobile, 0, 3) . '****' . substr($adminUser->mobile, -4),
            ]);
        }elseif($adminUser->bind_user_uuid){
            return Respond::success([
                'can_bind' => false,
                'msg' => '其他用户已确认,请联系管理核实',
            ]);
        }

        return Respond::success([
            'can_bind' => true,
            // 隐私保护 只显示第一个字
            'department' => $adminUser->departments?->first()?->name,
            'true_name' => mb_substr($adminUser->true_name, 0, 1) . '**',
            'mobile' => substr($adminUser->mobile, 0, 3) . '****' . substr($adminUser->mobile, -4),
        ]);
    }

    public function confirmBindAdminUser()
    {
        $user = auth('api')->user();
        $adminUser = AdminUser::where('mobile', $user->mobile)
            ->where('status', AdminUser::STATUS_ENABLED)
            ->whereNull('bind_user_uuid')
            ->first();

        if (!$adminUser) {
            return Respond::error(100001, '未找到对应企业联系人,请联系管理增加.');
        }

        $adminUser->bind_user_uuid = $user->uuid;
        $adminUser->save();

        return Respond::success([
            'msg' => '确认成功.',
        ]);
    }
}
