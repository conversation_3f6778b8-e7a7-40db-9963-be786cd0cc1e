<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $oauth_client_subscription_id 订阅配置ID
 * @property string $user_uuid 用户UUID
 * @property int $status 状态:1启用,0禁用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\OauthClientSubscription|null $oauthClientSubscription
 * @property-read mixed $status_text
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereOauthClientSubscriptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereUserUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperUserSubscription
 */
class UserSubscription extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'oauth_client_subscription_id',
        'user_uuid',
        'status',
    ];

    protected $casts = [];

    const STATUS_DISABLE = 0;
    const STATUS_ENABLE = 1;

    public static array $statusMap = [
        self::STATUS_ENABLE => '已订阅',
        self::STATUS_DISABLE => '已取消',
    ];

    protected function statusText(): Attribute {
        return Attribute::make(get: fn($value) => self::$statusMap[$value] ?? '未知');
    }

    public function user(): BelongsTo {
        return $this->belongsTo(User::class);
    }

    public function oauthClientSubscription(): BelongsTo {
        return $this->belongsTo(OauthClientSubscription::class);
    }
}
