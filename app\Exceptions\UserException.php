<?php

namespace App\Exceptions;

class UserException extends BaseException
{
    //禁止更新, 注销失败, 已绑定微信, 未绑定微信, 已实名, 未实名, 实名失败, 等待审核
//    const FORBIDDEN_UPDATE = 140001;
//    const LOGOUT_FAILED = 140002;
//    const WECHAT_BINDED = 140003;
//    const WECHAT_UNBINDED = 140004;
//    const REALNAME_BINDED = 140005;
//    const REALNAME_UNBINDED = 140006;
//    const REALNAME_FAILED = 140007;
//    const WAITING_AUDIT = 140008;
//    const USER_STATUS_ERROR = 140009;
//
//    public static function message($code) {
//        $msgArr = [
//            static::FORBIDDEN_UPDATE => '禁止更新',
//            static::LOGOUT_FAILED => '注销失败',
//            static::WECHAT_BINDED => '已绑定微信',
//            static::WECHAT_UNBINDED => '未绑定微信',
//            static::REALNAME_BINDED => '已实名',
//            static::REALNAME_UNBINDED => '未实名',
//            static::REALNAME_FAILED => '实名失败',
//            static::WAITING_AUDIT => '等待审核',
//            static::USER_STATUS_ERROR => '用户状态异常',
//        ];
//
//        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
//    }
}
