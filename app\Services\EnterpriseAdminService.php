<?php

namespace App\Services;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Models\EnterpriseAdmin;
use App\Models\Enterprise;
use App\Models\User;
use App\Models\Client;
use App\Models\AdminUser;
use Illuminate\Support\Facades\DB;

class EnterpriseAdminService
{
    /**
     * 验证用户是否可以绑定为企业管理员
     */
    public function validateUserForBinding(string $userUuid, string $enterpriseId): array
    {
        $user = User::find($userUuid);
        if (!$user) {
            throw new AdminException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        // 检查用户是否已经是企业管理员
        if ($user->enterpriseAdmins()->exists()) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_ADMIN_USER_ALREADY_BOUND);
        }

        // 检查用户是否已经是企业拥有者
        if ($user->bindAdminUser && $user->bindAdminUser->type === AdminUser::TYPE_ENTERPRISE) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_ADMIN_USER_IS_ENTERPRISE_OWNER);
        }

        // 检查企业是否存在且激活
        $enterprise = Enterprise::find($enterpriseId);
        if (!$enterprise) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_NOT_FOUND);
        }

        if (!$enterprise->isActive()) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_STATUS_ERROR);
        }

        return [
            'user' => $user,
            'enterprise' => $enterprise,
        ];
    }

    /**
     * 绑定用户为企业管理员
     */
    public function bindUser(string $userUuid, string $enterpriseId, ?string $createdBy = null): EnterpriseAdmin
    {
        $validation = $this->validateUserForBinding($userUuid, $enterpriseId);
        
        return DB::transaction(function () use ($validation, $createdBy) {
            return EnterpriseAdmin::create([
                'enterprise_id' => $validation['enterprise']->id,
                'user_id' => $validation['user']->uuid,
                'status' => EnterpriseAdmin::STATUS_ENABLED,
                'created_by' => $createdBy,
            ]);
        });
    }

    /**
     * 通过手机号绑定用户为企业管理员
     */
    public function bindUserByMobile(string $mobile, string $enterpriseId, ?string $createdBy = null): EnterpriseAdmin
    {
        $user = User::where('mobile', $mobile)->first();
        if (!$user) {
            throw new AdminException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        return $this->bindUser($user->uuid, $enterpriseId, $createdBy);
    }

    /**
     * 解绑企业管理员
     */
    public function unbindUser(string $adminId): bool
    {
        $admin = EnterpriseAdmin::find($adminId);
        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_ADMIN_NOT_FOUND);
        }

        return DB::transaction(function () use ($admin) {
            // 检查是否有关联的客户端
            if ($admin->clients()->exists()) {
                throw new AdminException(ErrorCodeEnum::ENTERPRISE_ADMIN_HAS_CLIENTS);
            }

            // 检查是否拥有客户端
            if ($admin->ownedClients()->exists()) {
                throw new AdminException(ErrorCodeEnum::ENTERPRISE_ADMIN_HAS_CLIENTS);
            }

            return $admin->delete();
        });
    }

    /**
     * 为企业管理员分配客户端
     */
    public function assignClient(string $adminId, string $clientId, bool $isOwner = false): bool
    {
        $admin = EnterpriseAdmin::find($adminId);
        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_ADMIN_NOT_FOUND);
        }

        $client = Client::find($clientId);
        if (!$client) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_CLIENT_NOT_FOUND);
        }

        // 检查客户端是否属于同一企业
        if ($client->enterprise_id !== $admin->enterprise_id) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_PERMISSION_DENIED);
        }

        return DB::transaction(function () use ($admin, $client, $isOwner) {
            // 如果是设置为拥有者，需要先检查是否已有拥有者
            if ($isOwner) {
                $existingOwner = $client->owner();
                if ($existingOwner && $existingOwner->id !== $admin->id) {
                    throw new AdminException(ErrorCodeEnum::ENTERPRISE_CLIENT_OWNER_EXISTS);
                }

                // 在admin_oauth_client表中设置拥有者关系
                DB::table('admin_oauth_client')->updateOrInsert(
                    [
                        'admin_uuid' => $admin->id,
                        'oauth_client_id' => $client->id,
                    ],
                    [
                        'is_owner' => 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                );
            }

            // 在enterprise_admin_clients表中设置管理关系
            if (!$admin->clients()->where('client_id', $client->id)->exists()) {
                $admin->clients()->attach($client->id, [
                    'status' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            return true;
        });
    }

    /**
     * 撤销企业管理员的客户端权限
     */
    public function revokeClient(string $adminId, string $clientId): bool
    {
        $admin = EnterpriseAdmin::find($adminId);
        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '企业管理员不存在');
        }

        return DB::transaction(function () use ($admin, $clientId) {
            // 从enterprise_admin_clients表中移除关系
            $admin->clients()->detach($clientId);

            // 从admin_oauth_client表中移除拥有者关系
            DB::table('admin_oauth_client')
                ->where('admin_uuid', $admin->id)
                ->where('oauth_client_id', $clientId)
                ->delete();

            return true;
        });
    }

    /**
     * 获取企业的所有管理员
     */
    public function getEnterpriseAdmins(string $enterpriseId, bool $activeOnly = true): \Illuminate\Database\Eloquent\Collection
    {
        $query = EnterpriseAdmin::where('enterprise_id', $enterpriseId)
                               ->with(['user', 'roles']);

        if ($activeOnly) {
            $query->enabled();
        }

        return $query->get();
    }

    /**
     * 检查用户是否为指定企业的管理员
     */
    public function isEnterpriseAdmin(string $userUuid, string $enterpriseId): bool
    {
        return EnterpriseAdmin::where('user_id', $userUuid)
                             ->where('enterprise_id', $enterpriseId)
                             ->enabled()
                             ->exists();
    }

    /**
     * 获取用户管理的所有企业
     */
    public function getUserEnterprises(string $userUuid): \Illuminate\Database\Eloquent\Collection
    {
        $user = User::find($userUuid);
        if (!$user) {
            return collect();
        }

        return $user->activeEnterpriseAdmins()
                   ->with('enterprise')
                   ->get()
                   ->pluck('enterprise');
    }

    /**
     * 切换企业管理员状态
     */
    public function toggleStatus(string $adminId): EnterpriseAdmin
    {
        $admin = EnterpriseAdmin::find($adminId);
        if (!$admin) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_ADMIN_NOT_FOUND);
        }

        $admin->status = $admin->status === EnterpriseAdmin::STATUS_ENABLED 
                        ? EnterpriseAdmin::STATUS_DISABLED 
                        : EnterpriseAdmin::STATUS_ENABLED;
        $admin->save();

        return $admin;
    }

    /**
     * 批量操作企业管理员状态
     */
    public function batchUpdateStatus(array $adminIds, int $status): int
    {
        if (!in_array($status, [EnterpriseAdmin::STATUS_ENABLED, EnterpriseAdmin::STATUS_DISABLED])) {
            throw new AdminException(ErrorCodeEnum::ENTERPRISE_INVALID_AUDIT_STATUS);
        }

        return EnterpriseAdmin::whereIn('id', $adminIds)->update(['status' => $status]);
    }
}
