<?php

namespace App\Http\Resources;

use App\Enums\OAuthScopeEnum;
use App\Utils\Tools;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Authorization Resource
 * @property-read \App\Models\OAuthUserAuthorization $resource
 * @mixin \App\Models\OAuthUserAuthorization
 */
class AuthorizationResouce extends JsonResource
{
    public function toArray(Request $request) {
        return [
            'authorization_id' => $this->id,
            $this->merge(Tools::mergeDateTimeFormat('authorized_at', $this->authorized_at)),
            $this->merge(Tools::mergeDateTimeFormat('last_used_at', $this->last_used_at)),
            'granted_scopes' => collect($this->granted_scopes)->map(function ($scope) {
                return [
                    'key' => $scope,
                    'value' => OAuthScopeEnum::from($scope)
                                             ->label(),
                ];
            }),
            'client' => ClientResource::make($this->client),
        ];
    }
}
