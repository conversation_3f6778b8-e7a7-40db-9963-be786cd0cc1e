<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $oauth_client_id OAuth Client ID
 * @property int $push_template_id 模板ID
 * @property int $status 状态:1启用,0禁用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \App\Models\Client|null $client
 * @property-read \App\Models\PushTemplate|null $pushTemplate
 * @property-read mixed $status_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate whereOauthClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate wherePushTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientTemplate whereUpdatedAt($value)
 * @mixin \Eloquent
 * @mixin IdeHelperOauthClientTemplate
 */
class OauthClientTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'oauth_client_id',
        'push_template_id',
        'status',
    ];

    protected $appends = [
        'status_text',
    ];

    const STATUS_DISABLE = 0;
    const STATUS_ENABLE = 1;

    public static array $statusMap = [
        self::STATUS_ENABLE => '启用',
        self::STATUS_DISABLE => '禁用',
    ];

    protected function statusText(): Attribute {
        return Attribute::make(get: fn() => self::$statusMap[$this->status] ?? '未知');
    }

    public function client(): BelongsTo {
        return $this->belongsTo(Client::class);
    }

    public function pushTemplate(): BelongsTo {
        return $this->belongsTo(PushTemplate::class);
    }
}
