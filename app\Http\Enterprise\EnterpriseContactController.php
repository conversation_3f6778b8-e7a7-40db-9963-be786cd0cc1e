<?php

namespace App\Http\Enterprise;

use App\Http\Enterprise\EnterpriseBaseController;
use App\Models\Enterprise;
use App\Models\EnterpriseContact;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class EnterpriseContactController extends EnterpriseBaseController
{
    protected $validationMessages = [
        'name.required'         => '姓名不能为空',
        'name.string'           => '姓名必须是字符串',
        'name.max'              => '姓名不能超过50个字符',
        'display_name.string'   => '显示姓名必须是字符串',
        'display_name.max'      => '显示姓名不能超过50个字符',
        'mobile.string'         => '手机号必须是字符串',
        'mobile.max'            => '手机号不能超过20个字符',
        'short_mobile.string'   => '短号必须是字符串',
        'short_mobile.max'      => '短号不能超过20个字符',
        'office_phone.string'   => '办公电话必须是字符串',
        'office_phone.max'      => '办公电话不能超过20个字符',
        'title.string'          => '职位必须是字符串',
        'title.max'             => '职位不能超过50个字符',
        'email.email'           => '邮箱格式不正确',
        'email.max'             => '邮箱不能超过100个字符',
        'department_id.exists'  => '所选部门不存在',
        'is_leader.in'          => '是否为部门负责人值无效',
        'status.in'             => '状态值无效',
        'remark.string'         => '备注必须是字符串',
    ];

    protected const PERMISSION_MAP = [
        'index' => '企业联系人管理.查看列表',
        'store' => '企业联系人管理.创建',
        'show' => '企业联系人管理.查看详情',
        'update' => '企业联系人管理.编辑',
        'destroy' => '企业联系人管理.删除',
        'toggleLeader' => '企业联系人管理.设置/取消部门负责人',
    ];

    /**
     * 获取企业联系人列表
     */
    public function index(Request $request, $enterpriseId)
    {
        $enterprise = Enterprise::findOrFail($enterpriseId);
        
        $query = EnterpriseContact::where('enterprise_id', $enterpriseId)
            ->with('department')
            ->when($request->input('keyword'), function ($query, $keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                        ->orWhere('mobile', 'like', "%{$keyword}%")
                        ->orWhere('email', 'like', "%{$keyword}%");
                });
            })
            ->when($request->filled('department_id'), function ($query, $departmentId) {
                $query->where('department_id', $departmentId);
            })
            ->when($request->filled('status'), function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->filled('is_leader'), function ($query, $isLeader) {
                $query->where('is_leader', $isLeader);
            });
        
        $contacts = $query->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 15));
        
        return Respond::success($contacts);
    }

    /**
     * 获取联系人详情
     */
    public function show($enterpriseId, $id)
    {
        $contact = EnterpriseContact::where('enterprise_id', $enterpriseId)
            ->with('department')
            ->findOrFail($id);
        
        return Respond::success($contact);
    }

    /**
     * 创建企业联系人
     */
    public function store(Request $request, $enterpriseId)
    {
        $enterprise = Enterprise::findOrFail($enterpriseId);
        
        $validated = $request->validate([
            'name' => 'required|string|max:50',
            'display_name' => 'nullable|string|max:50',
            'mobile' => 'required|string|max:20',
            'short_mobile' => 'nullable|string|max:20',
            'office_phone' => 'nullable|string|max:20',
            'title' => 'nullable|string|max:50',
            'email' => 'nullable|email|max:100',
            'department_id' => [
                'nullable',
                'string',
                Rule::exists('enterprise_departments', 'id')->where(function ($query) use ($enterpriseId) {
                    return $query->where('enterprise_id', $enterpriseId);
                }),
            ],
            'is_leader' => 'nullable|in:0,1',
            'status' => 'nullable|in:0,1',
            'remark' => 'nullable|string',
        ], $this->validationMessages);
        
        
        try {
            DB::beginTransaction();
            
            $validated['enterprise_id'] = $enterpriseId;
            $validated['status'] = $validated['status'] ?? EnterpriseContact::STATUS_ENABLED;
            
            $contact = EnterpriseContact::create($validated);
            
            DB::commit();
            return Respond::success($contact);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('创建联系人失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新企业联系人
     */
    public function update(Request $request, $enterpriseId, $id)
    {
        $contact = EnterpriseContact::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);
        
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:50',
            'display_name' => 'nullable|string|max:50',
            'mobile' => 'nullable|string|max:20',
            'short_mobile' => 'nullable|string|max:20',
            'office_phone' => 'nullable|string|max:20',
            'title' => 'nullable|string|max:50',
            'email' => 'nullable|email|max:100',
            'department_id' => [
                'nullable',
                'string',
                Rule::exists('enterprise_departments', 'id')->where(function ($query) use ($enterpriseId) {
                    return $query->where('enterprise_id', $enterpriseId);
                }),
            ],
            'is_leader' => 'nullable|in:0,1',
            'status' => 'nullable|in:0,1',
            'remark' => 'nullable|string',
        ], $this->validationMessages);
        

        try {
            DB::beginTransaction();
            
            $contact->update($validated);
            
            DB::commit();
            return Respond::success($contact);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('更新联系人失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除企业联系人
     */
    public function destroy($enterpriseId, $id)
    {
        $contact = EnterpriseContact::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);
        
        try {
            DB::beginTransaction();
            
            $contact->delete();
            
            DB::commit();
            return Respond::success(null, '删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('删除联系人失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量导入联系人
     */
    public function batchImport(Request $request, $enterpriseId)
    {
        $validator = Validator::make($request->all(), [
            'contacts' => 'required|array',
            'contacts.*.name' => 'required|string|max:50',
            'contacts.*.department_id' => [
                'nullable',
                'string',
                Rule::exists('enterprise_departments', 'id')->where(function ($query) use ($enterpriseId) {
                    return $query->where('enterprise_id', $enterpriseId);
                }),
            ],
        ]);
        
        if ($validator->fails()) {
            return Respond::error($validator->errors()->first(), null, null, 422);
        }
        
        try {
            DB::beginTransaction();
            
            $contacts = [];
            foreach ($request->input('contacts') as $contactData) {
                $contactData['enterprise_id'] = $enterpriseId;
                $contactData['status'] = $contactData['status'] ?? 1;
                
                $contact = EnterpriseContact::create($contactData);
                $contacts[] = $contact;
            }
            
            DB::commit();
            return Respond::success($contacts, '批量导入成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('批量导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置/取消联系人为部门负责人
     */
    public function toggleLeader(Request $request, $enterpriseId, $id)
    {
        $contact = EnterpriseContact::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);
        
        if (!$contact->department_id) {
            return Respond::error('联系人未关联部门，无法设置为部门负责人');
        }
        
        try {
            DB::beginTransaction();
            
            // 如果要设为负责人，则取消同部门其他联系人的负责人状态
            if (!$contact->is_leader) {
                EnterpriseContact::where('enterprise_id', $enterpriseId)
                    ->where('department_id', $contact->department_id)
                    ->where('id', '!=', $id)
                    ->where('is_leader', 1)
                    ->update(['is_leader' => 0]);
            }
            
            // 切换状态
            $contact->is_leader = !$contact->is_leader;
            $contact->save();
            
            DB::commit();
            
            $status = $contact->is_leader ? '设置为' : '取消';
            return Respond::success($contact, "已{$status}部门负责人");
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('操作失败: ' . $e->getMessage());
        }
    }
} 