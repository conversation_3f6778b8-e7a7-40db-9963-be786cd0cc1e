<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>绑定管理员确认</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 16px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 40px auto;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 30px;
        }
        .btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .btn:hover:not(:disabled) {
            background-color: #40a9ff;
        }
        .message {
            margin-top: 16px;
            padding: 12px;
            border-radius: 4px;
        }
        .message.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .message.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(24, 144, 255, 0.3);
            border-radius: 50%;
            border-top-color: #1890ff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        #contentBox {
            display: none;
        }
        #loadingBox {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
<div class="container">
    <!-- 环境检测加载界面 -->
    <div id="loadingBox">
        <div class="loading"></div>
        <span>环境检测中，请稍候...</span>
    </div>

    <!-- 操作界面（初始隐藏） -->
    <div id="contentBox">
        <h1>绑定管理员确认</h1>
        <button id="bindButton" class="btn">确认绑定</button>
    </div>

    <div id="messageBox" style="display: none;" class="message"></div>
</div>

<script>
    // 获取URL参数
    function getQueryParam(name) {
        var urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // 环境检查
    function isCGAppWeb() {
        var ua = window.navigator.userAgent.toLowerCase();
        return ua.indexOf('changguan') !== -1;
    }

    // 显示消息
    function showMessage(text, type) {
        var messageBox = document.getElementById('messageBox');
        messageBox.textContent = text;
        messageBox.className = 'message ' + type;
        messageBox.style.display = 'block';
    }

    // 显示内容区域
    function showContent() {
        document.getElementById('loadingBox').style.display = 'none';
        document.getElementById('contentBox').style.display = 'block';
    }

    // CGApp就绪检查
    function CGAppWebReady() {
        return new Promise(function(resolve, reject) {
            if (isCGAppWeb()) {
                if (window.cgapp) {
                    resolve();
                } else {
                    var timeoutId = setTimeout(function() {
                        reject(new Error('常观App环境加载超时'));
                    }, 5000); // 5秒超时

                    document.addEventListener(
                        'cgappjsbridgeready',
                        function() {
                            clearTimeout(timeoutId);
                            resolve();
                        },
                        false
                    );
                }
            } else {
                reject(new Error('请在常观App内打开页面'));
            }
        });
    }

    // 绑定操作
    function handleBind() {
        var button = document.getElementById('bindButton');
        button.disabled = true;

        try {
            var qrCode = getQueryParam('qr_code');
            if (!qrCode) {
                throw new Error('缺少必要的二维码参数');
            }

            cgapp.systemRequest({
                client_id: 'default',
                method: 'POST',
                uri: "{{ url('/api/user/bind-admin-user') }}",
                params: {
                    qr_code: qrCode
                },
                success: function(res) {
                    if (res.statusCode === 200) {
                        showMessage('绑定成功！', 'success');
                    } else {
                        showMessage(res.data.errmsg || '绑定失败，请重试', 'error');
                        button.disabled = false;
                    }
                },
                fail: function(error) {
                    showMessage(error.errmsg || '请求失败，请重试', 'error');
                    button.disabled = false;
                }
            });
        } catch (error) {
            showMessage(error.message || '操作失败，请重试', 'error');
            button.disabled = false;
        }
    }

    // 页面初始化
    function initPage() {
        // 先进行环境检测
        CGAppWebReady()
            .then(function() {
                // 检测成功，显示操作界面
                showContent();

                // 检查URL参数
                var qrCode = getQueryParam('qr_code');
                if (!qrCode) {
                    showMessage('缺少必要的二维码参数', 'error');
                    document.getElementById('bindButton').disabled = true;
                }
            })
            .catch(function(error) {
                // 环境检测失败
                document.getElementById('loadingBox').style.display = 'none';
                showMessage(error.message || '环境检测失败', 'error');
            });

        // 初始化事件监听
        document.getElementById('bindButton').addEventListener('click', handleBind);
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', initPage);
</script>
</body>
</html>
