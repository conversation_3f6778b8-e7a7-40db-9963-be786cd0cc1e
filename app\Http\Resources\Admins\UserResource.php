<?php

namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class UserResource
 * @property-read \App\Models\User $resource
 * @mixin \App\Models\User
 */
class UserResource extends JsonResource
{
    public function toArray(Request $request) {
        return [
            'id'                        => $this->id,
            'uuid'                      => $this->uuid,
            'username'                  => $this->username,
            'nickname'                  => $this->nickname,
            'nickname_confirmed'        => $this->nickname_confirmed,
            'nickname_confirmed_txt'    => $this->nickname_confirmed_txt,
            'gender'                    => $this->gender,
            'gender_txt'                => $this->gender_txt,
            'status'                    => $this->status,
            'status_txt'                => $this->status_txt,
            'mobile'                    => $this->masked_sensitive_info['mobile'],
            'mobile_confirmed'          => $this->mobile_confirmed,
            'mobile_confirmed_txt'      => $this->mobile_confirmed_txt,
            'avatar'                    => $this->avatar,
            'display_avatar'            => $this->display_avatar,
            'avatar_confirmed'          => $this->avatar_confirmed,
            'avatar_confirmed_txt'      => $this->avatar_confirmed_txt,
            'birthday'                  => $this->birthday,
            'realname_auth_confirmed'   => $this->realname_auth_confirmed,
            'realname_auth_txt'         => $this->realname_auth_txt,
            'register_env_platform'     => $this->register_env_uni_platform,
            'register_env_platform_txt' => $this->register_env_platform_txt,
            'register_env_os'           => $this->register_env_os_name,
            'register_env_os_txt'       => $this->register_env_os_txt,
            'register_date'             => $this->register_date,
            'register_ip'               => $this->register_ip,
            'last_login_date'           => $this->last_login_date,
            'last_login_ip'             => $this->last_login_ip,
            'comment'                   => $this->comment,
            'comment_confirmed'         => $this->comment_confirmed,
            'comment_confirmed_txt'     => $this->comment_confirmed_txt,
            'created_at'                => $this->created_at,
            'updated_at'                => $this->updated_at,
            'realname_auth_name'        => $this->masked_sensitive_info['realname'],
            'realname_auth_identity'    => $this->masked_sensitive_info['identity'],
        ];
    }
}
