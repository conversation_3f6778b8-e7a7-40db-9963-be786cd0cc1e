<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->unsignedTinyInteger('is_workspace_client')->after('is_revoked')->default(0)->comment('是否为工作区客户端');
            $table->unsignedTinyInteger('show_in_workspace')->after('is_workspace_client')->default(0)->comment('是否在工作区展示');
            $table->string('workspace_redirect_url',1000)->after('show_in_workspace')->nullable()->comment('工作区重定向地址');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->dropColumn('is_workspace_client');
            $table->dropColumn('show_in_workspace');
            $table->dropColumn('workspace_redirect_url');
        });
    }
};
