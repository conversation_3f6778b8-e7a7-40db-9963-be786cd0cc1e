<?php

namespace App\Services\CloudFiles;

use App\Services\CloudFiles\Contracts\CloudFilesInterface;
use League\Flysystem\Config;
use League\Flysystem\DirectoryAttributes;
use League\Flysystem\FileAttributes;
use League\Flysystem\FilesystemAdapter;
use League\Flysystem\PathPrefixer;
use League\Flysystem\UnableToCheckDirectoryExistence;
use League\Flysystem\UnableToCheckFileExistence;
use League\Flysystem\UnableToCopyFile;
use League\Flysystem\UnableToCreateDirectory;
use League\Flysystem\UnableToDeleteDirectory;
use League\Flysystem\UnableToDeleteFile;
use League\Flysystem\UnableToMoveFile;
use League\Flysystem\UnableToReadFile;
use League\Flysystem\UnableToRetrieveMetadata;
use League\Flysystem\UnableToSetVisibility;
use League\Flysystem\UnableToWriteFile;

class CloudFilesAdapter implements FilesystemAdapter
{
    protected CloudFilesInterface $client;
    protected PathPrefixer $prefixer;
    protected ?string $cdnDomain;

    public function __construct(CloudFilesInterface $client, string $prefix = '', ?string $cdnDomain = null)
    {
        $this->client = $client;
        $this->prefixer = new PathPrefixer($prefix);
        $this->cdnDomain = $cdnDomain;
    }

    public function fileExists(string $path): bool
    {
        try {
            return $this->client->has($path);
        } catch (\Exception $e) {
            throw UnableToCheckFileExistence::forLocation($path, $e);
        }
    }

    public function directoryExists(string $path): bool
    {
        try {
            $contents = $this->client->listContents($path, false);
            return !empty($contents);
        } catch (\Exception $e) {
            throw UnableToCheckDirectoryExistence::forLocation($path, $e);
        }
    }

    public function write(string $path, string $contents, Config $config): void
    {
        try {
            $this->client->write($path, $contents, $config->get('options', []));
        } catch (\Exception $e) {
            throw UnableToWriteFile::atLocation($path, $e->getMessage(), $e);
        }
    }

    public function writeStream(string $path, $contents, Config $config): void
    {
        try {
            $this->client->writeStream($path, $contents, $config->get('options', []));
        } catch (\Exception $e) {
            throw UnableToWriteFile::atLocation($path, $e->getMessage(), $e);
        }
    }

    public function read(string $path): string
    {
        try {
            $response = $this->client->read($path);
            return $response['contents'] ?? '';
        } catch (\Exception $e) {
            throw UnableToReadFile::fromLocation($path, $e->getMessage(), $e);
        }
    }

    public function readStream(string $path)
    {
        try {
            $response = $this->client->readStream($path);
            return $response['stream'] ?? null;
        } catch (\Exception $e) {
            throw UnableToReadFile::fromLocation($path, $e->getMessage(), $e);
        }
    }

    public function delete(string $path): void
    {
        try {
            $this->client->delete($path);
        } catch (\Exception $e) {
            throw UnableToDeleteFile::atLocation($path, $e->getMessage(), $e);
        }
    }

    public function deleteDirectory(string $path): void
    {
        try {
            $this->client->deleteDir($path);
        } catch (\Exception $e) {
            throw UnableToDeleteDirectory::atLocation($path, $e->getMessage(), $e);
        }
    }

    public function createDirectory(string $path, Config $config): void
    {
        try {
            $this->client->createDir($path);
        } catch (\Exception $e) {
            throw UnableToCreateDirectory::atLocation($path, $e->getMessage(), $e);
        }
    }

    public function setVisibility(string $path, string $visibility): void
    {
        try {
            $this->client->setVisibility($path, $visibility);
        } catch (\Exception $e) {
            throw UnableToSetVisibility::atLocation($path, $e->getMessage(), $e);
        }
    }

    public function visibility(string $path): FileAttributes
    {
        try {
            $response = $this->client->getVisibility($path);
            return new FileAttributes($path, null, $response['visibility']);
        } catch (\Exception $e) {
            throw UnableToRetrieveMetadata::visibility($path, $e->getMessage(), $e);
        }
    }

    public function mimeType(string $path): FileAttributes
    {
        try {
            $metadata = $this->client->getMetadata($path);
            return new FileAttributes($path, null, null, null, $metadata['mimetype'] ?? null);
        } catch (\Exception $e) {
            throw UnableToRetrieveMetadata::mimeType($path, $e->getMessage(), $e);
        }
    }

    public function lastModified(string $path): FileAttributes
    {
        try {
            $metadata = $this->client->getMetadata($path);
            return new FileAttributes($path, null, null, $metadata['timestamp'] ?? null);
        } catch (\Exception $e) {
            throw UnableToRetrieveMetadata::lastModified($path, $e->getMessage(), $e);
        }
    }

    public function fileSize(string $path): FileAttributes
    {
        try {
            $metadata = $this->client->getMetadata($path);
            return new FileAttributes($path, $metadata['size'] ?? null);
        } catch (\Exception $e) {
            throw UnableToRetrieveMetadata::fileSize($path, $e->getMessage(), $e);
        }
    }

    public function listContents(string $path, bool $deep): iterable
    {
        try {
            $contents = $this->client->listContents($path, $deep);

            foreach ($contents as $entry) {
                if ($entry['type'] === 'dir') {
                    yield new DirectoryAttributes(
                        $entry['path'],
                        null,
                        $entry['timestamp'] ?? null
                    );
                } else {
                    yield new FileAttributes(
                        $entry['path'],
                        $entry['size'] ?? null,
                        $entry['visibility'] ?? null,
                        $entry['timestamp'] ?? null,
                        $entry['mimetype'] ?? null
                    );
                }
            }
        } catch (\Exception $e) {
            throw new \RuntimeException("Unable to list contents: " . $e->getMessage(), 0, $e);
        }
    }

    public function move(string $source, string $destination, Config $config): void
    {
        try {
            $this->client->rename($source, $destination);
        } catch (\Exception $e) {
            throw UnableToMoveFile::fromLocationTo($source, $destination, $e);
        }
    }

    public function copy(string $source, string $destination, Config $config): void
    {
        try {
            $this->client->copy($source, $destination);
        } catch (\Exception $e) {
            throw UnableToCopyFile::fromLocationTo($source, $destination, $e);
        }
    }

    /**
     * 获取文件的临时访问URL
     */
    public function getTemporaryUrl(string $path, int $expiration): string
    {
        return $this->client->signatureUrl($path, $expiration);
    }

    /**
     * 获取文件的访问URL
     */
    public function getUrl(string $path): string
    {
        if ($this->cdnDomain) {
            return rtrim($this->cdnDomain, '/') . '/' . ltrim($path, '/');
        }
        return $this->client->getUrl($path);
    }
}
