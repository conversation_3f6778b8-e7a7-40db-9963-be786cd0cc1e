<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->string('default_redirect_url', 1000)->after('callback_url')
                  ->nullable()
                  ->comment('默认跳转地址');
            $table->json('white_ips')->after('default_redirect_url')
                  ->nullable()
                  ->comment('白名单IP');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->dropColumn('default_redirect_url');
            $table->dropColumn('white_ips');
        });
    }
};
