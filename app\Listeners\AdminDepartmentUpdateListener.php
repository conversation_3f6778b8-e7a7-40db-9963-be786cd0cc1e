<?php

namespace App\Listeners;

use App\Enums\OAuthScopeEnum;
use App\Events\AdminDepartmentUpdateEvent;
use App\Models\Client;
use App\Utils\SignatureValidator;
use App\Utils\Tools;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class AdminDepartmentUpdateListener implements ShouldQueue
{
    use InteractsWithQueue;

    public $queue = 'account';

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(AdminDepartmentUpdateEvent $event): void
    {
        $adminDepartment = $event->getDepartment();

        $postData = [
            'event' => 'admin_department_modified',
            'action' => 'updated',
            'to_department' => $adminDepartment->id,
            'data' => [
                'id' => $adminDepartment->id,
                'name' => $adminDepartment->name,
                'code' => $adminDepartment->code,
                'parent_id' => $adminDepartment->parent_id,
                'sort' => $adminDepartment->sort,
                'status' => $adminDepartment->status,
            ],
        ];

        if ($adminDepartment->deleted_at) {
            $postData['action'] = 'deleted';
        }

        Client::where('is_revoked', Client::IS_REVOKED_NO)
            ->where('is_workspace_client', Client::IS_WORKSPACE_CLIENT_YES)
            ->get()
            ->each(function (Client $client) use ($postData) {
                if (!$client->hasAdminScope(OAuthScopeEnum::DEPARTMENTLIST->value)) {
                    return;
                }

                $signatureData = [
                    'client_id' => $client->client_key,
                    'timestamp' => time(),
                    'nonce'     => Str::random(),
                ] + $postData;

                $signatureValidator = new SignatureValidator($client->client_key, $client->client_access_secret);

                $signature = $signatureValidator->generateSignature($signatureData);

                if ($client->callback_url && filter_var($client->callback_url, FILTER_VALIDATE_URL)) {
                    try {
                        $res = Http::withHeaders([
                            Tools::HEADER_SIGN      => $signature,
                            Tools::HEADER_TIMESTAMP => $signatureData['timestamp'],
                            Tools::HEADER_NONCE     => $signatureData['nonce'],
                            Tools::HEADER_CLIENT_ID => $client->client_key,
                        ])
                            ->withOptions([
                                'verify' => false,
                                'curl'   => [
                                    CURLOPT_RESOLVE => [
                                        'uc.example.com:80:127.0.0.1',
                                        'kanchangzhou.example.com:80:127.0.0.1',
                                    ],
                                ],
                            ])
                            ->post($client->callback_url, $postData);
                    } catch (ConnectionException $e) {
                    }
                }
            });
    }
}
