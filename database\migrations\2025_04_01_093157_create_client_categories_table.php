<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('oauth_client_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('分类名称');
            $table->string('description')->nullable()->comment('分类描述');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态:1启用,0禁用');
            $table->unsignedTinyInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->unsignedBigInteger('category_id')->after('id')->nullable()->comment('分类ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oauth_client_categories');
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->dropColumn('category_id');
        });
    }
};
