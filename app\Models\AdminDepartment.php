<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property string $id
 * @property string $name 部门名称
 * @property string|null $phone 电话
 * @property string $code 部门编码
 * @property int $lft 左值
 * @property int $rgt 右值
 * @property int $level 层级
 * @property int $sort 排序值
 * @property int $status 状态:1启用,0禁用
 * @property string|null $remark 备注
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $parent_id 父级部门ID
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Client> $accessibleOauthClients
 * @property-read int|null $accessible_oauth_clients_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AdminUser> $admins
 * @property-read int|null $admins_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, AdminDepartment> $childDepartments
 * @property-read int|null $child_departments_count
 * @property-read \App\Models\Enterprise|null $enterprise
 * @property-read mixed $full_name
 * @property-read AdminDepartment|null $parentDepartment
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read mixed $status_text
 * @method static Builder<static>|AdminDepartment enabled()
 * @method static Builder<static>|AdminDepartment newModelQuery()
 * @method static Builder<static>|AdminDepartment newQuery()
 * @method static Builder<static>|AdminDepartment onlyTrashed()
 * @method static Builder<static>|AdminDepartment permission($permissions, $without = false)
 * @method static Builder<static>|AdminDepartment query()
 * @method static Builder<static>|AdminDepartment role($roles, $guard = null, $without = false)
 * @method static Builder<static>|AdminDepartment roots()
 * @method static Builder<static>|AdminDepartment whereCode($value)
 * @method static Builder<static>|AdminDepartment whereCreatedAt($value)
 * @method static Builder<static>|AdminDepartment whereDeletedAt($value)
 * @method static Builder<static>|AdminDepartment whereId($value)
 * @method static Builder<static>|AdminDepartment whereLevel($value)
 * @method static Builder<static>|AdminDepartment whereLft($value)
 * @method static Builder<static>|AdminDepartment whereName($value)
 * @method static Builder<static>|AdminDepartment whereParentId($value)
 * @method static Builder<static>|AdminDepartment wherePhone($value)
 * @method static Builder<static>|AdminDepartment whereRemark($value)
 * @method static Builder<static>|AdminDepartment whereRgt($value)
 * @method static Builder<static>|AdminDepartment whereSort($value)
 * @method static Builder<static>|AdminDepartment whereStatus($value)
 * @method static Builder<static>|AdminDepartment whereUpdatedAt($value)
 * @method static Builder<static>|AdminDepartment withTrashed()
 * @method static Builder<static>|AdminDepartment withoutPermission($permissions)
 * @method static Builder<static>|AdminDepartment withoutRole($roles, $guard = null)
 * @method static Builder<static>|AdminDepartment withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperAdminDepartment
 */
class AdminDepartment extends Model
{
    use SoftDeletes, HasFactory;

    use HasRoles {
        HasRoles::syncPermissions as bootSyncPermissions;
        HasRoles::syncRoles as bootSyncRoles;
    }

    protected $guard_name = 'admin';

    protected $keyType = 'string';
    public $incrementing = true;

    protected $fillable = [
        'name', // 部门名称
        'parent_id', // 父级部门ID
        'code', // 部门编码
        'lft', // 左值
        'rgt', // 右值
        'level', // 层级
        'sort', // 排序
        'status', // 状态
        'remark', // 备注
        'enterprise_id', // 所属企业ID
    ];

    protected $casts = [];

    protected $appends = ['full_name'];

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    public static array $statusMap = [
        self::STATUS_ENABLED  => '启用',
        self::STATUS_DISABLED => '禁用',
    ];

    const IS_LEADER_YES = 1;
    const IS_LEADER_NO = 0;

    public static array $isLeaderMap = [
        self::IS_LEADER_YES => '是',
        self::IS_LEADER_NO  => '否',
    ];


    /**
     * 启动方法
     */
    protected static function boot()
    {
        parent::boot();

        // 默认按左值排序，确保树形结构的正确顺序
        static::addGlobalScope('order', function (Builder $builder) {
            $builder->orderBy('lft');
        });
    }

    /*
    |--------------------------------------------------------------------------
    | 关联关系
    |--------------------------------------------------------------------------
    */

    /**
     * 部门与管理员的多对多关联
     */
    public function admins()
    {
        return $this->belongsToMany(AdminUser::class, 'admin_department_admin_user')
            ->withPivot([
                'is_leader',
                'job_title',
                'sort',
            ])
            ->orderByPivot('sort')
            ->withTimestamps();
    }

    /**
     * 部门领导关联
     */
    public function leaders()
    {
        return $this->admins()
            ->wherePivot('is_leader', 1)
            ->orderByPivot('sort');
    }

    /**
     * 普通成员关联
     */
    public function members()
    {
        return $this->admins()
            ->wherePivot('is_leader', 0)
            ->orderByPivot('sort');
    }

    /**
     * 企业关联
     */
    public function enterprise()
    {
        return $this->belongsTo(Enterprise::class, 'enterprise_id', 'id');
    }

    public function can($permission): bool
    {
        if ($this->checkPermissionTo($permission)) {
            return true;
        }

        $parent = $this->parent();

        if ($parent) {
            return $parent->can($permission);
        }

        return false;
    }

    /**
     * @param ...$permissions
     *
     * @return void
     * @throws \Throwable
     */
    public function syncPermissions(...$permissions)
    {
        $this->bootSyncPermissions($permissions);

        $this->descendants()
            ->get()
            ->each(function ($department) {
                $department->forgetCachedPermissions();
            });
    }

    /**
     * @param ...$roles
     *
     * @return void
     * @throws \Throwable
     */
    public function syncRoles(...$roles)
    {
        $this->bootSyncRoles($roles);

        $this->descendants()
            ->get()
            ->each(function ($department) {
                $department->forgetCachedPermissions();
            });
    }

    /**
     * 获取部门的所有权限（包括继承的权限）
     * 使用缓存优化性能
     */
    //    public function getAllPermissions() {
    //        $cacheKey = "department_permissions:{$this->id}";
    //
    //        return cache()->remember($cacheKey, now()->addHours(24), function () {
    //            // 获取直接权限和角色权限
    //            $permissions = $this->permissions->merge($this->roles->flatMap(fn($role) => $role->permissions));
    //
    //            // 如果没有直接权限和角色，尝试从父部门继承
    //            if ($permissions->isEmpty()) {
    //                $parent = $this->parent();
    //                if ($parent) {
    //                    return $parent->getAllPermissions();
    //                }
    //            }
    //
    //            return $permissions->unique('id');
    //        });
    //    }

    /**
     * 清除部门及其子部门的权限缓存
     */
    //    public function clearPermissionCache() {
    //        // 清除自身缓存
    //        cache()->forget("department_permissions:{$this->id}");
    //
    //        // 清除子部门缓存
    //        $this->descendants()
    //             ->each(function ($department) {
    //                 cache()->forget("department_permissions:{$department->id}");
    //             });
    //    }

    /**
     * 同步部门权限，并清除相关缓存
     */
    //    public function syncPermissionsWithCache($permissions) {
    //        \DB::transaction(function () use ($permissions) {
    //            $this->syncPermissions($permissions);
    //            $this->clearPermissionCache();
    //
    //            // 清除部门下所有用户的权限缓存
    //            $this->admins->each(function ($admin) {
    //                cache()->forget("user_permissions:{$admin->uuid}");
    //            });
    //        });
    //    }

    /*
    |--------------------------------------------------------------------------
    | 访问器和修改器
    |--------------------------------------------------------------------------
    */

    /**
     * 状态文本
     */
    protected function statusText(): Attribute
    {
        return Attribute::make(get: fn() => self::$statusMap[$this->status] ?? '未知');
    }

    /**
     * 获取完整部门名称，包含所有上级部门
     * 基于嵌套集模型高效查询
     */
    protected function fullName(): Attribute
    {
        return Attribute::make(get: function () {
            // 使用左右值查询所有上级部门
            $ancestors = self::where('lft', '<=', $this->lft)
                ->where('rgt', '>=', $this->rgt)
                ->orderBy('level')
                ->pluck('name');

            return $ancestors->implode(' / ');
        });
    }

    /*
    |--------------------------------------------------------------------------
    | 树形结构方法
    |--------------------------------------------------------------------------
    */

    /**
     * 获取整个树的根节点
     */
    public function getRoot()
    {
        return $this->ancestors()
            ->oldest('lft')
            ->first() ?? $this;
    }

    /**
     * 获取所有祖先节点（不包括自己）
     */
    public function ancestors()
    {
        return self::where('lft', '<', $this->lft)
            ->where('rgt', '>', $this->rgt)
            ->orderBy('level');
    }

    /**
     * 获取所有子孙节点
     */
    public function descendants()
    {
        return self::where('lft', '>', $this->lft)
            ->where('rgt', '<', $this->rgt)
            ->orderBy('lft');
    }

    /**
     * 获取直接子节点
     */
    public function children()
    {
        return self::where('lft', '>', $this->lft)
            ->where('rgt', '<', $this->rgt)
            ->where('level', $this->level + 1)
            ->orderBy('lft');
    }

    public function parentDepartment(): BelongsTo
    {
        return $this->belongsTo(AdminDepartment::class, 'parent_id');
    }

    public function childDepartments()
    {
        return $this->hasMany(AdminDepartment::class, 'parent_id');
    }

    /**
     * 获取直接父节点
     */
    public function parent()
    {
        if ($this->level === 0) {
            return null;
        }

        return self::where('lft', '<', $this->lft)
            ->where('rgt', '>', $this->rgt)
            ->where('level', $this->level - 1)
            ->first();
    }

    /**
     * 获取同级节点（不包括自己）
     */
    public function siblings()
    {
        $parent = $this->parent();

        if (!$parent) {
            return self::where('level', 0)
                ->where('id', '!=', $this->id);
        }

        return self::where('lft', '>', $parent->lft)
            ->where('rgt', '<', $parent->rgt)
            ->where('level', $this->level)
            ->where('id', '!=', $this->id);
    }

    /*
    |--------------------------------------------------------------------------
    | 查询作用域
    |--------------------------------------------------------------------------
    */

    /**
     * 启用状态的部门
     */
    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 根节点
     */
    public function scopeRoots(Builder $query): Builder
    {
        return $query->where('level', 0);
    }

    /*
    |--------------------------------------------------------------------------
    | 业务方法
    |--------------------------------------------------------------------------
    */

    /**
     * 检查是否是根节点
     */
    public function isRoot(): bool
    {
        return $this->level === 0;
    }

    /**
     * 检查是否是叶子节点
     */
    public function isLeaf(): bool
    {
        return $this->rgt - $this->lft === 1;
    }

    /**
     * 检查是否有子节点
     */
    public function hasChildren(): bool
    {
        return !$this->isLeaf();
    }

    /**
     * 获取节点深度
     */
    public function getDepth(): int
    {
        return $this->level;
    }

    /**
     * 获取子节点数量
     */
    public function getDescendantCount(): int
    {
        return ($this->rgt - $this->lft - 1) / 2;
    }


    /**
     * 验证是否可以将部门移动到指定父级
     *
     * @param int|null $parentId
     *
     * @return bool
     */
    public function canMoveTo($parentId): bool
    {
        if (!$parentId) {
            return true;
        }

        try {
            $newParent = self::findOrFail($parentId);

            return !($newParent->id === $this->id || ($newParent->lft > $this->lft && $newParent->rgt < $this->rgt));
        } catch (\Exception $e) {
            return false;
        }
    }

    // 添加到 AdminDepartment 模型
    // 可访问的OAuth客户端多态关联
    public function accessibleOauthClients()
    {
        return $this->morphToMany(Client::class, 'accessible', 'oauth_client_accessibles', 'accessible_id', 'oauth_client_id')
            ->where('is_workspace_client', Client::IS_WORKSPACE_CLIENT_YES)
            ->withTimestamps();
    }

    // 获取部门及其所有上级部门可访问的OAuth客户端
    public function getAllAccessibleOauthClients()
    {
        // 获取当前部门直接可访问的客户端
        $directClients = $this->accessibleOauthClients;

        // 获取父部门
        $parent = $this->parent();
        if (!$parent) {
            return $directClients;
        }

        // 递归获取父部门的可访问客户端
        $parentClients = $parent->getAllAccessibleOauthClients();

        // 合并去重
        return $directClients->merge($parentClients)
            ->unique('id');
    }

    // 检查部门是否可以访问指定的OAuth客户端
    public function canAccessOauthClient($clientId): bool
    {
        // 检查当前部门是否可访问
        if ($this->accessibleOauthClients()
            ->where('id', $clientId)
            ->exists()
        ) {
            return true;
        }

        // 检查上级部门是否可访问
        $parent = $this->parent();
        if ($parent) {
            return $parent->canAccessOauthClient($clientId);
        }

        return false;
    }

    public function getAllAccessibleOauthClientsWithCache()
    {
        $cacheKey = "department_accessible_clients:{$this->id}";

        return cache()->remember($cacheKey, now()->addHours(6), function () {
            return $this->getAllAccessibleOauthClients();
        });
    }

    public function clearAccessibleOauthClientsCache()
    {
        cache()->forget("department_accessible_clients:{$this->id}");

        // 清除子部门及其用户的缓存
        $this->descendants()->get()->each(function ($dept) {
            cache()->forget("department_accessible_clients:{$dept->id}");
            $dept->admins->each(function ($admin) {
                cache()->forget("admin_accessible_clients:{$admin->uuid}");
            });
        });
    }
}
