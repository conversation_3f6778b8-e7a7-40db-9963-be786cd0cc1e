{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "alibabacloud/captcha-20230305": "^1.1", "alibabacloud/cloudauth-20190307": "3.1.0", "alibabacloud/dypnsapi-20170525": "^1.2", "alibabacloud/mpaas-20201028": "^1.3", "aliyuncs/oss-sdk-php": "^2.7", "griffinledingham/php-apple-signin": "^1.1", "hedeqiang/umeng": "^2.1", "kalnoy/nestedset": "^6.0", "laravel/framework": "^11.9", "laravel/passport": "^12.3", "laravel/tinker": "^2.9", "lpilp/guomi": "^2.0", "maatwebsite/excel": "^3.1", "medz/id-card-of-china": "^1.1", "obs/esdk-obs-php": "^3.24", "overtrue/laravel-easy-sms": "^2.2", "overtrue/laravel-pinyin": "^5.2", "overtrue/laravel-socialite": "^4.1", "predis/predis": "^2.2", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-permission": "^6.10", "tymon/jwt-auth": "^2.1"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}