<?php

namespace App\Services\CloudFiles\Providers;

use App\Services\CloudFiles\Exceptions\CloudFilesException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use OSS\Core\OssException;
use OSS\Http\RequestCore_Exception;
use OSS\OssClient;
use Throwable;

/**
 * 阿里云OSS存储服务实现类
 */
class Oss extends AbstractCloudFiles
{
    /**
     * OSS客户端实例
     * @var OssClient
     */
    protected $client;

    /**
     * 结果映射关系
     * @var array
     */
    protected static $resultMap = [
        'Body'           => 'raw_contents',
        'Content-Length' => 'size',
        'ContentType'    => 'mimetype',
        'Size'           => 'size',
        'StorageClass'   => 'storage_class',
        'LastModified'   => 'timestamp',
        'ETag'           => 'etag',
    ];

    /**
     * 默认上传选项
     * @var array
     */
    protected $defaultOptions = [
        OssClient::OSS_CHECK_MD5 => true,
        OssClient::OSS_PART_SIZE => 10 * 1024 * 1024, // 每个分片10MB
    ];


    /**
     * 获取外部访问的域名
     * 优先使用 CDN 域名，处理 SSL 配置
     *
     * @return string
     */
    protected function getAccessDomain(): string {
        $ssl = $this->getConfig('ssl', false);
        $protocol = $ssl ? 'https://' : 'http://';

        // 优先使用CDN域名
        $cdnDomain = $this->getConfig('cdndomain');
        if ($cdnDomain) {
            return $protocol . rtrim($cdnDomain, '/');
        }

        // 如果没有CDN域名，使用bucket + endpoint组合
        $bucket = $this->getConfig('bucket');
        $endpoint = $this->getConfig('endpoint');

        // 移除endpoint中可能存在的协议前缀
        $endpoint = preg_replace('#^https?://#', '', $endpoint);

        return $protocol . $bucket . '.' . rtrim($endpoint, '/');
    }

    /**
     * 初始化OSS客户端
     * 包含重试机制，最多重试3次
     *
     * @return $this
     * @throws CloudFilesException
     */
    protected function initializeClient() {
        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                // 优先使用内部endpoint，如果没有则使用普通endpoint
                $endpoint = $this->getConfig('internal_endpoint') ?: $this->getConfig('endpoint');

                // 移除endpoint中可能存在的协议前缀
                $endpoint = preg_replace('#^https?://#', '', $endpoint);

                $this->client = new OssClient(
                    $this->getConfig('access_key'),
                    $this->getConfig('secret_key'),
                    $endpoint,
                    false // 不使用cname
                );

                // 设置默认超时
                $this->client->setTimeout(30);
                $this->client->setConnectTimeout(10);

                if ($this->debug) {
                    $this->debug('OSS客户端初始化成功', [
                        'endpoint' => $endpoint,
                        'bucket' => $this->getConfig('bucket'),
                        'retry' => $retryCount,
                        'using_internal' => !empty($this->getConfig('internal_endpoint'))
                    ]);
                }

                return $this;
            } catch (OssException $e) {
                $retryCount++;
                if ($retryCount >= $maxRetries) {
                    $this->handleException($e, '初始化客户端');
                }
                sleep(1); // 重试前等待
            }
        }

        throw new CloudFilesException('OSS客户端初始化失败，已重试' . $maxRetries . '次');
    }

    /**
     * 获取上传策略
     * 用于客户端直传场景
     *
     * @param string $callbackRoute 回调路由
     * @param string $dir 上传目录
     * @param int $expire 过期时间(秒)
     * @param int $contentLengthRange 内容长度范围限制
     *
     * @return array
     */
    public function policy(string $callbackRoute = '', string $dir = '', int $expire = 300, int $contentLengthRange = 1048576000): array {
        $dir = ltrim($dir, '/');
        $host = $this->getAccessDomain();

        $conditions = [
            ['bucket' => $this->getConfig('bucket')],
            [
                'starts-with',
                '$key',
                $dir,
            ],
            [
                'content-length-range',
                0,
                $contentLengthRange,
            ],
        ];

        if ($callbackRoute) {
            $callback = [
                'callbackUrl'      => $callbackRoute,
                'callbackBody'     => 'filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}&format=${imageInfo.format}',
                'callbackBodyType' => 'application/x-www-form-urlencoded',
            ];
            $conditions[] = ['callback' => base64_encode(json_encode($callback))];
        }

        $policy = base64_encode(json_encode([
            'expiration' => gmdate('Y-m-d\TH:i:s\Z', time() + $expire),
            'conditions' => $conditions,
        ]));

        $signature = base64_encode(hash_hmac('sha1', $policy, $this->getConfig('secret_key'), true));

        return [
            'policy'         => $policy,
            'signature'      => $signature,
            'callback'       => $callbackRoute ? base64_encode(json_encode($callback)) : '',
            'OSSAccessKeyId' => $this->getConfig('access_key'),
            'host'           => $host,
            'dir'            => $dir,
            'expire'         => $expire,
        ];
    }

    /**
     * 验证上传回调
     * 用于验证OSS回调请求的合法性
     *
     * @return bool
     * @throws CloudFilesException
     */
    public function verify(): bool {
        $authorization = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        $pubKeyUrl = $_SERVER['HTTP_X_OSS_PUB_KEY_URL'] ?? '';

        if (!$authorization || !$pubKeyUrl) {
            throw new CloudFilesException('缺少OSS回调参数');
        }

        // 获取公钥
        $pubKey = '';
        try {
            $client = new Client();
            $pubKey = $client->get(base64_decode($pubKeyUrl))
                             ->getBody()
                             ->getContents();
        } catch (GuzzleException $e) {
            throw new CloudFilesException('无法获取OSS公钥');
        }

        if (!$pubKey) {
            throw new CloudFilesException('无法获取OSS公钥');
        }

        // 构建签名字符串
        $path = urldecode($_SERVER['REQUEST_URI']);
        $stringToSign = $path . "\n" . file_get_contents('php://input');

        // 验证签名
        return openssl_verify($stringToSign, base64_decode($authorization), $pubKey, OPENSSL_ALGO_MD5) === 1;
    }

    /**
     * 分片上传文件
     * 支持进度回调和自动重试
     *
     * @param string $path 目标路径
     * @param string $filePath 本地文件路径
     * @param array $options 上传选项
     *
     * @return string|bool 成功返回文件URL，失败返回false
     * @throws RequestCore_Exception
     */
    public function multiUpload(string $path, string $filePath, array $options = []): bool|string {
        $options = array_merge($this->defaultOptions, $options);
        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                // 添加上传进度回调
                if ($this->debug) {
                    $options[OssClient::OSS_CALLBACK] = function ($uploaded, $total) use ($path) {
                        $this->debug('上传进度', [
                            'path'     => $path,
                            'uploaded' => $uploaded,
                            'total'    => $total,
                            'progress' => round(($uploaded / $total) * 100, 2) . '%',
                        ]);
                    };
                }

                $result = $this->client->multiuploadFile($this->getConfig('bucket'), $this->removePathPrefix($path), $filePath, $options);

                return $result['oss-request-url'];
            } catch (OssException $e) {
                $retryCount++;
                if ($retryCount >= $maxRetries) {
                    $this->debug('分片上传失败', [
                        'path'    => $path,
                        'error'   => $e->getMessage(),
                        'retries' => $retryCount,
                    ]);

                    return false;
                }
                sleep(1);
            }
        }

        return false;
    }

    /**
     * 写入文件内容
     * 支持字符串和资源类型的内容
     *
     * @param string $path 目标路径
     * @param string|resource $contents 文件内容
     * @param array|null $config 配置选项
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function write(string $path, $contents, ?array $config = null): bool {
        // 处理资源类型内容
        if (is_resource($contents)) {
            $contents = stream_get_contents($contents);
        }

        $config = $config ?? [];

        // 自动检测内容类型
        if (!isset($config[OssClient::OSS_CONTENT_TYPE])) {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            $mimeType = $extension ? mime_content_type('test.' . $extension) : 'application/octet-stream';
            $config[OssClient::OSS_CONTENT_TYPE] = $mimeType;
        }

        try {
            $this->client->putObject($this->getConfig('bucket'), $this->removePathPrefix($path), $contents, $config);

            $this->debug('文件写入成功', [
                'path'      => $path,
                'size'      => strlen($contents),
                'mime_type' => $config[OssClient::OSS_CONTENT_TYPE],
            ]);

            return true;
        } catch (OssException $e) {
            $this->handleException($e, '写入文件');
        }
    }

    /**
     * 删除文件
     * 支持单个文件和批量删除
     *
     * @param string|array $path 文件路径或路径数组
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function delete($path): bool {
        try {
            $bucket = $this->getConfig('bucket');

            if (is_array($path)) {
                // 分批处理大量文件
                $chunks = array_chunk($path, 100);
                foreach ($chunks as $chunk) {
                    $this->client->deleteObjects($bucket, $chunk);
                }
            } else {
                $this->client->deleteObject($bucket, $this->removePathPrefix($path));
            }

            $this->debug('删除操作完成', [
                'path'     => $path,
                'is_batch' => is_array($path),
            ]);

            return true;
        } catch (OssException $e) {
            $this->handleException($e, '删除文件');
        }
    }

    /**
     * 获取文件URL
     * 支持CDN域名和SSL
     *
     * @param string $path 文件路径
     *
     * @return string
     */
    public function getUrl(string $path): string {
        return $this->getAccessDomain() . '/' . $this->removePathPrefix($path);
    }

    /**
     * 获取文件临时访问URL
     *
     * @param string $path 文件路径
     * @param int $expire 过期时间(秒)
     * @param array $options 其他选项
     *
     * @return string
     * @throws CloudFilesException
     */
    /**
     * 获取文件临时访问URL
     * 支持CDN域名和SSL配置
     *
     * @param string $path 文件路径
     * @param int $expire 过期时间(秒)
     * @param array $options 其他选项
     *
     * @return string
     * @throws CloudFilesException
     */
    public function signatureUrl(string $path, int $expire = 3600, array $options = []): string {
        try {
            // 获取原始的签名URL
            $signedUrl = $this->client->signUrl(
                $this->getConfig('bucket'),
                $this->removePathPrefix($path),
                $expire,
                'GET',
                array_merge($this->defaultOptions, $options)
            );

            // 如果使用CDN域名，需要替换域名部分
            $cdnDomain = $this->getConfig('cdndomain');
            if ($cdnDomain) {
                // 解析原始签名URL
                $urlParts = parse_url($signedUrl);

                // 构建新的host
                $protocol = $this->getConfig('ssl', false) ? 'https://' : 'http://';
                $newHost = $protocol . rtrim($cdnDomain, '/');

                // 重构URL
                $newUrl = $newHost . $urlParts['path'] . '?' . $urlParts['query'];

                if ($this->debug) {
                    $this->debug('签名URL域名替换', [
                        'original_url' => $signedUrl,
                        'new_url' => $newUrl,
                        'using_cdn' => true
                    ]);
                }

                return $newUrl;
            }

            if ($this->debug) {
                $this->debug('生成签名URL', [
                    'path' => $path,
                    'expire' => $expire,
                    'url' => $signedUrl,
                    'using_cdn' => false
                ]);
            }

            return $signedUrl;
        } catch (OssException $e) {
            $this->handleException($e, '生成签名URL');
        }
    }

    /**
     * Base64写入文件
     * 支持处理Base64编码的图片数据
     *
     * @param string $path 目标路径
     * @param string $base64Data Base64编码的数据
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function base64(string $path, string $base64Data): bool {
        if (preg_match('/^data:\s*image\/(\w+);base64,/', $base64Data, $matches)) {
            $contents = base64_decode(substr($base64Data, strpos($base64Data, ',') + 1));

            return $this->write($path, $contents);
        }

        throw new CloudFilesException('无效的Base64图片数据');
    }

    /**
     * 获取文件元数据
     *
     * @param string $path 文件路径
     *
     * @return array
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function getMetadata(string $path): array {
        try {
            $objectMeta = $this->client->getObjectMeta($this->getConfig('bucket'), $this->removePathPrefix($path));

            return $this->normalizeResponse($objectMeta, $path);
        } catch (OssException $e) {
            $this->handleException($e, '获取文件元数据');
        }
    }

    /**
     * 使用流写入文件
     *
     * @param string $path 目标路径
     * @param resource $resource 文件资源
     * @param array|null $config 配置选项
     *
     * @return bool
     * @throws CloudFilesException
     * @throws RequestCore_Exception
     */
    public function writeStream(string $path, $resource, ?array $config = null): bool {
        return $this->write($path, stream_get_contents($resource), $config);
    }

    /**
     * 写入本地文件
     *
     * @param string $path 目标路径
     * @param string $filePath 本地文件路径
     * @param array|null $config 配置选项
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function writeFile(string $path, string $filePath, ?array $config = null): bool {
        try {
            $this->client->uploadFile($this->getConfig('bucket'), $this->removePathPrefix($path), $filePath, $config ?? []);

            $this->debug('本地文件上传成功', [
                'path'       => $path,
                'local_path' => $filePath,
            ]);

            return true;
        } catch (OssException $e) {
            $this->handleException($e, '上传本地文件');
        }
    }

    /**
     * 读取文件内容
     *
     * @param string $path 文件路径
     *
     * @return array
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function read(string $path): array {
        try {
            $result = $this->client->getObject($this->getConfig('bucket'), $this->removePathPrefix($path));

            return $this->normalizeResponse([
                'contents' => $result,
                'path'     => $path,
                'type'     => 'file',
            ], $path);
        } catch (OssException $e) {
            $this->handleException($e, '读取文件');
        }
    }

    /**
     * 以流形式读取文件
     *
     * @param string $path 文件路径
     *
     * @return array
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function readStream(string $path): array {
        $result = $this->read($path);
        $stream = fopen('php://temp', 'r+');
        fwrite($stream, $result['contents']);
        rewind($stream);

        $result['stream'] = $stream;
        unset($result['contents']);

        return $result;
    }

    /**
     * 列出目录内容
     *
     * @param string $dirname 目录名
     * @param bool $recursive 是否递归
     *
     * @return array
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function listContents(string $dirname = '', bool $recursive = false): array {
        try {
            $dirname = rtrim($dirname, '/') . '/';

            $options = [
                'delimiter' => $recursive ? '' : '/',
                'prefix'    => $dirname,
                'max-keys'  => 1000,
                'marker'    => '',
            ];

            $contents = [];
            $listObjectInfo = $this->client->listObjects($this->getConfig('bucket'), $options);

            foreach ($listObjectInfo->getObjectList() as $object) {
                $contents[] = $this->normalizeResponse([
                    'Key'          => $object->getKey(),
                    'LastModified' => $object->getLastModified(),
                    'ETag'         => $object->getETag(),
                    'Size'         => $object->getSize(),
                    'StorageClass' => $object->getStorageClass(),
                    'type'         => 'file',
                ], $object->getKey());
            }

            if (!$recursive) {
                foreach ($listObjectInfo->getPrefixList() as $prefix) {
                    $contents[] = [
                        'path' => $prefix->getPrefix(),
                        'type' => 'dir',
                    ];
                }
            }

            return array_filter($contents, function ($item) {
                return !empty($item['path']);
            });

        } catch (OssException $e) {
            $this->handleException($e, '列出目录内容');
        }
    }

    /**
     * 判断文件是否存在
     *
     * @param string $path 文件路径
     *
     * @return bool
     * @throws RequestCore_Exception
     */
    public function has(string $path): bool {
        try {
            return $this->client->doesObjectExist($this->getConfig('bucket'), $this->removePathPrefix($path));
        } catch (OssException $e) {
            return false;
        }
    }

    /**
     * 获取文件路径
     *
     * @param string $path
     *
     * @return string
     */
    public function getPath(string $path): string {
        return $this->removePathPrefix($path);
    }

    /**
     * 复制文件
     *
     * @param string $path
     * @param string $newPath
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function copy(string $path, string $newPath): bool {
        try {
            $this->client->copyObject($this->getConfig('bucket'), $this->removePathPrefix($path), $this->getConfig('bucket'), $this->removePathPrefix($newPath));

            return true;
        } catch (OssException $e) {
            $this->handleException($e, '复制文件');
        }
    }

    /**
     * 移动/重命名文件
     *
     * @param string $path
     * @param string $newPath
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function rename(string $path, string $newPath): bool {
        if (!$this->copy($path, $newPath)) {
            return false;
        }

        return $this->delete($path);
    }

    /**
     * 删除目录
     *
     * @param string $dirname
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function deleteDir(string $dirname): bool {
        try {
            $dirname = rtrim($this->removePathPrefix($dirname), '/') . '/';

            // 列出目录下的所有文件
            $contents = $this->listContents($dirname, true);

            if (!empty($contents)) {
                $files = array_map(function ($item) {
                    return $item['path'];
                }, $contents);

                // 分批删除文件
                foreach (array_chunk($files, 100) as $chunk) {
                    $this->delete($chunk);
                }
            }

            // 删除目录标记
            $this->client->deleteObject($this->getConfig('bucket'), $dirname);

            return true;
        } catch (OssException $e) {
            $this->handleException($e, '删除目录');
        }
    }

    /**
     * 创建目录
     *
     * @param string $dirname
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function createDir(string $dirname): bool {
        try {
            $dirname = rtrim($this->removePathPrefix($dirname), '/') . '/';
            $this->client->createObjectDir($this->getConfig('bucket'), $dirname);

            return true;
        } catch (OssException $e) {
            $this->handleException($e, '创建目录');
        }
    }

    /**
     * 设置文件访问权限
     *
     * @param string $path
     * @param string $visibility
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function setVisibility(string $path, string $visibility): bool {
        try {
            $this->client->putObjectAcl($this->getConfig('bucket'), $this->removePathPrefix($path), $this->normalizeVisibility($visibility));

            return true;
        } catch (OssException $e) {
            $this->handleException($e, '设置访问权限');
        }
    }

    /**
     * 获取文件访问权限
     *
     * @param string $path
     *
     * @return array
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function getVisibility(string $path): array {
        try {
            $acl = $this->client->getObjectAcl($this->getConfig('bucket'), $this->removePathPrefix($path));

            return [
                'visibility' => $this->normalizeVisibilityResult($acl),
            ];
        } catch (OssException $e) {
            $this->handleException($e, '获取访问权限');
        }
    }

    /**
     * 格式化访问权限
     *
     * @param string $visibility
     *
     * @return string
     */
    protected function normalizeVisibility(string $visibility): string {
        return match ($visibility) {
            'public' => OssClient::OSS_ACL_TYPE_PUBLIC_READ,
            default => OssClient::OSS_ACL_TYPE_PRIVATE,
        };
    }

    /**
     * 格式化访问权限结果
     *
     * @param string $visibility
     *
     * @return string
     */
    protected function normalizeVisibilityResult(string $visibility): string {
        return match ($visibility) {
            OssClient::OSS_ACL_TYPE_PUBLIC_READ, OssClient::OSS_ACL_TYPE_PUBLIC_READ_WRITE => 'public',
            default => 'private',
        };
    }

    /**
     * 创建软链接
     * OSS的符号链接实现
     *
     * @param string $path 软链接路径
     * @param string $target 目标文件
     *
     * @return bool
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function createSymlink(string $path, string $target): bool {
        try {
            $this->client->putSymlink($this->getConfig('bucket'), $this->removePathPrefix($path), $this->removePathPrefix($target));

            $this->debug('创建软链接成功', [
                'symlink' => $path,
                'target'  => $target,
            ]);

            return true;
        } catch (OssException $e) {
            $this->handleException($e, '创建软链接');
        }
    }

    /**
     * 读取软链接
     *
     * @param string $path 软链接路径
     *
     * @return string|null
     * @throws CloudFilesException|RequestCore_Exception
     */
    public function readSymlink(string $path): ?string {
        try {
            $response = $this->client->getSymlink($this->getConfig('bucket'), $this->removePathPrefix($path));

            if (isset($response[OssClient::OSS_SYMLINK_TARGET])) {
                return $response[OssClient::OSS_SYMLINK_TARGET];
            }

            return null;
        } catch (OssException $e) {
            // 如果是符号链接不存在的错误，返回null
            if (str_contains($e->getMessage(), 'NoSuchKey')) {
                return null;
            }
            $this->handleException($e, '读取软链接');
        }
    }

    /**
     * 检查是否是软链接
     *
     * @param string $path 文件路径
     *
     * @return bool
     * @throws RequestCore_Exception
     */
    public function isSymlink(string $path): bool {
        try {
            $this->readSymlink($path);

            return true;
        } catch (CloudFilesException $e) {
            return false;
        }
    }

    /**
     * 异常处理
     * 记录详细的错误信息和上下文
     *
     * @param Throwable $e 异常对象
     * @param string $action 操作名称
     *
     * @throws CloudFilesException
     */
    protected function handleException(Throwable $e, string $action): void {
        $context = [
            'exception' => get_class($e),
            'file'      => $e->getFile(),
            'line'      => $e->getLine(),
            'code'      => $e->getCode(),
            'bucket'    => $this->getConfig('bucket'),
            'endpoint'  => $this->getOperationEndpoint(),
        ];

        if ($e instanceof OssException) {
            $context['request_id'] = $e->getRequestId();
        }

        $message = sprintf('[%s] %s失败: %s', get_class($this), $action, $e->getMessage());

        if ($this->debug) {
            $this->debug($message, $context);
        }

        throw new CloudFilesException($message, $e->getCode(), $e);
    }

    /**
     * 更新文件内容
     *
     * @param string $path
     * @param mixed $contents
     * @param array|null $config
     *
     * @return bool
     * @throws CloudFilesException
     * @throws RequestCore_Exception
     */
    public function update(string $path, $contents, ?array $config = null): bool {
        return $this->write($path, $contents, $config);
    }

    /**
     * 使用流更新文件内容
     *
     * @param string $path
     * @param resource $resource
     * @param array|null $config
     *
     * @return bool
     * @throws CloudFilesException
     * @throws RequestCore_Exception
     */
    public function updateStream(string $path, $resource, ?array $config = null): bool {
        return $this->writeStream($path, $resource, $config);
    }
}
