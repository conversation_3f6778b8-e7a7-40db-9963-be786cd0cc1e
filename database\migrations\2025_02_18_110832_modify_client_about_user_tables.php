<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('oauth_access_tokens', function (Blueprint $table) {
            $table->string('user_type')
                  ->after('user_uuid')
                  ->nullable()
                  ->default('user')
                  ->comment('用户类型');
        });

        Schema::table('oauth_auth_codes', function (Blueprint $table) {
            $table->string('user_type')
                  ->after('user_uuid')
                  ->nullable()
                  ->default('user')
                  ->comment('用户类型');
        });

        Schema::table('oauth_user_authorizations', function (Blueprint $table) {
            $table->string('user_type')
                  ->after('user_uuid')
                  ->nullable()
                  ->default('user')
                  ->comment('用户类型');
        });

        Schema::table('oauth_user_bindings', function (Blueprint $table) {
            $table->string('user_type')
                  ->after('user_uuid')
                  ->nullable()
                  ->default('user')
                  ->comment('用户类型');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        //
    }
};
