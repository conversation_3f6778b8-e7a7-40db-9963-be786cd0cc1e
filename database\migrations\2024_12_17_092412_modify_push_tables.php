<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('push_messages', function (Blueprint $table) {
            $table->tinyInteger('push_type')
                  ->default(1)
                  ->after('delivery_type')
                  ->comment('推送类型 1:普通 2:群发');
        });

        Schema::table('oauth_client_subscriptions', function (Blueprint $table) {
            $table->string('subscription_code')
                  ->after('id')
                  ->comment('订阅码');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('push_messages', function (Blueprint $table) {
            $table->dropColumn('push_type');
        });

        Schema::table('oauth_client_subscriptions', function (Blueprint $table) {
            $table->dropColumn('subscription_code');
        });
    }
};
