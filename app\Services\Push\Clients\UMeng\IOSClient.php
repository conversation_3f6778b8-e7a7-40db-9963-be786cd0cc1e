<?php

namespace App\Services\Push\Clients\UMeng;

use App\Services\Push\Contracts\ClientInterface;
use App\Services\Push\Support\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class IOSClient implements ClientInterface
{
    /**
     * 消息发送接口
     */
    private const ENDPOINT_SEND = 'https://msgapi.umeng.com/api/send';

    /**
     * 消息状态查询接口
     */
    private const ENDPOINT_STATUS = 'https://msgapi.umeng.com/api/status';

    /**
     * 消息撤销接口
     */
    private const ENDPOINT_CANCEL = 'https://msgapi.umeng.com/api/cancel';

    /**
     * 文件上传接口
     */
    private const ENDPOINT_UPLOAD = 'https://msgapi.umeng.com/upload';

    /**
     * 配置对象
     */
    protected Config $config;

    public function __construct(array $config)
    {
        $this->config = new Config($config);
    }

    /**
     * 发送消息
     */
    public function send(array $params): array
    {
        [$url, $params] = $this->prepareRequest($params, 'send');
        return $this->makeRequest($url, $params);
    }

    /**
     * 查询消息状态
     */
    public function status(array $params): array
    {
        [$url, $params] = $this->prepareRequest($params, 'status');
        return $this->makeRequest($url, $params);
    }

    /**
     * 取消定时消息
     */
    public function cancel(array $params): array
    {
        [$url, $params] = $this->prepareRequest($params, 'cancel');
        return $this->makeRequest($url, $params);
    }

    /**
     * 上传文件
     */
    public function upload(array $params): array
    {
        [$url, $params] = $this->prepareRequest($params, 'upload');
        return $this->makeRequest($url, $params);
    }

    /**
     * 获取配置值
     */
    public function getConfig(string $key, mixed $default = null): mixed
    {
        return $this->config->get($key, $default);
    }

    /**
     * 准备请求参数
     *
     * @param array $params 原始参数
     * @param string $type 请求类型
     * @return array [url, params]
     */
    protected function prepareRequest(array $params, string $type): array
    {
        // 添加通用参数
        if (!isset($params['timestamp'])) {
            $params['timestamp'] = time();
        }

        if (!isset($params['production_mode'])) {
            $params['production_mode'] = $this->config->get('ios.productionMode');
        }

        if (!isset($params['appkey'])) {
            $params['appkey'] = $this->config->get('ios.appKey');
        }

        // 根据不同类型构建URL
        $endpoint = match($type) {
            'send' => self::ENDPOINT_SEND,
            'status' => self::ENDPOINT_STATUS,
            'cancel' => self::ENDPOINT_CANCEL,
            'upload' => self::ENDPOINT_UPLOAD,
            default => throw new \InvalidArgumentException("Invalid request type: {$type}")
        };

        // 生成签名URL
        $url = $this->buildSignedUrl($endpoint, $params);

        return [$url, $params];
    }

    /**
     * 构建带签名的URL
     */
    protected function buildSignedUrl(string $endpoint, array $params): string
    {
        $body = json_encode($params);
        $sign = md5('POST' . $endpoint . $body . $this->config->get('ios.appMasterSecret'));

        return $endpoint . '?sign=' . $sign;
    }

    /**
     * 发送HTTP请求
     */
    protected function makeRequest(string $url, array $params): array
    {
        try {
//            Log::info('UMeng IOS Request', ['url' => $url, 'params' => $params]);
            $response = Http::post($url, $params);
            return $response->json();
        } catch (\Throwable $e) {
            return [
                'ret' => 'FAIL',
                'data' => [
                    'error_code' => $e->getCode(),
                    'error_msg' => $e->getMessage()
                ]
            ];
        }
    }
}
