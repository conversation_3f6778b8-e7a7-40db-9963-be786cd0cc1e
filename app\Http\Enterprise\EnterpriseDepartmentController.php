<?php

namespace App\Http\Enterprise;

use App\Http\Enterprise\EnterpriseBaseController;
use App\Models\Enterprise;
use App\Models\EnterpriseDepartment;
use App\Models\EnterpriseContact;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class EnterpriseDepartmentController extends EnterpriseBaseController
{
    protected $validationMessages = [
        'name.required'    => '部门名称不能为空',
        'name.string'      => '部门名称必须是字符串',
        'name.max'         => '部门名称不能超过50个字符',
        'code.required'    => '部门编码不能为空',
        'code.string'      => '部门编码必须是字符串',
        'code.max'         => '部门编码不能超过50个字符',
        'code.unique'      => '部门编码已存在',
        'parent_id.exists' => '上级部门不存在',
        'sort.integer'     => '排序值必须是整数',
        'remark.string'    => '备注必须是字符串',
    ];

    protected const PERMISSION_MAP = [
        'index' => '企业部门管理.查看列表',
        'store' => '企业部门管理.创建',
        'show' => '企业部门管理.查看详情',
        'update' => '企业部门管理.编辑',
        'destroy' => '企业部门管理.删除',
        'move' => '企业部门管理.移动',
    ];

    /**
     * 获取企业部门列表
     */
    public function index(Request $request, $enterpriseId)
    {
        $enterprise = Enterprise::findOrFail($enterpriseId);
        
        $query = EnterpriseDepartment::byEnterprise($enterpriseId)
            ->when($request->input('keyword'), function ($query, $keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                        ->orWhere('code', 'like', "%{$keyword}%");
                });
            });

        // 是否返回树形结构
        if ($request->boolean('tree', true)) {
            $departments = $query->get()->toTree();
        } else {
            // 如果指定了父部门ID，则获取其子部门
            if ($request->filled('parent_id')) {
                $parent = EnterpriseDepartment::where('enterprise_id', $enterpriseId)
                    ->findOrFail($request->input('parent_id'));
                
                // 如果只需要直接子部门
                if ($request->boolean('direct_children', true)) {
                    $query->where('parent_id', $parent->id);
                } else {
                    $query = $parent->descendants();
                }
            }
            
            $departments = $query->defaultOrder()->get();
        }
        
        return Respond::success($departments);
    }

    /**
     * 获取部门详情
     */
    public function show($enterpriseId, $id)
    {
        $department = EnterpriseDepartment::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);
            
        // 加载关联数据
        $department->load('contacts');
        
        // 获取祖先部门
        $ancestors = $department->ancestors()->get();
        
        // 获取子部门
        $children = $department->children()->get();
        
        return Respond::success([
            'department' => $department,
            'ancestors' => $ancestors,
            'children' => $children
        ]);
    }

    /**
     * 创建企业部门
     */
    public function store(Request $request, $enterpriseId)
    {
        $enterprise = Enterprise::findOrFail($enterpriseId);
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('enterprise_departments')->where(function ($query) use ($enterpriseId) {
                    return $query->where('enterprise_id', $enterpriseId);
                }),
            ],
            'parent_id' => [
                'nullable',
                'string',
                Rule::exists('enterprise_departments', 'id')->where(function ($query) use ($enterpriseId) {
                    return $query->where('enterprise_id', $enterpriseId);
                }),
            ],
            'sort' => 'nullable|integer',
            'remark' => 'nullable|string',
        ], $this->validationMessages);
        
        if ($validator->fails()) {
            return Respond::error($validator->errors()->first(), null, null, 422);
        }
        
        try {
            DB::beginTransaction();
            
            $departmentData = [
                'enterprise_id' => $enterpriseId,
                'name' => $request->input('name'),
                'code' => $request->input('code'),
                'sort' => $request->input('sort', 0),
                'remark' => $request->input('remark'),
            ];
            
            // 创建部门记录
            $department = new EnterpriseDepartment($departmentData);
            
            if ($request->filled('parent_id')) {
                // 如果有父部门，添加为子节点
                $parent = EnterpriseDepartment::findOrFail($request->input('parent_id'));
                $parent->appendNode($department);
            } else {
                // 否则创建为根节点
                $department->save();
                $department->makeRoot();
            }
            
            DB::commit();
            return Respond::success($department);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('创建部门失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新企业部门
     */
    public function update(Request $request, $enterpriseId, $id)
    {
        $department = EnterpriseDepartment::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:50',
            'code' => [
                'sometimes',
                'required',
                'string',
                'max:50',
                Rule::unique('enterprise_departments')->where(function ($query) use ($enterpriseId) {
                    return $query->where('enterprise_id', $enterpriseId);
                })->ignore($id),
            ],
            'sort' => 'nullable|integer',
            'remark' => 'nullable|string',
        ], $this->validationMessages);
        
        if ($validator->fails()) {
            return Respond::error($validator->errors()->first(), null, null, 422);
        }
        
        try {
            DB::beginTransaction();
            
            // 更新基本信息
            $department->update($request->only(['name', 'code', 'sort', 'remark']));
            
            DB::commit();
            return Respond::success($department);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('更新部门失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除企业部门
     */
    public function destroy($enterpriseId, $id)
    {
        $department = EnterpriseDepartment::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);
        
        // 检查是否有子部门
        if ($department->children()->exists()) {
            return Respond::error('无法删除含有子部门的部门，请先删除所有子部门');
        }
        
        try {
            DB::beginTransaction();
            
            // 处理关联的联系人
            EnterpriseContact::where('department_id', $id)->update(['department_id' => null]);
            
            // 删除部门
            $department->delete();
            
            DB::commit();
            return Respond::success(null, '删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('删除部门失败: ' . $e->getMessage());
        }
    }

    /**
     * 移动部门
     */
    public function move(Request $request, $enterpriseId, $id)
    {
        $department = EnterpriseDepartment::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);
        
        $validated = $request->validate([
            'parent_id' => [
                'nullable',
                'string',
                Rule::exists('enterprise_departments', 'id')->where(function ($query) use ($enterpriseId) {
                    return $query->where('enterprise_id', $enterpriseId);
                }),
            ],
            'position' => 'nullable|in:first,last,before,after',
            'relative_id' => [
                'required_with:position',
                'string',
                Rule::exists('enterprise_departments', 'id')->where(function ($query) use ($enterpriseId) {
                    return $query->where('enterprise_id', $enterpriseId);
                }),
            ],
        ], $this->validationMessages);
        
        // 检查是否移动到自己或其子部门下
        if ($request->filled('parent_id')) {
            $newParentId = $request->input('parent_id');
            
            if ($newParentId == $id) {
                return Respond::error('不能将部门移动到自己下');
            }
            
            $descendants = $department->descendants()->pluck('id')->toArray();
            if (in_array($newParentId, $descendants)) {
                return Respond::error('不能将部门移动到其子部门下');
            }
        }
        
        try {
            DB::beginTransaction();
            
            if ($request->input('position')) {
                // 根据相对位置移动
                $relativeNode = EnterpriseDepartment::where('enterprise_id', $enterpriseId)
                    ->findOrFail($request->input('relative_id'));
                
                switch ($request->input('position')) {
                    case 'first':
                        $department->prependToNode($relativeNode)->save();
                        break;
                    case 'last':
                        $department->appendToNode($relativeNode)->save();
                        break;
                    case 'before':
                        $department->beforeNode($relativeNode)->save();
                        break;
                    case 'after':
                        $department->afterNode($relativeNode)->save();
                        break;
                }
            } elseif ($request->filled('parent_id')) {
                // 移动到指定父节点下
                $parent = EnterpriseDepartment::where('enterprise_id', $enterpriseId)
                    ->findOrFail($request->input('parent_id'));
                $department->appendToNode($parent)->save();
            } else {
                // 移动为根节点
                $department->makeRoot()->save();
            }
            
            DB::commit();
            return Respond::success($department);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('移动部门失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取部门树形结构
     */
    public function tree($enterpriseId)
    {
        $tree = EnterpriseDepartment::getDepartmentTree($enterpriseId);
        return Respond::success($tree);
    }
    
    /**
     * 获取部门联系人
     */
    public function contacts($enterpriseId, $id)
    {
        $department = EnterpriseDepartment::where('enterprise_id', $enterpriseId)
            ->findOrFail($id);
            
        $contacts = $department->contacts()->get();
        
        return Respond::success($contacts);
    }
} 