<?php

use App\Console\Commands\ImportDepartmentAndUser;
use App\Enums\ErrorCodeEnum;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\SignatureCheckMiddleware;
use App\Utils\Respond;


return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(web: __DIR__ . '/../routes/web.php', then: function () {
        Route::middleware('api')
            ->group(base_path('routes/no_auth.php'));

        if (config('app.env') !== 'production') {
            Route::middleware('api')
                ->prefix('api/test')
                ->group(base_path('routes/test.php'));
        }

        Route::middleware(['api', 'auth:api'])
            ->prefix('api')
            ->name('api.')
            ->group(base_path('routes/user.php'));

        Route::middleware(['api', 'auth:admin'])
            ->prefix('admin/api')
            ->name('admin.')
            ->group(base_path('routes/admin.php'));
    })
    ->withMiddleware(function (Middleware $middleware) {
        // $middleware->trustProxies(at: [
        //     '192.168.0.7',
        //     '192.168.0.8',
        //     '192.168.1.31',
        //     '192.168.1.32',
        //     '2408:4002:1036:100:c47b:9536:ffb0:38e4',
        //     '2408:4002:1036:100:c47b:9536:ffb0:38e5',
        //     '2408:4002:10f4:f600:3894:2696:fc8d:df5e',
        //     '2408:4002:10f4:f600:3894:2696:fc8d:df5d',
        // ]);
        $middleware->alias([
            'signature' => SignatureCheckMiddleware::class,
        ]);
    })->withCommands([
        ImportDepartmentAndUser::class,
    ])
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (\Illuminate\Validation\ValidationException $e, $request) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR->value, $e->validator->errors()
                ->first(), $e->validator->errors()
                ->toArray(), 422);
        })
            ->render(function (\Symfony\Component\HttpKernel\Exception\NotFoundHttpException|\Illuminate\Database\Eloquent\ModelNotFoundException|\Symfony\Component\Routing\Exception\RouteNotFoundException $e, $request) {
                return Respond::error(ErrorCodeEnum::NOT_FOUND->value, ErrorCodeEnum::NOT_FOUND->label(), [], 404);
            })
            ->render(function (\Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException $e, $request) {
                return Respond::error(ErrorCodeEnum::METHOD_NOT_ALLOWED->value, ErrorCodeEnum::METHOD_NOT_ALLOWED->label(), [], 405);
            })
            ->render(function (\Illuminate\Auth\AuthenticationException $e, $request) {
                return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN->value, '请登录', [], 401);
            });
    })
    ->create();
