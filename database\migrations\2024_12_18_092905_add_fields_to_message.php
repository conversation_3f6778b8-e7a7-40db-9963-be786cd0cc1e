<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('push_templates', function (Blueprint $table) {
            $table->string('cloud_template_id', 100)
                  ->after('delivery_type')
                  ->nullable()
                  ->comment('云推送模板ID');

            $table->tinyInteger('is_silent')
                  ->after('cloud_template_id')
                  ->default(0)
                  ->comment('是否静默推送 0否 1是');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('push_templates', function (Blueprint $table) {
            $table->dropColumn('cloud_template_id');
        });
    }
};
