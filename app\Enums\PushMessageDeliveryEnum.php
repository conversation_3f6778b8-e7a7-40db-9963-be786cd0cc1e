<?php

namespace App\Enums;

enum PushMessageDeliveryEnum: int
{

    // 推送目标类型
    case DELIVERY_ANDROID = 1;    // Android设备
    case DELIVERY_IOS = 2;       // iOS设备
    case DELIVERY_USER = 3;      // 用户
    case DELIVERY_PUSH_TOKEN = 5; // 实时活动pushToken
    case DELIVERY_ACTIVITY = 6;   // 实时活动activityId
    case DELIVERY_HARMONY = 7;    // 鸿蒙设备
    case DELIVERY_ALL = 8;        // 全部设备


    public function label(): string {
        return match ($this) {
            self::DELIVERY_ANDROID => 'Android设备',
            self::DELIVERY_IOS => 'iOS设备',
            self::DELIVERY_USER => '用户',
            self::DELIVERY_PUSH_TOKEN => '实时活动pushToken',
            self::DELIVERY_ACTIVITY => '实时活动activityId',
            self::DELIVERY_HARMONY => '鸿蒙设备',
            self::DELIVERY_ALL => '全部设备',
            default => '',
        };
    }
    public static function options(): array {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }
}
