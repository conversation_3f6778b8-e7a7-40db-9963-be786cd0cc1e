<?php

namespace App\Enums;

enum ErrorCodeEnum: int
{

    case SYSTEM_ERROR = 100000;
    case MODEL_NOT_FOUND = 100001;
    case NOT_FOUND = 100002;
    case METHOD_NOT_ALLOWED = 100003;
    case DATABASE_QUERY_EXCEPTION = 100004;
    case OTHER_HTTP_EXCEPTION = 100005;
    case HAS_SENSITIVE = 100006;
    // 参数验证错误
    case VALIDATE_ERROR = 100007;
    case INVALID_REQUEST = 100008;
    case PARTIAL_FAILURE = 100009;

    // 增加错误代码: 无此用户, 用户状态异常, 用户密码错误, 用户已存在, 用户未登录
    case USER_NOT_FOUND = 110001;
    case USER_STATUS_ERROR = 110002;
    case USER_PASSWORD_ERROR = 110003;
    case USER_EXIST = 110004;
    case USER_NOT_LOGIN = 110005;
    // 三方授权失败
    case THIRD_AUTH_FAILED = 110006;
    // 一键登录获取手机号失败
    case ONECLICK_GET_PHONE_FAILED = 110007;
    // 绑定手机号超时
    case BIND_PHONE_TIMEOUT = 110008;
    // 手机已绑定
    case PHONE_ALREADY_BIND = 110009;


    // 参数缺失, 重复请求, 签名错误, 签名过期, 非法参数
    case PARAMS_MISSING = 120001;
    case REPLAY_ATTACK = 120002;
    case SIGNATURE_ERROR = 120003;
    case SIGNATURE_EXPIRED = 120004;
    case ILLEGAL_PARAMS = 120005;
    case ILLEGAL_CLIENT = 120006;


    // 发送失败, 发送频繁, 发送次数超限, 验证码错误, 验证码过期, 手机号错误, 未绑定手机号
    case SEND_FAILED = 130001;
    case SEND_FREQUENT = 130002;
    case SEND_LIMIT = 130003;
    case CODE_ERROR = 130004;
    case CODE_EXPIRED = 130005;
    case MOBILE_ERROR = 130006;
    case MOBILE_NOT_BIND = 130007;


    //禁止更新, 注销失败, 已绑定微信, 未绑定微信, 已实名, 未实名, 实名失败, 等待审核
    case FORBIDDEN_UPDATE = 140001;
    case LOGOUT_FAILED = 140002;
    case WECHAT_BINDED = 140003;
    case WECHAT_UNBINDED = 140004;
    case REALNAME_BINDED = 140005;
    case REALNAME_UNBINDED = 140006;
    case REALNAME_FAILED = 140007;
    case WAITING_AUDIT = 140008;
    case WECHAT_HASBEEN_BINDED = 140010;
    // 身份证已认证
    case IDENTITY_BINDED = 140009;

    // 消息推送
    case PUSH_MESSAGE_FAILED = 150001;
    case PUSH_MESSAGE_FREQUENT = 150002;
    case PUSH_MESSAGE_LIMIT = 150003;
    // 模板不可用
    case PUSH_TEMPLATE_NOT_AVAILABLE = 150004;
    // 模板参数错误
    case PUSH_TEMPLATE_PARAMS_ERROR = 150005;
    case PUSH_SUBSCRIPTION_NOT_FOUND = 150006;
    case PUSH_TEMPLATE_HAS_BEEN_USED = 150007;

    // OAuth
    // client 不存在
    case OAUTH_CLIENT_NOT_FOUND = 160001;
    // client 状态异常
    case OAUTH_CLIENT_STATUS_ERROR = 160002;
    // client 密钥错误
    case OAUTH_CLIENT_SECRET_ERROR = 160003;
    // client 未授权
    case OAUTH_CLIENT_UNAUTHORIZED = 160004;
    // client 授权失败
    case OAUTH_AUTH_FAILED = 160005;
    // client 授权过期
    case OAUTH_AUTH_EXPIRED = 160006;
    // client code无效
    case OAUTH_CODE_INVALIDATE = 160007;
    // client scopes 错误
    case OAUTH_SCOPES_ERROR = 160008;
    // client refresh_token 失效
    case OAUTH_REFRESH_TOKEN_INVALIDATE = 160009;

    case OAUTH_CLIENT_REDIRECT_INVALIDATE = 160010;
    // invalid token
    case OAUTH_TOKEN_INVALIDATE = 160011;
    case OAUTH_OPNEID_NOT_FOUND = 160012;
    // 用户未授权
    case OAUTH_USER_UNAUTHORIZED = 160013;
    // 未绑定管理员
    case OAUTH_USER_NO_ADMIN_BINDING = 160014;
    // 权限不足
    case OAUTH_ACCESS_DENIED = 160015;
    // 权限不足
    case OAUTH_ACCESS_CLIENT_DENIED = 160016;

    // Admin System Error Codes (170000-170999)
    case ADMIN_NOT_FOUND = 170001;
    case ADMIN_USERNAME_EXISTS = 170002;
    case ADMIN_PASSWORD_ERROR = 170003;
    case ADMIN_STATUS_ERROR = 170004;
    case ADMIN_LOGIN_FAILED = 170005;
    case ADMIN_NO_PERMISSION = 170006;
    case ADMIN_INVALID_PERMISSION_FORMAT = 170007;
    // 扫码登录相关错误码
    case ADMIN_QR_CODE_EXPIRED = 170008;
    case ADMIN_QR_CODE_INVALID = 170009;
    case ADMIN_QR_CODE_CANCELED = 170010;
    case ADMIN_QR_CODE_CONFIRMED = 170011;
    case ADMIN_QR_CODE_DEVICE_ID_REQUIRED = 170012;

    // Department Error Codes (171000-171999)
    case DEPARTMENT_NOT_FOUND = 171001;
    case DEPARTMENT_CODE_EXISTS = 171002;
    case DEPARTMENT_HAS_CHILDREN = 171003;
    case DEPARTMENT_HAS_ADMINS = 171004;
    case DEPARTMENT_STATUS_ERROR = 171005;
    case DEPARTMENT_PARENT_NOT_FOUND = 171006;
    case DEPARTMENT_PARENT_DISABLED = 171007;
    case INVALID_PARENT_NODE = 171008;

    public function label(): string {
        return match ($this) {

            self::SYSTEM_ERROR => '请稍后再试',
            self::MODEL_NOT_FOUND => '未找到该项目',
            self::NOT_FOUND => '内容不存在',
            self::METHOD_NOT_ALLOWED => '不支持的请求方式',
            self::DATABASE_QUERY_EXCEPTION => '数据库查询异常',
            self::OTHER_HTTP_EXCEPTION => '其他网络错误',
            self::HAS_SENSITIVE => '输入信息违规,请修改或重新输入',
            self::VALIDATE_ERROR => '参数错误',
            self::INVALID_REQUEST => '无效的请求或参数',
            self::PARTIAL_FAILURE => '部分操作失败',

            self::USER_NOT_FOUND => '无此用户',
            self::USER_STATUS_ERROR => '用户状态异常',
            self::USER_PASSWORD_ERROR => '用户密码错误',
            self::USER_EXIST => '用户已存在',
            self::USER_NOT_LOGIN => '用户未登录',
            self::THIRD_AUTH_FAILED => '三方授权失败',
            self::ONECLICK_GET_PHONE_FAILED => '一键登录获取手机号失败',
            self::BIND_PHONE_TIMEOUT => '绑定手机号超时',
            self::PHONE_ALREADY_BIND => '手机已绑定',

            self::PARAMS_MISSING => '参数缺失',
            self::REPLAY_ATTACK => '重复请求',
            self::SIGNATURE_ERROR => '非法请求',
            self::SIGNATURE_EXPIRED => '签名过期',
            self::ILLEGAL_PARAMS => '非法参数',

            self::SEND_FAILED => '发送失败',
            self::SEND_FREQUENT => '发送频繁',
            self::SEND_LIMIT => '发送次数超限',
            self::CODE_ERROR => '验证码错误',
            self::CODE_EXPIRED => '验证码过期',
            self::MOBILE_ERROR => '手机号错误',
            self::MOBILE_NOT_BIND => '未绑定手机号',

            self::FORBIDDEN_UPDATE => '禁止更新',
            self::LOGOUT_FAILED => '注销失败',
            self::WECHAT_BINDED => '已绑定微信',
            self::WECHAT_UNBINDED => '未绑定微信',
            self::REALNAME_BINDED => '已实名',
            self::REALNAME_UNBINDED => '未实名',
            self::REALNAME_FAILED => '实名失败',
            self::WAITING_AUDIT => '等待审核',
            self::IDENTITY_BINDED => '身份证已认证',
            self::WECHAT_HASBEEN_BINDED => '微信已经被绑定',

            self::PUSH_MESSAGE_FAILED => '消息发送失败',
            self::PUSH_MESSAGE_FREQUENT => '消息发送频繁',
            self::PUSH_MESSAGE_LIMIT => '消息发送次数超限',
            // 模板不可用
            self::PUSH_TEMPLATE_NOT_AVAILABLE => '模板不可用',
            // 模板参数错误
            self::PUSH_TEMPLATE_PARAMS_ERROR => '模板参数错误',

            self::PUSH_SUBSCRIPTION_NOT_FOUND => '未找到订阅配置',
            self::PUSH_TEMPLATE_HAS_BEEN_USED => '模板已被使用',

            self::OAUTH_CLIENT_NOT_FOUND => '应用不存在',
            self::OAUTH_CLIENT_STATUS_ERROR => '应用状态异常',
            self::OAUTH_CLIENT_SECRET_ERROR => '密钥缺失',
            self::OAUTH_CLIENT_UNAUTHORIZED => '未授权应用',
            self::OAUTH_AUTH_FAILED => '应用授权失败',
            self::OAUTH_AUTH_EXPIRED => '应用授权过期, 请重新授权',
            self::OAUTH_CODE_INVALIDATE => 'code无效, 授权失败',
            self::OAUTH_SCOPES_ERROR => '应用作用域错误',
            self::OAUTH_REFRESH_TOKEN_INVALIDATE => 'AccessToken失效',
            self::OAUTH_CLIENT_REDIRECT_INVALIDATE => '跳转地址不合法',
            self::OAUTH_TOKEN_INVALIDATE => '无效AccessToken',
            self::OAUTH_OPNEID_NOT_FOUND => '未找到openId',
            self::OAUTH_USER_UNAUTHORIZED => '用户未授权',
            self::OAUTH_USER_NO_ADMIN_BINDING => '请通过管理侧绑定APP账号',
            self::OAUTH_ACCESS_DENIED => '应用权限不足',
            self::OAUTH_ACCESS_CLIENT_DENIED => '未授权访问该应用,权限不足',
            // Admin errors
            self::ADMIN_NOT_FOUND => '账号不存在',
            self::ADMIN_USERNAME_EXISTS => '用户名已存在',
            self::ADMIN_PASSWORD_ERROR => '密码错误',
            self::ADMIN_STATUS_ERROR => '账号状态异常',
            self::ADMIN_LOGIN_FAILED => '登录失败',
            self::ADMIN_NO_PERMISSION => '没有操作权限',
            self::ADMIN_INVALID_PERMISSION_FORMAT => '无效的权限格式',
            // 扫码登录相关错误信息
            self::ADMIN_QR_CODE_EXPIRED => '二维码已过期',
            self::ADMIN_QR_CODE_INVALID => '无效的二维码',
            self::ADMIN_QR_CODE_CANCELED => '二维码已取消',
            self::ADMIN_QR_CODE_CONFIRMED => '二维码已被使用',
            self::ADMIN_QR_CODE_DEVICE_ID_REQUIRED => '无效的二维码请求',
            // Department errors
            self::DEPARTMENT_NOT_FOUND => '部门不存在',
            self::DEPARTMENT_CODE_EXISTS => '部门编码已存在',
            self::DEPARTMENT_HAS_CHILDREN => '存在下级部门',
            self::DEPARTMENT_HAS_ADMINS => '部门下存在管理员',
            self::DEPARTMENT_STATUS_ERROR => '部门状态异常',
            self::DEPARTMENT_PARENT_NOT_FOUND => '上级部门不存在',
            self::DEPARTMENT_PARENT_DISABLED => '上级部门已禁用',
            self::INVALID_PARENT_NODE => '无效的上级部门',

            default => '',
        };
    }
}
