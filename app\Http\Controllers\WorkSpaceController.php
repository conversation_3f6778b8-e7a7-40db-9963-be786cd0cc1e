<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\OAuthException;
use App\Models\AdminDepartment;
use App\Models\AdminUser;
use App\Models\Client;
use App\Services\OAuthService;
use App\Utils\OAuthCacher;
use App\Utils\Respond;
use App\Utils\Tools;
use Illuminate\Http\Request;

class WorkSpaceController extends Controller
{
    public function __construct() {}

    /**
     * 获取企业部门列表
     * 
     * 根据应用关联的企业获取部门列表
     */
    public function enterpriseDepartments(Request $request)
    {
        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        if ($client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_DENIED);
        }

        // @TODO 如果应用未关联企业，则返回空列表, 
        // if (!$client->enterprise_id) {
        //     return Respond::success([]);
        // }

        $departments = AdminDepartment::with('parentDepartment')
            ->where('status', AdminDepartment::STATUS_ENABLED)
            ->get();

        return Respond::success($departments->map(function ($department) {
            return [
                'id'          => $department->id,
                'name'        => $department->name,
                'code'        => $department->code,
                'level'       => $department->level,
                'full_name'   => $department->full_name,
                'status'      => $department->status,
                'status_text' => $department->status_text,
                'remark'      => $department->remark,
                'parent'      => $department->parentDepartment ? [
                    'id'   => $department->parentDepartment->id,
                    'code' => $department->parentDepartment->code,
                    'name' => $department->parentDepartment->name,
                ] : null,
            ];
        }));
    }

    /**
     * 获取企业通讯录
     * 
     * 根据应用关联的企业获取管理员用户列表
     */
    public function enterpriseFullContacts(Request $request)
    {
        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        if ($client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_DENIED);
        }

        // @TODO 如果应用未关联企业，则返回空列表, 
        // if(!$client->enterprise_id){
        //     return Respond::success([]);
        // }


        $admins = AdminUser::with([
            'departments',
            'oAuthUserBinding' => function ($query) use ($client) {
                $query->where('client_id', $client->id);
            },
        ])
            ->where('status', AdminUser::STATUS_ENABLED)
            ->get();

        return Respond::success($admins->map(function ($admin) {
            return [
                'open_id'      => $admin->oAuthUserBinding?->open_id ?? null,
                'union_id'     => $admin->uuid,
                'true_name'    => $admin->true_name,
                'display_name' => $admin->display_name,
                'nickname'     => $admin->nickname,
                'mobile'       => $admin->mobile,
                'avatar'       => $admin->display_avatar,
                'short_mobile' => $admin->short_mobile,
                'departments'  => $admin->departments->map(function ($department) {
                    return [
                        'id'        => $department->id,
                        'code'      => $department->code,
                        'name'      => $department->name,
                        'is_leader' => $department->pivot->is_leader,
                    ];
                })->toArray(),
            ];
        }));
    }


    /**
     * 递归构建部门树
     */
    protected function buildDepartmentTree($departments, &$result, $parentId)
    {
        $list = $departments->where('parent_id', $parentId)->values();

        foreach ($list as $department) {
            $item = [
                'id' => $department->id,
                'name' => $department->name,
                'code' => $department->code,
                'parent_id' => $department->parent_id,
                'level' => $department->level,
                'sort' => $department->sort,
                'members' => [],
                'children' => [],
            ];

            // 获取部门成员
            $members = $department->admins()
                ->where('status', AdminUser::STATUS_ENABLED)
                ->get()
                ->map(function ($user) {
                    return [
                        'uuid' => $user->uuid,
                        'username' => $user->username,
                        'nickname' => $user->nickname,
                        'true_name' => $user->true_name,
                        'display_name' => $user->display_name,
                        'mobile' => $user->mobile,
                        'avatar' => $user->display_avatar,
                        'is_leader' => (bool)$user->pivot->is_leader,
                        'job_title' => $user->pivot->job_title,
                    ];
                })
                ->toArray();

            $item['members'] = $members;

            // 递归处理子部门
            $this->buildDepartmentTree($departments, $item['children'], $department->id);

            $result[] = $item;
        }
    }
}
