<?php

namespace App\Utils;

class JWTDecoder
{
    /**
     * 解码JWT Token
     *
     * @param string $token JWT token字符串
     *
     * @return array|false 解码后的数据或false(解码失败)
     */
    public function decode(string $token) {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }

        $header = $this->decodeSegment($parts[0]);
        $payload = $this->decodeSegment($parts[1]);
        $signature = $this->urlsafeB64Decode($parts[2]);

        if (!$header || !$payload) {
            return false;
        }

        return [
            'header' => $header,
            'headerRaw' => $parts[0],
            'payload' => $payload,
            'payloadRaw' => $parts[1],
            'signature' => $signature,
        ];
    }

    /**
     * 验证JWT Token
     *
     * @param string $token JWT token字符串
     * @param string|null $accessKey 密钥字符串
     * @param string|null $accessKeyPath 密钥文件路径
     *
     * @return bool 验证是否通过
     * @throws \Exception
     */
    public function verify(string $token, ?string $accessKey = null, ?string $accessKeyPath = null): bool {
        if ($accessKey === null && $accessKeyPath === null) {
            throw new \Exception('必须提供密钥或密钥文件路径');
        }

        $decodeToken = $this->decode($token);
        // 获取header和payload
        $header = $decodeToken['header'];
        $payload = $decodeToken['payload'];

        if (!$header || !$payload) {
            return false;
        }

        // 验证签名
        $flag = $this->calculateSignature(accessKey: $accessKey, accessKeyPath: $accessKeyPath, payload: $decodeToken['headerRaw'] . '.' . $decodeToken['payloadRaw'], signature: $decodeToken['signature'], algorithm: $header['alg'] ?? 'HS256', userId: $payload['sub'] ?? null);

        if (!$flag) {
            return false;
        }

        // 验证时间相关的声明
        $time = time();

        // 验证是否过期(exp)
        if (isset($payload['exp']) && $time >= $payload['exp']) {
            return false;
        }

        // 验证生效时间(nbf)
        if (isset($payload['nbf']) && $time < $payload['nbf']) {
            return false;
        }

        // 验证签发时间(iat)
        if (isset($payload['iat']) && $time < $payload['iat']) {
            return false;
        }

        return true;
    }

    /**
     * 解码JWT段数据(header或payload)
     *
     * @param string $segment
     *
     * @return array|false
     */
    private function decodeSegment(string $segment) {
        $decoded = $this->urlsafeB64Decode($segment);
        if ($decoded === false) {
            return false;
        }

        $data = json_decode($decoded, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        return $data;
    }

    /**
     * URL安全的Base64解码
     *
     * @param string $input
     *
     * @return string|false
     */
    private function urlsafeB64Decode(string $input) {
        $remainder = strlen($input) % 4;
        if ($remainder) {
            $input .= str_repeat('=', 4 - $remainder);
        }
        $input = strtr($input, '-_', '+/');

        return base64_decode($input);
    }

    /**
     * 计算签名
     *
     * @param string|null $accessKey 密钥字符串
     * @param string|null $accessKeyPath 密钥文件路径
     * @param string $payload 需要签名的数据
     * @param string $signature 密钥
     * @param string $algorithm 算法
     * @param string|null $userId 用户ID
     *
     * @return bool
     * @throws \Exception
     */
    private function calculateSignature(?string $accessKey, ?string $accessKeyPath, string $payload, string $signature, string $algorithm, ?string $userId = null): bool {
        $algorithms = [
            'HS256' => 'sha256',
            'HS384' => 'sha384',
            'HS512' => 'sha512',
            'SM2' => 'SM2',
        ];

        $algo = $algorithms[$algorithm] ?? 'sha256';

        if ($algo === 'SM2') {
            // 对于SM2算法，优先使用密钥路径
            if ($accessKeyPath !== null) {
                GmSm::configure(publicKeyPath: $accessKeyPath);
            } elseif ($accessKey !== null) {
                GmSm::configure(publicKey: $accessKey);
            } else {
                throw new \Exception('SM2算法需要提供公钥或公钥文件路径');
            }

            return GmSm::sm2VerifySign($payload, $signature, $userId, 'hex');
        } else {
            // 对于HMAC算法，优先使用密钥字符串
            $key = $accessKey ?? file_get_contents($accessKeyPath);
            if ($key === false) {
                throw new \Exception('无法读取密钥文件');
            }

            return hash_equals(hash_hmac($algo, $payload, $key, true), $signature);
        }
    }
}
