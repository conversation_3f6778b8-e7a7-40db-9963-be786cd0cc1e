<?php

namespace App\Http\Controllers;

use App\Enums\PushMessageCategoryEnum;
use App\Http\Resources\PushMessageResource;
use App\Models\PushMessage;
use App\Models\UserMessage;
use App\Utils\Respond;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UserMessageController extends Controller
{

    protected $validationMessages = [
        'category.required' => '消息类别不能为空',
        'category.integer'  => '消息类别必须是整数',
        'category.min'      => '消息类别不能小于1',
        'category.max'      => '消息类别不能大于7',
    ];

    public function getMessageCategoryInfo($category){
        // 验证是否为有效值
        if (!PushMessageCategoryEnum::tryFrom($category)) {
            return Respond::error('无效的消息类别');
        }

        $category = PushMessageCategoryEnum::from($category);
        return Respond::success([
            'icon' => $category->icon(),
            'name' => $category->label(),
            'style' => $category->style(),
            'sort' => $category->sort(),
        ]);
    }

    /**
     * 获取用户消息列表
     */
    public function getUserMessages(Request $request) {
        $validated = $request->validate([
            'category' => 'required|integer',
        ], $this->validationMessages);

        if ($validated['category'] == PushMessageCategoryEnum::CATEGORY_HISTORY->value) {
            $messages = PushMessage::where('category', PushMessageCategoryEnum::CATEGORY_HISTORY->value)
                                   ->where('push_type', PushMessage::PUSH_TYPE_BROADCAST)
                                   ->orderBy('created_at', 'desc')
                                   ->paginate();
        } else {
            $userUuid = $this->getUserUuid();
            // 查询消息
            $messages = PushMessage::with('oauthClient')->where('category', $validated['category'])
                                   ->where(function ($query) use ($userUuid) {
                                       // 个人消息
                                       $query->where(function ($q) use ($userUuid) {
                                           $q->where('push_type', PushMessage::PUSH_TYPE_PERSONAL)
                                             ->whereHas('userMessages', function ($q) use ($userUuid) {
                                                 $q->where('user_uuid', $userUuid);
                                             });
                                       });
                                       // 群发消息
                                       $query->orWhere(function ($q) {
                                           $q->where('push_type', PushMessage::PUSH_TYPE_BROADCAST)
                                             ->where('created_at', '>=', Carbon::now()
                                                                               ->subMonth());
                                       });
                                   })
                                   ->orderBy('created_at', 'desc')
                                   ->paginate();
            // 处理用户消息状态
            //            $messageData = $this->processMessageStatus($messages, $userUuid);
        }

        return Respond::success(PushMessageResource::collection($messages));
    }

    /**
     * 获取未读消息数量
     */
    public function getUnreadCounts() {
        $userUuid = $this->getUserUuid();

        // 获取个人消息未读数
        $personalUnread = $this->getPersonalUnreadCounts($userUuid);

        // 获取群发消息未读数
        //        $broadcastUnread = $this->getBroadcastUnreadCounts($userUuid);
        $broadcastUnread = [];

        // 合并结果
        $result = $this->mergeCategoryCounts($personalUnread, $broadcastUnread);

        return Respond::success(array_values($result));
    }

    /**
     * 标记消息已读
     * 支持按消息ID列表或分类标记已读
     */
    public function markAsRead(Request $request) {
        $validated = $request->validate([
            'category' => 'integer|nullable|min:1',
        ], $this->validationMessages);

        $userUuid = $this->getUserUuid();
        $now = Carbon::now();

        UserMessage::where('user_uuid', $userUuid)
                   ->where('is_read', UserMessage::IS_READ_NO)
                   ->whereHas('PushMessage', function ($query) use ($validated) {
                       $query->where('category', $validated['category']);
                   })
                   ->update([
                       'is_read'   => UserMessage::IS_READ_YES,
                       'read_time' => $now,
                   ]);

        return Respond::success();

        /*try {
            DB::beginTransaction();

            // 获取需要标记的消息ID列表
            $messageIds = $this->getMessagesToMark(userUuid: $userUuid, category: $validated['category'] ?? null, messageIds: $validated['message_ids'] ?? null);

            if (empty($messageIds)) {
                return $this->jsonResponse(200, 'No messages to mark as read');
            }

            // 获取已存在的用户消息记录
            $existingRecords = UserMessage::where('user_uuid', $userUuid)
                                          ->whereIn('push_message_id', $messageIds)
                                          ->pluck('push_message_id')
                                          ->toArray();

            // 需要更新的记录
            if (!empty($existingRecords)) {
                UserMessage::where('user_uuid', $userUuid)
                           ->whereIn('push_message_id', $existingRecords)
                           ->update([
                               'is_read' => UserMessage::IS_READ_YES,
                               'read_time' => $now,
                               'updated_at' => $now,
                           ]);
            }

            // 需要新增的记录
            $recordsToInsert = array_diff($messageIds, $existingRecords);
            if (!empty($recordsToInsert)) {
                $insertData = array_map(function ($messageId) use ($userUuid, $now) {
                    return [
                        'user_uuid' => $userUuid,
                        'push_message_id' => $messageId,
                        'is_read' => UserMessage::IS_READ_YES,
                        'read_time' => $now,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }, $recordsToInsert);

                // 使用 chunk 分批插入
                foreach (array_chunk($insertData, 1000) as $chunk) {
                    UserMessage::insert($chunk);
                }
            }

            DB::commit();

            return Respond::success();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Mark messages as read failed', [
                'error' => $e->getMessage(),
                'user_uuid' => $userUuid,
                'category' => $validated['category'] ?? null,
                'message_ids' => $validated['message_ids'] ?? null,
            ]);

            return Respond::success();
        }*/
    }

    /**
     * 获取需要标记为已读的消息ID列表
     */
    private function getMessagesToMark(string $userUuid, ?int $category, ?array $messageIds): array {
        // 如果提供了具体的消息ID列表，直接返回
        if (!empty($messageIds)) {
            return $messageIds;
        }

        // 如果提供了分类，获取该分类下的所有未读消息
        if ($category) {
            // 获取个人消息ID列表
            $personalMessageIds = PushMessage::where('push_type', 1)
                                             ->where('category', $category)
                                             ->whereHas('userMessages', function ($query) use ($userUuid) {
                                                 $query->where('user_uuid', $userUuid)
                                                       ->where('is_read', UserMessage::IS_READ_NO);
                                             })
                                             ->pluck('id')
                                             ->toArray();

            // 获取已有但未读的群发消息
            $unreadBroadcastIds = PushMessage::where('push_type', 2)
                                             ->where('category', $category)
                                             ->whereHas('userMessages', function ($query) use ($userUuid) {
                                                 $query->where('user_uuid', $userUuid)
                                                       ->where('is_read', UserMessage::IS_READ_NO);
                                             })
                                             ->pluck('id')
                                             ->toArray();

            //            $broadcastMessageIds = array_merge($broadcastMessageQuery->pluck('id')
            //                                                                     ->toArray(), $unreadBroadcastIds);

            return array_unique(array_merge($personalMessageIds, $unreadBroadcastIds));
        }

        return [];
    }

    /**
     * 标记所有信息已读
     */
    public function markAllAsRead() {
        $userId = $this->getUserUuid();
        $now = Carbon::now();

        UserMessage::where('user_uuid', $userId)
                   ->where('is_read', UserMessage::IS_READ_NO)
                   ->update([
                       'is_read'   => UserMessage::IS_READ_YES,
                       'read_time' => $now,
                   ]);

        return Respond::success();
    }


    /**
     * 获取个人消息未读数和最新未读标题
     */
    private function getPersonalUnreadCounts(string $userUuid): array {
        return PushMessage::where('push_type', PushMessage::PUSH_TYPE_PERSONAL)
                          ->whereHas('userMessages', function ($query) use ($userUuid) {
                              $query->where('user_uuid', $userUuid)
                                    ->where('is_read', UserMessage::IS_READ_NO);
                          })
                          ->groupBy('category')
                          ->select('category', DB::raw('count(*) as count'), DB::raw('MAX(id) as latest_message_id'), DB::raw('MAX(title) as latest_title'), DB::raw('MAX(created_at) as latest_time'))
                          ->get()
                          ->mapWithKeys(function ($item) {
                              return [
                                  $item->category => [
                                      'count'        => $item->count,
                                      'latest_title' => $item->latest_title,
                                      'latest_time'  => $item->latest_time,
                                  ],
                              ];
                          })
                          ->toArray();
    }

    /**
     * 获取群发消息未读数和最新未读标题
     */
    private function getBroadcastUnreadCounts(string $userUuid): array {
        // 获取已读消息ID
        //        $readMessageIds = UserMessage::where('user_uuid', $userUuid)
        //                                     ->where('is_read', UserMessage::IS_READ_YES)
        //                                     ->pluck('push_message_id');

        // 查询未读的群发消息
        return PushMessage::where('push_type', PushMessage::PUSH_TYPE_BROADCAST)
                          ->whereDoesntHave('userMessages', function ($query) use ($userUuid) {
                              $query->where('user_uuid', $userUuid)
                                    ->where('is_read', UserMessage::IS_READ_NO);
                          })
                          ->where('created_at', '>=', Carbon::now()
                                                            ->subDays(7))
                          ->groupBy('category')
                          ->select('category', DB::raw('count(*) as count'), DB::raw('MAX(id) as latest_message_id'), DB::raw('MAX(title) as latest_title'), DB::raw('MAX(created_at) as latest_time'))
                          ->get()
                          ->mapWithKeys(function ($item) {
                              return [
                                  $item->category => [
                                      'count'        => $item->count,
                                      'latest_title' => $item->latest_title,
                                      'latest_time'  => $item->latest_time,
                                  ],
                              ];
                          })
                          ->toArray();
    }


    /**
     * 合并分类统计结果
     */
    private function mergeCategoryCounts(array $personal, array $broadcast): array {
        $result = [];
        $allCategories = PushMessageCategoryEnum::cases();

        foreach ($allCategories as $category) {
            $personalCount = $personal[$category->value]['count'] ?? 0;
            $broadcastCount = $broadcast[$category->value]['count'] ?? 0;
            $totalCount = $personalCount + $broadcastCount;

            if ($totalCount > 0) {
                // 比较个人消息和群发消息的最新时间，选择最新的标题
                $personalTime = isset($personal[$category->value]) ? strtotime($personal[$category->value]['latest_time']) : 0;
                $broadcastTime = isset($broadcast[$category->value]) ? strtotime($broadcast[$category->value]['latest_time']) : 0;

                $latestTitle = '暂时没有新通知';
                if ($personalTime > $broadcastTime) {
                    $latestTitle = $personal[$category->value]['latest_title'];
                } elseif ($broadcastTime > $personalTime) {
                    $latestTitle = $broadcast[$category->value]['latest_title'];
                } elseif ($personalTime > 0) {
                    $latestTitle = $personal[$category->value]['latest_title'];
                }

                $result[$category->value] = [
                    'icon'          => $category->icon(),
                    'category'      => $category->value,
                    'category_name' => $category->label(),
                    'category_style' => $category->style(),
                    'unread_count'  => $totalCount,
                    'latest_title'  => $latestTitle,
                ];
            } else {
                $result[$category->value] = [
                    'icon'          => $category->icon(),
                    'category'      => $category->value,
                    'category_name' => $category->label(),
                    'category_style' => $category->style(),
                    'unread_count'  => 0,
                    'latest_title'  => '无未读信息',
                ];
            }
        }

        // 按sort排序
        $result = collect($result)->sortBy(function ($item) {
            return PushMessageCategoryEnum::from($item['category'])->sort();
        })->toArray();

        return $result;
    }

    /**
     * 获取当前用户ID
     */
    private function getUserUuid(): string {
        return Auth::guard('api')
                   ->user()
                   ->getAuthIdentifier();
    }
}
