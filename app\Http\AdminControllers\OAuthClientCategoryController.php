<?php

namespace App\Http\AdminControllers;

use App\Models\ClientCategory;
use App\Utils\Respond;
use Illuminate\Http\Request;

class OAuthClientCategoryController extends AdminBaseController
{
    public function index()
    {
        $categories = ClientCategory::all();

        return Respond::success($categories);
    }

    public function options()
    {
        $status = ClientCategory::$statusMap;

        return Respond::success([
            'status' => $status,
        ]);
    }

    public function show(ClientCategory $category)
    {
        return Respond::success($category);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:' . implode(',', array_keys(ClientCategory::$statusMap)),
            'sort' => 'required|integer|min:0',
        ]);

        ClientCategory::create($request->all());

        return Respond::success();
    }

    public function update(Request $request, ClientCategory $category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:' . implode(',', array_keys(ClientCategory::$statusMap)),
            'sort' => 'required|integer|min:0',
        ]);

        $category->update($request->all());

        return Respond::success();
    }

    public function destroy(ClientCategory $category)
    {
        $category->delete();

        return Respond::success();
    }
}