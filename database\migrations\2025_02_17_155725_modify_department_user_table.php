<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('admin_department_admin_user', function (Blueprint $table) {
            $table->string('duties')
                  ->after('is_leader')
                  ->nullable()
                  ->comment('职务');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('admin_department_admin_user', function (Blueprint $table) {
            $table->dropColumn('duties');
        });
    }
};
