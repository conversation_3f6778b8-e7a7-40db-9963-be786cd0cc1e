<?php

namespace App\Http\Enterprise;

use App\Http\Enterprise\EnterpriseBaseController;
use App\Models\Enterprise;
use App\Models\Material;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class EnterpriseController extends EnterpriseBaseController
{

    protected $validationMessages = [
        'name.required' => '企业名称不能为空',
        'name.string' => '企业名称必须为字符串',
        'name.max' => '企业名称不能超过100个字符',
        'code.unique' => '企业编码已存在',
        'status.required' => '状态不能为空',
        'status.integer' => '状态必须为整数',
        'status.in' => '状态值无效',
        'contact_person.string' => '联系人必须为字符串',
        'contact_person.max' => '联系人不能超过50个字符',
        'contact_phone.string' => '联系电话必须为字符串',
        'contact_phone.max' => '联系电话不能超过20个字符',
        'address.string' => '地址必须为字符串',
        'address.max' => '地址不能超过255个字符',
    ];

    protected const PERMISSION_MAP = [
        'index' => '企业管理.查看列表',
        'store' => '企业管理.创建',
        'show' => '企业管理.查看详情',
        'update' => '企业管理.编辑',
        'destroy' => '企业管理.删除',
    ];

    /**
     * 获取企业列表
     */
    public function index(Request $request)
    {
        $query = Enterprise::query();
        
        // 搜索条件
        if ($request->filled('name')) {
            $query->where('name', 'like', '%' . $request->input('name') . '%');
        }
        
        if ($request->filled('code')) {
            $query->where('code', 'like', '%' . $request->input('code') . '%');
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }
        
        $enterprises = $query->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 15));
        
        return Respond::success($enterprises);
    }
    
    /**
     * 创建企业
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'code' => 'nullable|string|max:50|unique:enterprises',
            'description' => 'nullable|string',
            'contact_person' => 'nullable|string|max:50',
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'logo' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)
                                        ->first();
                    if ($value && !$material) {
                        $fail('无效媒体资源');
                    }
                },
            ],
            'status' => ['required', 'integer', Rule::in([Enterprise::STATUS_ENABLED, Enterprise::STATUS_DISABLED])],
        ], $this->validationMessages);
        
        try {
            DB::beginTransaction();

            $logoMaterial = Material::where('uuid', $validated['logo']??'')
                                       ->first();

            $validated['logo'] = $logoMaterial ? [
                'path'     => $logoMaterial->path,
                'provider' => $logoMaterial->provider,
                'uuid'     => $logoMaterial->uuid,
            ] : ['path' => config('uc.default_avatar'), 'provider' => 'url', 'uuid' => ''];

            $enterprise = Enterprise::create($validated);
            
            DB::commit();
            return Respond::success($enterprise);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('创建企业失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取企业详情
     */
    public function show($id)
    {
        $enterprise = Enterprise::findOrFail($id);
        return Respond::success($enterprise);
    }
    
    /**
     * 更新企业信息
     */
    public function update(Request $request, $id)
    {
        $enterprise = Enterprise::findOrFail($id);
        
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:100',
            'code' => ['sometimes', 'nullable', 'string', 'max:50', Rule::unique('enterprises')->ignore($id)],
            'description' => 'nullable|string',
            'contact_person' => 'nullable|string|max:50',
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'logo' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)
                                        ->first();
                    if ($value && !$material) {
                        $fail('无效媒体资源');
                    }
                },
            ],
            'status' => ['required', 'integer', Rule::in([Enterprise::STATUS_ENABLED, Enterprise::STATUS_DISABLED])],
        ], $this->validationMessages);
        
        try {
            DB::beginTransaction();

            $logoMaterial = Material::where('uuid', $validated['logo']??'')
                                       ->first();

            $validated['logo'] = $logoMaterial ? [
                'path'     => $logoMaterial->path,
                'provider' => $logoMaterial->provider,
                'uuid'     => $logoMaterial->uuid,
            ] : ['path' => config('uc.default_avatar'), 'provider' => 'url', 'uuid' => ''];

            $enterprise->update($validated);
            DB::commit();
            return Respond::success($enterprise);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('更新企业失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 删除企业
     */
    public function destroy($id)
    {
        $enterprise = Enterprise::findOrFail($id);
        
        try {
            DB::beginTransaction();
            
            // 解除关联关系
            // AdminUser::where('enterprise_id', $id)->update(['enterprise_id' => null]);
            // AdminDepartment::where('enterprise_id', $id)->update(['enterprise_id' => null]);
            // Client::where('enterprise_id', $id)->update(['enterprise_id' => null]);

            $enterprise->adminUsers()->delete();
            $enterprise->departments()->delete();
            $enterprise->clients()->delete();
            
            // 删除企业
            $enterprise->delete();
            
            DB::commit();
            return Respond::success(null, '删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('删除企业失败: ' . $e->getMessage());
        }
    }
} 