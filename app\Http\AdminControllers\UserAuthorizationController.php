<?php

namespace App\Http\AdminControllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\UserAuthorizationResouce;
use App\Models\Client;
use App\Models\OAuthUserAuthorization;
use App\Utils\OAuthCacher;
use App\Utils\Respond;
use Illuminate\Http\Request;

class UserAuthorizationController extends AdminBaseController
{
    protected const PERMISSION_MAP = [
        'index'  => '用户应用授权.查看列表',
        'show'   => '用户应用授权.查看详情',
        'revoke' => '用户应用授权.撤销',
    ];

    public function index(Request $request) {
        $authorizations = OAuthUserAuthorization::query()
                                                ->with(['client'])
                                                // ->where('user_type', 'user')
                                                ->when($request->input('client_id'), function ($query, $client_id) {
                                                    $query->where('client_id', $client_id);
                                                })
                                                ->when($request->input('user_uuid'), function ($query, $user_uuid) {
                                                    $query->where('user_uuid', $user_uuid);
                                                })
                                                ->latest()
                                                ->paginate();

        return Respond::success(UserAuthorizationResouce::collection($authorizations));
    }

    public function show(OAuthUserAuthorization $authorization) {
        $authorization->load('client');

        return Respond::success(UserAuthorizationResouce::make($authorization));
    }

    public function revoke(OAuthUserAuthorization $authorization) {

        $authorization->revoke();

        // Invalidate caches
        $this->invalidateAuthorizationCache($authorization);

        // Revoke associated access tokens
        $authorization->client->tokens()
                              ->where('user_uuid', $authorization->user_uuid)
                              ->where('is_revoked', Client::IS_REVOKED_NO)
                              ->each(function ($token) {
                                  $token->revoke();
                                  if ($refreshToken = $token->refreshToken) {
                                      $refreshToken->revoke();
                                  }
                                  OAuthCacher::invalidateAccessToken($token->id);
                              });

        return Respond::success();
    }

    private function invalidateAuthorizationCache(OAuthUserAuthorization $authorization) {
        OAuthCacher::invalidateUserAuthorization($authorization->user_uuid, $authorization->client_id);
        OAuthCacher::invalidateUserBinding($authorization->user_uuid, $authorization->client_id);
    }
}
