<?php

use App\Http\AdminControllers\{
    O<PERSON>uthClient<PERSON><PERSON>roller,
    <PERSON>r<PERSON><PERSON><PERSON>zation<PERSON><PERSON>roller,
    Users<PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ontroller,
    <PERSON>min<PERSON>sers<PERSON>ontroller,
    Auth<PERSON>ontroller,
    <PERSON>minDepartmentsController,
    Roles<PERSON>ontroller,
    PermissionsController,
    AdminProfileController,
    AdminOAuthClientsController,
    AdminA<PERSON>licationsController,
    MaterialsController,
    OAuthController,
    OAuthClientAccessController,
    OAuthClientCategoryController,
    AdminPushMessageController,
    AdminRoutesController,
    ActivityLogsController,
};

use App\Http\Enterprise\{
    EnterpriseController,
    EnterpriseAdminController,
    EnterpriseDepartmentController,
    EnterpriseClientController,
};

/*
|--------------------------------------------------------------------------
| 管理后台路由配置
|--------------------------------------------------------------------------
|
| 这些路由由 RouteServiceProvider 自动加载
| 已在 app.php 中统一配置了 admin/api 前缀和 auth:admin 中间件
|
*/

/*
|--------------------------------------------------------------------------
| 认证相关路由
|--------------------------------------------------------------------------
*/

Route::prefix('auth')
    ->group(function () {
        Route::delete('logout', [AuthController::class, 'logout']);   // 退出登录
    });

/*
|--------------------------------------------------------------------------
| 个人资料管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('profile')
    ->group(function () {
        Route::get('/', [AdminProfileController::class, 'show']);             // 获取个人资料
        Route::put('/', [AdminProfileController::class, 'update']);           // 更新个人资料
        Route::put('password', [AdminProfileController::class, 'updatePassword']);  // 修改密码
        Route::get('bind-user-qr', [AdminProfileController::class, 'getWaitBindingUsers']);  // 前端相关用户列表和绑定码
        Route::post('bind-user', [AdminProfileController::class, 'bindWithUser']); // 绑定前端用户
        Route::delete('unbind-user', [AdminProfileController::class, 'unbindWithUser']); // 解绑前端用户
    });

/*
|--------------------------------------------------------------------------
| OAuth客户端管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('oauth-clients')
    ->group(function () {
        // 客户端分类管理
        Route::prefix('categories')
            ->group(function () {
                Route::get('/', [OAuthClientCategoryController::class, 'index']); // 获取分类列表
                Route::get('options', [OAuthClientCategoryController::class, 'options']); // 获取分类选项
                Route::get('{category}', [OAuthClientCategoryController::class, 'show']); // 获取分类详情
                Route::post('/', [OAuthClientCategoryController::class, 'store']); // 创建分类
                Route::put('{category}', [OAuthClientCategoryController::class, 'update']); // 更新分类
                Route::delete('{category}', [OAuthClientCategoryController::class, 'destroy']); // 删除分类
            });

        // 基础管理
        Route::get('options', [OAuthClientController::class, 'options']);         // 获取选项列表
        Route::get('/', [OAuthClientController::class, 'index']);                 // 获取客户端列表
        Route::get('/workspace/clients', [OAuthClientController::class, 'workspaceClients']);         // 获取选项列表
        Route::post('/', [OAuthClientController::class, 'store']);                // 创建客户端
        Route::get('{client}', [OAuthClientController::class, 'show']);          // 获取客户端详情
        Route::put('{client}', [OAuthClientController::class, 'update']);        // 更新客户端
        Route::put('{client}/revoke', [OAuthClientController::class, 'revoke']);    // 禁用客户端
        Route::get('{client}/show-keys', [OAuthClientController::class, 'getKeys']);     // 查看应用密钥
        Route::put('{client}/reset-keys', [OAuthClientController::class, 'resetKeys']);   // 重置应用密钥

        // 管理员客户端关联
        Route::prefix('admin/{uuid}')
            ->group(function () {
                Route::get('/', [AdminOAuthClientsController::class, 'getAdminClients']);      // 获取管理员关联的客户端
                Route::post('/assign', [AdminOAuthClientsController::class, 'assignClients']); // 分配客户端
                Route::post('/remove', [AdminOAuthClientsController::class, 'removeClients']); // 移除客户端
            });

        // 访问控制
        Route::prefix('accessible')
            ->group(function () {
                Route::get('current-user/clients', [OAuthClientAccessController::class, 'getCurrentAdminAccessibleClients']);
                Route::get('admin/{adminUuid}/clients', [OAuthClientAccessController::class, 'getAdminAccessibleClients']);
                Route::get('department/{departmentId}/clients', [OAuthClientAccessController::class, 'getDepartmentAccessibleClients']);
                Route::get('client/{clientId}/admins', [OAuthClientAccessController::class, 'getClientAccessibleAdmins']);
                Route::get('client/{clientId}/departments', [OAuthClientAccessController::class, 'getClientAccessibleDepartments']);

                // 权限分配类接口
                Route::post('admin/assign-clients', [OAuthClientAccessController::class, 'assignClientsToAdmin']);
                Route::post('department/assign-clients', [OAuthClientAccessController::class, 'assignClientsToDepartment']);
                Route::post('client/assign-admins', [OAuthClientAccessController::class, 'assignAdminsToClient']);
                Route::post('client/assign-departments', [OAuthClientAccessController::class, 'assignDepartmentsToClient']);

                // 权限移除类接口
                Route::delete('admin/remove-client', [OAuthClientAccessController::class, 'removeClientFromAdmin']);
                Route::delete('department/remove-client', [OAuthClientAccessController::class, 'removeClientFromDepartment']);
            });
    });

/*
|--------------------------------------------------------------------------
| 应用管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('apps')
    ->group(function () {
        Route::get('/', [AdminApplicationsController::class, 'index']); // 获取可管理的客户端列表
        Route::get('/options', [AdminApplicationsController::class, 'options']); // 获取选项列表
        Route::get('{clientId}', [AdminApplicationsController::class, 'show']);       // 获取应用详情
        Route::put('{clientId}', [AdminApplicationsController::class, 'updateClient']);     // 更新应用信息
        Route::get('{clientId}/show-keys', [AdminApplicationsController::class, 'getKeys']);     // 查看应用密钥
        Route::put('{clientId}/reset-keys', [AdminApplicationsController::class, 'resetKeys']);   // 重置应用密钥
        Route::get('{clientId}/templates', [AdminApplicationsController::class, 'availableTemplates']);  // 获取可用模板

        // 订阅管理
        Route::prefix('{clientId}/subscriptions')
            ->group(function () {
                Route::get('/', [AdminApplicationsController::class, 'subscriptions']);    // 获取订阅列表
                Route::post('/', [AdminApplicationsController::class, 'createSubscription']);   // 创建订阅
                Route::put('{subscriptionId}', [AdminApplicationsController::class, 'updateSubscription']);  // 更新订阅
                Route::delete('{subscriptionId}', [AdminApplicationsController::class, 'deleteSubscription']);   // 删除订阅
                Route::put('{subscriptionId}/status', [AdminApplicationsController::class, 'toggleSubscriptionStatus']);    // 切换订阅状态
            });
    });

/*
|--------------------------------------------------------------------------
| 用户授权管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('user-authorizations')
    ->group(function () {
        Route::get('/', [UserAuthorizationController::class, 'index']);           // 获取授权列表
        Route::get('{authorization}', [UserAuthorizationController::class, 'show']);   // 获取授权详情
        Route::delete('{authorization}', [UserAuthorizationController::class, 'revoke']);   // 撤销授权
    });

/*
|--------------------------------------------------------------------------
| 推送模板管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('push-templates')
    ->group(function () {
        // 模板管理
        Route::get('/', [PushTemplateController::class, 'index']);               // 获取模板列表
        Route::get('options', [PushTemplateController::class, 'options']);       // 获取模板选项
        Route::post('/', [PushTemplateController::class, 'store']);              // 创建模板
        Route::get('{template}', [PushTemplateController::class, 'show']);       // 获取模板详情
        Route::put('{template}', [PushTemplateController::class, 'update']);     // 更新模板
        Route::delete('{template}', [PushTemplateController::class, 'destroy']); // 删除模板

        // 客户端模板管理
        Route::prefix('client/{clientId}')
            ->group(function () {
                Route::get('templates', [AdminOAuthClientsController::class, 'getClientTemplates']);     // 获取客户端模板
                Route::post('templates', [AdminOAuthClientsController::class, 'assignTemplates']);       // 分配模板
                Route::put('templates/{templateId}/status', [
                    AdminOAuthClientsController::class,
                    'toggleTemplateStatus',
                ]);  // 切换模板状态
                Route::delete('templates/{templateId}', [
                    AdminOAuthClientsController::class,
                    'removeTemplate',
                ]);   // 移除模板
            });
    });

/*
|--------------------------------------------------------------------------
| 用户管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('users')
    ->group(function () {
        Route::get('options', [UsersController::class, 'options']);              // 获取用户选项
        Route::get('wait-confirmed', [UsersController::class, 'waitConfirmedUsers']); // 获取待确认用户列表
        Route::put('batch-confirm', [UsersController::class, 'batchConfirm']);       // 批量确认
        Route::get('/', [UsersController::class, 'index']);                      // 获取用户列表
        Route::post('/', [UsersController::class, 'store']);                     // 创建用户
        Route::get('{uuid}', [UsersController::class, 'show']);                 // 获取用户详情
        Route::put('{uuid}', [UsersController::class, 'update']);               // 更新用户信息
        Route::put('{uuid}/update-status', [UsersController::class, 'updateStatus']);   // 更新用户状态
        // Route::put('{uuid}/confirm-nickname', [UsersController::class, 'confirmNickname']);   // 确认昵称
        // Route::put('{uuid}/confirm-avatar', [UsersController::class, 'confirmAvatar']);       // 确认头像
        // Route::put('{uuid}/confirm-comment', [UsersController::class, 'confirmComment']);       // 确认签名

        // 新的用户信息审核路由
        Route::get('user-changes/pending', [UsersController::class, 'getPendingChanges'])->name('admin.user.changes.pending');
        Route::put('{uuid}/approve-change', [UsersController::class, 'approveChange'])->name('admin.user.changes.approve');
        Route::put('{uuid}/reject-change', [UsersController::class, 'rejectChange'])->name('admin.user.changes.reject');
        Route::post('batch-approve-changes', [UsersController::class, 'batchApproveChanges'])->name('admin.user.changes.batchApprove');
    });

/*
|--------------------------------------------------------------------------
| 管理员管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('admin-users')
    ->group(function () {
        Route::get('options', [AdminUsersController::class, 'options']);         // 获取管理员选项
        Route::get('/', [AdminUsersController::class, 'index']);                 // 获取管理员列表
        Route::post('/', [AdminUsersController::class, 'store']);                // 创建管理员
        Route::get('{uuid}', [AdminUsersController::class, 'show']);            // 获取管理员详情
        Route::put('{uuid}', [AdminUsersController::class, 'update']);          // 更新管理员信息
        Route::delete('{uuid}', [AdminUsersController::class, 'destroy']);      // 删除管理员
        Route::put('{uuid}/update-status', [AdminUsersController::class, 'changeStatus']);  // 更新管理员状态
        Route::put('batch/update-status', [AdminUsersController::class, 'batchChangeStatus']);  // 批量更新管理员状态
    });

/*
|--------------------------------------------------------------------------
| 部门管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('admin-departments')
    ->group(function () {
        // 部门基础管理
        Route::get('options', [AdminDepartmentsController::class, 'options']);    // 获取部门选项
        Route::get('/', [AdminDepartmentsController::class, 'index']);            // 获取部门列表
        Route::post('/', [AdminDepartmentsController::class, 'store']);           // 创建部门
        Route::get('{id}', [AdminDepartmentsController::class, 'show']);         // 获取部门详情
        Route::put('{id}', [AdminDepartmentsController::class, 'update']);       // 更新部门信息
        Route::delete('{id}', [AdminDepartmentsController::class, 'destroy']);   // 删除部门
        Route::put('{id}/move', [AdminDepartmentsController::class, 'move']);    // 移动部门

        // 部门人员管理
        Route::post('{id}/leaders', [AdminDepartmentsController::class, 'setLeaders']);    // 设置部门领导
        Route::post('{id}/members', [AdminDepartmentsController::class, 'addMembers']);    // 添加部门成员
        Route::delete('{id}/members', [AdminDepartmentsController::class, 'removeMembers']); // 移除部门成员
    });

/*
|--------------------------------------------------------------------------
| 访问控制管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('acl')
    ->group(function () {
        // 角色管理
        Route::prefix('roles')
            ->group(function () {
                Route::get('options', [RolesController::class, 'options']);          // 获取角色选项
                Route::get('/', [RolesController::class, 'index']);                  // 获取角色列表
                Route::post('/', [RolesController::class, 'store']);                 // 创建角色
                Route::get('{id}', [RolesController::class, 'show']);               // 获取角色详情
                Route::put('{id}', [RolesController::class, 'update']);             // 更新角色
                Route::delete('{id}', [RolesController::class, 'destroy']);         // 删除角色
                Route::get('{id}/permissions', [PermissionsController::class, 'getRolePermissions']);  // 获取角色权限
            });

        // 权限管理
        Route::prefix('permissions')
            ->group(function () {
                Route::get('options', [PermissionsController::class, 'options']);     // 获取权限选项
                Route::get('tree', [PermissionsController::class, 'tree']);          // 获取权限树
                Route::get('/', [PermissionsController::class, 'index']);            // 获取权限列表
                Route::post('/', [PermissionsController::class, 'store']);           // 创建权限
                Route::get('{id}', [PermissionsController::class, 'show']);         // 获取权限详情
                Route::put('{id}', [PermissionsController::class, 'update']);       // 更新权限
                Route::delete('{id}', [PermissionsController::class, 'destroy']);   // 删除权限
            });
    });

/*
|--------------------------------------------------------------------------
| 媒体管理路由
|--------------------------------------------------------------------------
*/

Route::prefix('materials')
    ->group(function () {
        Route::get('policy', [MaterialsController::class, 'policy']);
    });

/*
|--------------------------------------------------------------------------
| OAuth2.0 认证路由
|--------------------------------------------------------------------------
*/
Route::prefix('oauth2')
    ->group(function () {
        Route::get('check-auth-status', [OAuthController::class, 'checkAuthStatus']);     // 检查授权状态
        Route::post('authorize', [OAuthController::class, 'authorize']);                  // 确认授权
    });

/*
|--------------------------------------------------------------------------
| 企业管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('enterprises')
    ->group(function () {
        Route::get('/', [EnterpriseController::class, 'index']);            // 获取企业列表
        Route::post('/', [EnterpriseController::class, 'store']);           // 创建企业
        Route::get('{id}', [EnterpriseController::class, 'show']);         // 获取企业详情
        Route::put('{id}', [EnterpriseController::class, 'update']);       // 更新企业信息
        Route::delete('{id}', [EnterpriseController::class, 'destroy']);   // 删除企业

        // 企业管理员关联
        Route::get('{id}/admin-users', [EnterpriseController::class, 'adminUsers']);          // 获取企业下的管理员
        Route::post('{id}/admin-users/assign', [EnterpriseController::class, 'assignAdminUsers']);    // 关联管理员
        Route::post('{id}/admin-users/remove', [EnterpriseController::class, 'removeAdminUsers']);    // 移除管理员

        // 企业部门关联
        Route::get('{id}/departments', [EnterpriseController::class, 'departments']);          // 获取企业下的部门
        Route::post('{id}/departments/assign', [EnterpriseController::class, 'assignDepartments']);    // 关联部门
        Route::post('{id}/departments/remove', [EnterpriseController::class, 'removeDepartments']);    // 移除部门

        // 企业应用关联
        Route::get('{id}/clients', [EnterpriseController::class, 'clients']);                 // 获取企业关联的应用
        Route::post('{id}/clients/assign', [EnterpriseController::class, 'assignClient']);     // 关联应用
        Route::post('{id}/clients/remove', [EnterpriseController::class, 'removeClient']);     // 移除应用
    });

/*
|--------------------------------------------------------------------------
| 活动日志管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('activity-logs')
    ->group(function () {
        Route::get('/', [ActivityLogsController::class, 'index']);                // 获取日志列表
        Route::get('/model', [ActivityLogsController::class, 'modelLogs']);       // 获取指定模型的日志
        Route::get('/options', [ActivityLogsController::class, 'logOptions']);    // 获取日志类型选项
    });

/*
|--------------------------------------------------------------------------
| 路由管理路由
|--------------------------------------------------------------------------
*/
Route::prefix('settings')->group(function () {
    Route::get('/routes', [AdminRoutesController::class, 'index']);
    Route::get('/routes/{id}', [AdminRoutesController::class, 'show']);
    Route::post('/routes', [AdminRoutesController::class, 'store']);
    Route::put('/routes/{id}', [AdminRoutesController::class, 'update']);
    Route::delete('/routes/{id}', [AdminRoutesController::class, 'destroy']);
});
/*
|--------------------------------------------------------------------------
| 推送消息管理
|--------------------------------------------------------------------------
*/
Route::prefix('push')->name('push.')->group(function () {
    // 系统级推送给个人
    Route::post('/to-user', [AdminPushMessageController::class, 'pushToUser'])->name('to-user');
    
    // 系统级广播消息
    Route::post('/broadcast', [AdminPushMessageController::class, 'broadcast'])->name('broadcast');
    
    // 应用级推送给个人
    Route::post('/client/to-user', [AdminPushMessageController::class, 'pushByClient'])->name('client-to-user');
    
    // 创建应用订阅
    Route::post('/subscription', [AdminPushMessageController::class, 'createSubscription'])->name('create-subscription');
    
    // 触发订阅推送
    Route::post('/subscription/trigger', [AdminPushMessageController::class, 'triggerSubscription'])->name('trigger-subscription');
});
