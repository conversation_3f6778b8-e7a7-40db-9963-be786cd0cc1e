<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Facades\AdminPermission;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\RoleResource;
use App\Models\Permission;
use App\Services\PermissionGroupService;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Role;

class RolesController extends AdminBaseController
{
    protected $validationMessages = [
        'name.required'        => '角色名称不能为空',
        'name.string'          => '角色名称必须是字符串',
        'name.max'             => '角色名称不能超过50个字符',
        'name.unique'          => '角色名称已存在',
        'permissions.required' => '权限不能为空',
        'permissions.array'    => '权限必须是数组',
        'permissions.*.exists' => '选择的权限不存在',
        'permissions.*.string' => '权限标识必须是字符串',
        'guard_name.required'  => '守卫名称不能为空',
        'guard_name.string'    => '守卫名称必须是字符串',
    ];

    protected const PERMISSION_MAP = [
        'index'   => '角色管理.查看列表',
        'show'    => '角色管理.查看详情',
        'store'   => '角色管理.创建',
        'update'  => '角色管理.编辑',
        'destroy' => '角色管理.删除',
    ];

    /**
     * 角色列表
     */
    public function index(Request $request) {
        $query = Role::query()
                     ->where('guard_name', 'admin')
            //                     ->with(['permissions:id,name,description'])
                     ->when($request->input('keyword'), function ($query, $keyword) {
                $query->where('name', 'like', "%{$keyword}%");
            });

        $roles = $query->orderByDesc('id')
                       ->paginate();

        return Respond::success(RoleResource::collection($roles));
    }

    /**
     * 角色详情
     */
    public function show($id) {
        $role = Role::findOrFail($id);

        return Respond::success(RoleResource::make($role));
    }

    /**
     * 获取角色管理相关选项
     */
    public function options() {
        return Respond::success([
            'all_permissions' => app(PermissionGroupService::class)->getPermissionTree(),
            'roles'           => Role::where('guard_name', 'admin')
                                     ->select([
                                         'id',
                                         'name',
                                     ])
                                     ->orderByDesc('id')
                                     ->get(),
        ]);
    }

    /**
     * 创建角色
     *
     * @throws AdminException
     */
    public function store(Request $request) {
        $validated = $request->validate([
            'name'          => 'required|string|max:50|unique:roles',
            'permissions'   => 'required|array',
            'permissions.*' => 'sometimes|exists:' . Permission::class . ',id',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $role = Role::create([
                'name'       => $validated['name'],
                'guard_name' => 'admin',
            ]);

            // 为角色分配权限
            $role->syncPermissions(array_map('intval', $validated['permissions']));

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 更新角色
     *
     * @throws AdminException
     */
    public function update(Request $request, $id) {
        $role = Role::findOrFail($id);

        $validated = $request->validate([
            'name'          => 'required|string|max:50|unique:roles,name,' . $id,
            'permissions'   => 'required|array',
            'permissions.*' => 'sometimes|exists:' . Permission::class . ',id',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $role->update(['name' => $validated['name']]);

            // 同步权限
            $role->syncPermissions(array_map('intval', $validated['permissions']));

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 删除角色
     *
     * @throws AdminException|\Throwable
     */
    public function destroy($id) {
        $role = Role::findOrFail($id);

        if ($role->id == 1) {
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [], '超级管理员角色无法删除');
        }
        // 检查是否有管理员使用此角色
        if ($role->users()
                 ->exists()) {
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [], '该角色正在使用中，无法删除');
        }

        try {
            DB::beginTransaction();

            // 删除角色的所有权限关联
            $role->syncPermissions([]);
            // 删除角色
            $role->delete();

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 获取角色的权限树
     */
    public function permissions($id) {
        return Respond::success(AdminPermission::getRolePermissions($id));
    }

    /**
     * 设置角色权限
     *
     * @throws AdminException
     */
    public function setPermissions(Request $request, $id) {
        $role = Role::findOrFail($id);

        $validated = $request->validate([
            'permissions'   => 'required|array',
            'permissions.*' => 'string',
            // 权限名称数组
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $role->syncPermissions($validated['permissions']);

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 获取所有角色（用于下拉选择）
     */
    public function all() {
        $roles = Role::where('guard_name', 'admin')
                     ->select([
                         'id',
                         'name',
                     ])
                     ->orderByDesc('id')
                     ->get();

        return Respond::success($roles);
    }
}
