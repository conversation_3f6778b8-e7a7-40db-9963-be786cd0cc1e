<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $uuid uuid
 * @property string|null $name 名称
 * @property string $path 路径
 * @property string $provider 提供商
 * @property string|null $format 格式
 * @property string|null $mini_type 类型
 * @property string|null $width 宽度
 * @property string|null $height 高度
 * @property string|null $size 大小
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereFormat($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereMiniType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Material whereWidth($value)
 * @mixin \Eloquent
 * @mixin IdeHelperMaterial
 */
class Material extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'name',
        'path',
        'provider',
        'format',
        'mini_type',
        'width',
        'height',
        'size',
    ];
}
