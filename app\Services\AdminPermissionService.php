<?php

namespace App\Services;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Models\Permission;
use App\Models\Role;
use App\Models\AdminUser;
class AdminPermissionService
{
    /**
     * 默认Guard
     * 
     * @var string
     */
    protected string $defaultGuard = 'admin';

    /**
     * 设置默认Guard
     * 
     * @param string $guard
     * @return $this
     */
    public function setDefaultGuard(string $guard): self
    {
        $this->defaultGuard = $guard;
        return $this;
    }

    /**
     * 获取当前用户
     * 
     * @param string|null $guard
     * @return mixed
     */
    public function getUser(?string $guard = null)
    {
        return Auth::guard($guard ?? $this->defaultGuard)->user();
    }

    /**
     * 检查是否有权限
     * 
     * @param string $permission 权限名称
     * @param string|null $guard Guard名称
     * @return bool
     */
    public function can(string $permission, ?string $guard = null): bool {
        $user = $this->getUser($guard);
        if (!$user) {
            return false;
        }

        // 管理员ID为1的拥有所有权限
        if ($user->id === 1) {
            return true;
        }

        if ($user->checkPermissionTo($permission)) {
            return true;
        }

        // 检查部门权限（如果用户有departments方法）
        if (method_exists($user, 'departments')) {
            return $user->departments()
                       ->get()
                       ->contains(function ($department) use ($permission) {
                           return $department->can($permission);
                       });
        }

        return false;
    }

    /**
     * 检查是否没有权限
     * 
     * @param string $permission 权限名称
     * @param string|null $guard Guard名称
     * @return bool
     */
    public function cant(string $permission, ?string $guard = null): bool {
        return !$this->can($permission, $guard);
    }

    /**
     * 检查是否有任意一个权限
     * 
     * @param array $permissions 权限列表
     * @param string|null $guard Guard名称
     * @return bool
     */
    public function any(array $permissions, ?string $guard = null): bool {
        foreach ($permissions as $permission) {
            if ($this->can($permission, $guard)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否有全部权限
     * 
     * @param array $permissions 权限列表
     * @param string|null $guard Guard名称
     * @return bool
     */
    public function all(array $permissions, ?string $guard = null): bool {
        foreach ($permissions as $permission) {
            if (!$this->can($permission, $guard)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取所有权限
     */
    public function getAllPermissions(): Collection {
        return Permission::orderBy('name')
                         ->get();
    }

    /**
     * 获取权限树
     */
    public function getPermissionTree(): array {
        $permissions = $this->getAllPermissions();
        $tree = [];

        foreach ($permissions as $permission) {
            $parts = explode('.', $permission->name);
            $current = &$tree;

            foreach ($parts as $index => $part) {
                if (!isset($current[$part])) {
                    $current[$part] = [
                        'title' => $part,
                        'key'   => implode('.', array_slice($parts, 0, $index + 1)),
                    ];

                    if ($index === count($parts) - 1) {
                        $current[$part]['id'] = $permission->id;
                        $current[$part]['value'] = $permission->name;
                        $current[$part]['description'] = $permission->description;
                    } else {
                        $current[$part]['children'] = [];
                    }
                }
                $current = &$current[$part]['children'];
            }
        }

        return $this->formatTree(array_values($tree));
    }

    /**
     * 获取分组后的权限
     */
    public function getGroupedPermissions(): array {
        $permissions = $this->getAllPermissions();
        $groups = [];

        foreach ($permissions as $permission) {
            $module = explode('.', $permission->name)[0];
            $groups[$module][] = [
                'name'        => $permission->name,
                'description' => $permission->description,
                'actions'     => array_slice(explode('.', $permission->name), 1),
            ];
        }

        return $groups;
    }

    /**
     * 获取所有模块
     */
    public function getModules(): array {
        return Permission::query()
                         ->get()
                         ->map(function ($permission) {
                             return explode('.', $permission->name)[0];
                         })
                         ->unique()
                         ->values()
                         ->toArray();
    }

    /**
     * 获取角色的权限
     */
    public function getRolePermissions(int $roleId): array {
        $role = Role::findOrFail($roleId);
        $rolePermissions = $role->permissions->pluck('name')
                                             ->toArray();

        $tree = $this->getPermissionTree();

        return $this->markSelectedPermissions($tree, $rolePermissions);
    }

    /**
     * 评估权限表达式
     * 支持: 单个权限、OR(|)、AND(&)、分组()
     * 
     * @param string $expression 权限表达式
     * @param string|null $guard Guard名称
     * @return bool
     */
    public function evaluateExpression(string $expression, ?string $guard = null): bool {
        // 移除空格
        $expression = str_replace(' ', '', $expression);

        // 处理括号
        while (preg_match('/\((([^()]+))\)/', $expression, $matches)) {
            $result = $this->evaluateExpression($matches[1], $guard);
            $expression = str_replace($matches[0], $result ? 'true' : 'false', $expression);
        }

        // 处理AND
        if (str_contains($expression, '&')) {
            $parts = explode('&', $expression);
            foreach ($parts as $part) {
                if ($part === 'false' || !$this->evaluateExpression($part, $guard)) {
                    return false;
                }
            }

            return true;
        }

        // 处理OR
        if (str_contains($expression, '|')) {
            $parts = explode('|', $expression);
            foreach ($parts as $part) {
                if ($part === 'true' || $this->evaluateExpression($part, $guard)) {
                    return true;
                }
            }

            return false;
        }

        // 单个权限
        return $expression === 'true' || $this->can($expression, $guard);
    }

    /**
     * 格式化权限树
     */
    private function formatTree(array $tree): array {
        $result = [];
        foreach ($tree as $node) {
            $formattedNode = [
                'title' => $node['title'],
                'key'   => $node['key'],
            ];

            if (isset($node['value'])) {
                $formattedNode['id'] = $node['id'];
                $formattedNode['value'] = $node['value'];
                $formattedNode['description'] = $node['description'];
            }

            if (isset($node['children'])) {
                $formattedNode['children'] = $this->formatTree(array_values($node['children']));
            }

            $result[] = $formattedNode;
        }

        return $result;
    }

    /**
     * 标记选中的权限
     */
    private function markSelectedPermissions(array $tree, array $selectedPermissions): array {
        foreach ($tree as &$node) {
            if (isset($node['value'])) {
                $node['selected'] = in_array($node['value'], $selectedPermissions);
            }
            if (isset($node['children'])) {
                $node['children'] = $this->markSelectedPermissions($node['children'], $selectedPermissions);
                // 如果所有子节点都被选中，则父节点也被选中
                $node['selected'] = isset($node['children']) && count($node['children']) > 0 && count(array_filter($node['children'], fn($child) => $child['selected'])) === count($node['children']);
            }
        }

        return $tree;
    }

    /**
     * 检查指定用户是否有权限
     * 
     * @param mixed $user 用户ID或用户对象
     * @param string $permission 权限名称
     * @param string|null $guard Guard名称
     * @return bool
     * @throws AdminException
     */
    public function whoCan($user, string $permission, ?string $guard = null): bool {
        // 如果是UUID，尝试查找用户
        if (Str::isUuid($user)) {
            $userClass = $guard === 'admin' || $guard === null ? AdminUser::class : config("auth.guards.{$guard}.provider");
            $user = $userClass::find($user);
            if (!$user) {
                return false;
            }
        }

        // 用户ID为1的管理员拥有所有权限
        if (method_exists($user, 'getAuthIdentifier') && $user->getAuthIdentifier() === 1) {
            return true;
        }

        // 检查用户权限
        if (method_exists($user, 'checkPermissionTo') && $user->checkPermissionTo($permission)) {
            return true;
        }

        // 检查部门权限（如果用户有departments方法）
        if (method_exists($user, 'departments')) {
            return $user->departments()
                       ->get()
                       ->contains(function ($department) use ($permission) {
                           return $department->can($permission);
                       });
        }

        return false;
    }
}
