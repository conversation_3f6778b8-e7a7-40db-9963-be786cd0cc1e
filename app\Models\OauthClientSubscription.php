<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $subscription_code 订阅码
 * @property string $oauth_client_id OAuth Client ID
 * @property string $name 订阅名称
 * @property string|null $description 订阅描述
 * @property int $push_template_id 使用的模板ID
 * @property array<array-key, mixed>|null $filter_conditions 订阅过滤条件
 * @property int $status 状态:1启用,0禁用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Client|null $client
 * @property-read \App\Models\PushTemplate|null $pushTemplate
 * @property-read mixed $status_text
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\UserSubscription> $userSubscriptions
 * @property-read int|null $user_subscriptions_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereFilterConditions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereOauthClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription wherePushTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereSubscriptionCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OauthClientSubscription withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperOauthClientSubscription
 */
class OauthClientSubscription extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'subscription_code',
        'oauth_client_id',
        'name',
        'description',
        'push_template_id',
        'filter_conditions',
        'status',
    ];

    protected $casts = [
        'filter_conditions' => 'array',
    ];

    protected $appends = [
        'status_text',
    ];

    const STATUS_DISABLE = 0;
    const STATUS_ENABLE = 1;

    public static array $statusMap = [
        self::STATUS_ENABLE  => '启用',
        self::STATUS_DISABLE => '禁用',
    ];

    protected function statusText(): Attribute {
        return Attribute::make(get: fn($value) => self::$statusMap[$value] ?? '未知');
    }

    public function pushTemplate(): BelongsTo {
        return $this->belongsTo(PushTemplate::class);
    }

    public function userSubscriptions(): HasMany {
        return $this->hasMany(UserSubscription::class);
    }

    public function client() {
        return $this->belongsTo(Client::class, 'oauth_client_id', 'id');
    }
}
