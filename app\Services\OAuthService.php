<?php

namespace App\Services;

use App\Models\AdminDepartment;
use App\Models\AdminUser;
use App\Models\Client;
use App\Models\Enterprise;
use Illuminate\Support\Collection;

class OAuthService
{
    /**
     * 获取应用关联的企业下的部门列表
     *
     * @param int $clientId OAuth客户端ID
     * @return Collection 部门列表
     */
    public function getClientEnterpriseDepartments(int $clientId): Collection
    {
        $client = Client::findOrFail($clientId);
        
        // 如果客户端未关联企业，则返回空集合
        if (!$client->enterprise_id) {
            return collect();
        }
        
        // 从企业获取部门列表
        return AdminDepartment::where('enterprise_id', $client->enterprise_id)
            ->where('status', AdminDepartment::STATUS_ENABLED)
            ->get();
    }
    
    /**
     * 获取应用关联的企业下的管理员用户列表
     *
     * @param int $clientId OAuth客户端ID
     * @return Collection 管理员用户列表
     */
    public function getClientEnterpriseAdminUsers(int $clientId): Collection
    {
        $client = Client::findOrFail($clientId);
        
        // 如果客户端未关联企业，则返回空集合
        if (!$client->enterprise_id) {
            return collect();
        }
        
        // 从企业获取管理员用户列表
        return AdminUser::where('enterprise_id', $client->enterprise_id)
            ->where('status', AdminUser::STATUS_ENABLED)
            ->get();
    }
    
    /**
     * 检查OAuth客户端是否关联了企业
     *
     * @param int $clientId OAuth客户端ID
     * @return bool 是否关联企业
     */
    public function clientHasEnterprise(int $clientId): bool
    {
        $client = Client::findOrFail($clientId);
        return !empty($client->enterprise_id);
    }
    
    /**
     * 获取OAuth客户端关联的企业
     *
     * @param int $clientId OAuth客户端ID
     * @return Enterprise|null 企业实例
     */
    public function getClientEnterprise(int $clientId): ?Enterprise
    {
        $client = Client::findOrFail($clientId);
        
        if (!$client->enterprise_id) {
            return null;
        }
        
        return Enterprise::find($client->enterprise_id);
    }
} 