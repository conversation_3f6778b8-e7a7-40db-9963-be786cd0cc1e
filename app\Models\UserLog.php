<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string|null $device_id
 * @property string|null $ip
 * @property int $state
 * @property string|null $type
 * @property string|null $ua
 * @property string|null $user_id
 * @property string|null $username
 * @property string|null $mobile
 * @property string|null $openid
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereDeviceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereOpenid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereUa($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserLog whereUsername($value)
 * @mixin \Eloquent
 * @mixin IdeHelperUserLog
 */
class UserLog extends Model
{
    //
}
