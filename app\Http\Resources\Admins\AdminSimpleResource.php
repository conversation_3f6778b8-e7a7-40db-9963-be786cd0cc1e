<?php

namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class AdminSimpleResource
 *
 * @property-read \App\Models\AdminUser $resource
 * @mixin \App\Models\AdminUser
 */
class AdminSimpleResource extends JsonResource
{
    public function toArray(Request $request): array {
        return [
            'uuid'           => $this->uuid,
            'username'       => $this->username,
            'nickname'       => $this->nickname,
            'true_name'      => $this->true_name,
            'display_avatar' => $this->display_avatar,
            'is_leader'      => $this->whenPivotLoaded('admin_department_admin_user', function () {
                return $this->pivot->is_leader;
            }),
        ];
    }
}
