<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @property string $id
 * @property string $enterprise_id 所属企业ID
 * @property string|null $department_id 所属部门ID
 * @property bool|null $is_leader 是否为部门负责人
 * @property string $name 联系人姓名
 * @property string|null $display_name 显示姓名
 * @property string|null $mobile 手机号
 * @property string|null $short_mobile 短号
 * @property string|null $office_phone 办公电话
 * @property string|null $title 职位
 * @property string|null $email 邮箱
 * @property string|null $remark 备注
 * @property int $status 状态:1启用,0禁用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\EnterpriseDepartment|null $department
 * @property-read \App\Models\Enterprise|null $enterprise
 * @property-read mixed $is_leader_text
 * @property-read mixed $status_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereDepartmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereDisplayName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereEnterpriseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereIsLeader($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereOfficePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereShortMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EnterpriseContact withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperEnterpriseContact
 */
class EnterpriseContact extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        'enterprise_id',
        'department_id',
        'is_leader',
        'name',
        'display_name',
        'mobile',
        'short_mobile',
        'office_phone',
        'title',
        'email',
        'remark',
        'status',
    ];

    protected $casts = [
        'is_leader' => 'boolean',
    ];

    protected $appends = [
        'status_text',
    ];

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    public static array $statusMap = [
        self::STATUS_ENABLED  => '启用',
        self::STATUS_DISABLED => '禁用',
    ];

    const IS_LEADER_NO = 0;
    const IS_LEADER_YES = 1;

    public static array $isLeaderMap = [
        self::IS_LEADER_YES => '是',
        self::IS_LEADER_NO  => '否',
    ];

    /**
     * 状态文本属性
     */
    protected function statusText(): Attribute
    {
        return Attribute::make(get: fn() => self::$statusMap[$this->status] ?? '未知');
    }

    /**
     * 是否领导文本属性
     */
    protected function isLeaderText(): Attribute
    {
        return Attribute::make(get: fn() => self::$isLeaderMap[$this->is_leader] ?? '未知');
    }

    /**
     * 关联企业
     */
    public function enterprise()
    {
        return $this->belongsTo(Enterprise::class, 'enterprise_id', 'id');
    }

    /**
     * 关联部门
     */
    public function department()
    {
        return $this->belongsTo(EnterpriseDepartment::class, 'department_id', 'id');
    }
}
