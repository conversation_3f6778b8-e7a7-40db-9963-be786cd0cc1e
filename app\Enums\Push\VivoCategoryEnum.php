<?php

namespace App\Enums\Push;

enum VivoCategoryEnum: string
{
    case ACCOUNT = 'ACCOUNT';           // 账号类消息
    case NEWS = 'NEWS';                 // 新闻资讯类消息
    case SOCIAL = 'SOCIAL';             // 社交类消息
    case MARKETING = 'MARKETING';       // 营销类消息 
    case SUBSCRIPTION = 'SUBSCRIPTION'; // 订阅类消息

    public function label(): string {
        return match ($this) {
            self::ACCOUNT => '账号类消息',
            self::NEWS => '新闻资讯类消息',
            self::SOCIAL => '社交类消息',
            self::MARKETING => '营销类消息',
            self::SUBSCRIPTION => '订阅类消息',
            default => '',
        };
    }

    public static function options(): array {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }
}