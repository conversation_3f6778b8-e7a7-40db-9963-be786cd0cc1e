<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('user_devices', function (Blueprint $table) {
            $table->id();
            $table->uuid('user_uuid');
            $table->string('device_id');
            $table->string('device_token');
            $table->tinyInteger('platform_type')
                  ->comment('1:android,2:ios,7:鸿蒙');
            $table->string('device_type')
                  ->nullable();
            $table->string('app_id')
                  ->nullable();
            $table->string('client_type')
                  ->nullable();
            $table->string('client_version')
                  ->nullable();
            $table->string('device_os')
                  ->nullable();
            $table->string('device_os_version')
                  ->nullable();
            $table->unsignedTinyInteger('is_active')
                  ->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('user_devices');
    }
};
