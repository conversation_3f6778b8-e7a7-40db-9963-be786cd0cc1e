<?php

return [
    'default' => env('CLOUD_DISK', 'oss'),

    'disks' => [
        'oss' => [
            'driver' => 'cfoss',
            'access_key' => env('CLOUD_OSS_ACCESS_KEY'),
            'secret_key' => env('CLOUD_OSS_SECRET_KEY'),
            'endpoint' => env('CLOUD_OSS_ENDPOINT'),
            'internal_endpoint' => env('CLOUD_OSS_INTERNAL_ENDPOINT', ''),
            'bucket' => env('CLOUD_OSS_BUCKET'),
            'cdndomain' => env('CLOUD_OSS_CDNDOMAIN', ''),
            'ssl' => env('CLOUD_OSS_SSL', false),
            'debug' => env('CLOUD_OSS_DEBUG', false),
        ],
        'cos' => [
            'driver' => 'cfcos',
            'access_key' => env('CLOUD_COS_ACCESS_KEY'),
            'secret_key' => env('CLOUD_COS_SECRET_KEY'),
            'region' => env('CLOUD_COS_REGION'),
            'bucket' => env('CLOUD_COS_BUCKET'),
            'cdndomain' => env('CLOUD_COS_CDNDOMAIN', ''),
            'ssl' => env('CLOUD_COS_SSL', false),
        ],
        'obs' => [
            'driver' => 'cfobs',
            'access_key' => env('CLOUD_OBS_ACCESS_KEY'),
            'secret_key' => env('CLOUD_OBS_SECRET_KEY'),
            'endpoint' => env('CLOUD_OBS_ENDPOINT'),
            'bucket' => env('CLOUD_OBS_BUCKET'),
            'cdndomain' => env('CLOUD_OBS_CDNDOMAIN', ''),
            'ssl' => env('CLOUD_OBS_SSL', false),
        ],
        'qiniu' => [
            'driver' => 'cfqiniu',
            'access_key' => env('CLOUD_QINIU_ACCESS_KEY'),
            'secret_key' => env('CLOUD_QINIU_SECRET_KEY'),
            'bucket' => env('CLOUD_QINIU_BUCKET'),
            'domain' => env('CLOUD_QINIU_DOMAIN'),
        ],
    ],
];
