<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\OAuthException;
use App\Utils\OAuthCacher;
use App\Utils\RedirectToBuilder;
use App\Utils\Respond;
use App\Utils\SignatureValidator;
use App\Utils\Tools;
use Carbon\Carbon;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class TestController extends Controller
{
    public function testSignature(Request $request) {
        return Respond::success([
            'success' => '成功了, 你好!',
        ]);
    }

    /**
     * @throws ValidationException
     */
    public function checkSignature(Request $request) {
        $this->validate($request, [
            'client_secret' => 'required',
        ]);

        $clientId = Tools::getClientId();
        $clientSecret = $request->input('client_secret');

        $timestamp = Tools::getTimestamp();
        $nonce = Tools::getNonce();
        $signature = Tools::getSignature();
        $data = $request->input();

        $signatureData = [
                'client_id' => $clientId,
                'timestamp' => $timestamp,
                'nonce'     => $nonce,
                'signature' => $signature,
            ] + $data;

        $signatureValidator = new SignatureValidator($clientId, $clientSecret);

        $onlineSignature = $signatureValidator->generateSignature($signatureData);

        return Respond::success([
            'signature'              => $onlineSignature,
            'check_signature_result' => $signatureValidator->verifySignature($data, $signature),
            'signature_data'         => $signatureData,
            'signature_str'          => $signatureValidator->getSignatureStr(),
        ]);
    }

    /**
     * @throws ConnectionException
     * @throws ValidationException
     */
    public function eventCall(Request $request) {
        $this->validate($request, [
            'client_secret' => 'required',
            'event'         => 'required',
            'callback_url'  => 'required|url',
        ]);

        $clientId = Tools::getClientId();
        $clientSecret = $request->input('client_secret');
        $event = $request->input('event');
        $callbackUrl = $request->input('callback_url');
        $toUser = $request->input('to_user') ?: '9d846bf5-ce36-4aea-9926-a0eea0d9e9f3';
        $field = $request->input('field') ?: 'nickname';
        $value = $request->input('value') ?: '张三改名为李四';
        $data = [];

        switch ($event) {
            case 'user_info_modified':
                $data = [
                    'event'     => 'user_info_modified',
                    'to_user'   => $toUser,
                    'client_id' => $clientId,
                    'field'     => $field,
                    'value'     => $value,
                ];
                break;
            case 'user_status_changed':
                $data = [
                    'event'     => 'user_status_changed',
                    'to_user'   => $toUser,
                    'client_id' => $clientId,
                    'status'    => 'disabled',
                ];
                break;
            case 'user_logout':
                $data = [
                    'event'     => 'user_logout',
                    'to_user'   => $toUser,
                    'client_id' => $clientId,
                    'status'    => 'logout',
                ];
                break;
            case 'user_revoke':
                $data = [
                    'event'     => 'user_revoke',
                    'to_user'   => $toUser,
                    'client_id' => $clientId,
                    'status'    => 'revoke',
                ];
                break;
        }

        $signatureData = [
                'client_id' => $clientId,
                'timestamp' => time(),
                'nonce'     => Str::random(),
            ] + $data;

        $signatureValidator = new SignatureValidator($clientId, $clientSecret);
        $signature = $signatureValidator->generateSignature($signatureData);

        Http::withHeaders([
            'X-Requested-With'      => 'XMLHttpRequest',
            'Accept'                => 'application/json',
            Tools::HEADER_CLIENT_ID => $clientId,
            Tools::HEADER_TIMESTAMP => $signatureData['timestamp'],
            Tools::HEADER_NONCE     => $signatureData['nonce'],
            Tools::HEADER_SIGN      => $signature,
        ])
            ->post($callbackUrl, $data);

        return Respond::success([
            'signature'      => $signature,
            'signature_data' => $signatureData,
            'signature_str'  => $signatureValidator->getSignatureStr(),
            'event_data'     => $data,
        ]);
    }

    public function checkCaptcha(Request $request) {
        //        try{
        $this->validate($request, [
            'captcha_scene'        => 'required',
            'captcha_verify_param' => 'required|checkRobotAnalyze:true',
        ]);
        //        }catch(ValidationException $e){
        //            return Respond::error(1,'验证错误',[
        //                'captcha_verify_param'=>$request->input('captcha_verify_param')
        //            ]);
        //        }

        return Respond::success();
    }

    /**
     * Get common headers for push requests
     */
    private function getPushRequestHeaders(array $data = []): array {
        $clientId = '2024110112345678';
        $timestamp = time();
        $nonce = Str::random();

        $signatureData = [
                'client_id' => $clientId,
                'timestamp' => $timestamp,
                'nonce'     => $nonce,
            ] + $data;

        $signatureValidator = new SignatureValidator($clientId, OAuthCacher::getClientSecretByKey($clientId));
        $signature = $signatureValidator->generateSignature($signatureData);

        return [
            'Accept'                => 'application/json',
            'Content-Type'          => 'application/json',
            Tools::HEADER_CLIENT_ID => $clientId,
            Tools::HEADER_TIMESTAMP => $timestamp,
            Tools::HEADER_NONCE     => $nonce,
            Tools::HEADER_SIGN      => $signature,
        ];
    }

    /**
     * Test personal push message
     */
    public function testPersonalPush(Request $request) {
        // Get current logged in user
        $user = auth('api')->user();
        if (!$user) {
            return Respond::error(401, '用户未登录');
        }

        //        $clientSecret = $request->input('client_secret', 'test_secret');

        $pushData = [
            'template_code'   => 'bangbang_feedback_reply',
            'to_open_id'      => $request->input('uuid') ?: $user->uuid,
            'template_params' => [
                'title'         => '测试通知审核',
                'status'        => '审核中',
                'reply_content' => ' ',
            ],
            'extend_params'   => [],
            'redirect_to'     => RedirectToBuilder::rongmei('article', 309421),
        ];

        $response = Http::withHeaders($this->getPushRequestHeaders($pushData))
                        ->withOptions([
                            'verify' => false,
                            'curl'   => [
                                CURLOPT_RESOLVE => ['uc.example.com:80:127.0.0.1'],
                            ],
                        ])
                        ->post(config('app.url') . '/api/push/service/to-user', $pushData);

        return $response->json();
    }

    /**
     * Test silent push message
     */
    public function testSilentPush(Request $request) {
        $user = auth('api')->user();
        if (!$user) {
            return Respond::error(401, '用户未登录');
        }

        $clientSecret = $request->input('client_secret', 'test_secret');

        $pushData = [
            'template_code'   => 'like_notice',
            'to_open_id'      => $request->input('uuid') ?: $user->uuid,
            'template_params' => [
                'from_name' => '陌生人',
                'title'     => '静默消息',
            ],
            'redirect_to'     => RedirectToBuilder::rongmei('article', 309421),
        ];

        $response = Http::withHeaders($this->getPushRequestHeaders($pushData))
                        ->withOptions([
                            'verify' => false,
                            'curl'   => [
                                CURLOPT_RESOLVE => ['uc.example.com:80:127.0.0.1'],
                            ],
                        ])
                        ->post(config('app.url') . '/api/push/service/to-user', $pushData);

        return $response->json();
    }

    /**
     * Test broadcast push message
     */
    public function testBroadcastPush(Request $request) {
        $clientSecret = $request->input('client_secret', 'test_secret');

        $pushData = [
            'template_code'   => 'news_broadcast',
            'template_params' => [
                'title'   => '测试广播',
                'summary' => '广播内容就是这么多. 这是测试这是测试',
            ],
            'extend_params'   => [
                'id' => '309421',
            ],
            'redirect_to'     => RedirectToBuilder::rongmei('article', 309421),
        ];

        $response = Http::withHeaders($this->getPushRequestHeaders($pushData))
                        ->withOptions([
                            'verify' => false,
                            'curl'   => [
                                CURLOPT_RESOLVE => ['uc.example.com:80:127.0.0.1'],
                            ],
                        ])
                        ->post(config('app.url') . '/api/push/service/broadcast', $pushData);

        return $response->json();
    }

    public function testAccessToken(Request $request) {
        $clientId = $request->input('client_id');
        $code = $request->input('code');

        // 获取client信息
        $client = OAuthCacher::getClientByKey($clientId);

        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }


        $signatureData = [
                'client_id' => $client->client_key,
                'timestamp' => time(),
                'nonce'     => Str::random(),
            ] + ['client_id' => $client->client_key, 'code' => $code, 'grant_type' => 'authorization_code'];

        $signatureValidator = new SignatureValidator($client->client_key, $client->client_access_secret);

        $signature = $signatureValidator->generateSignature($signatureData);

        $response = Http::withHeaders([
            Tools::HEADER_SIGN      => $signature,
            Tools::HEADER_TIMESTAMP => $signatureData['timestamp'],
            Tools::HEADER_NONCE     => $signatureData['nonce'],
            Tools::HEADER_CLIENT_ID => $client->client_key,
        ])
                        ->withOptions([
                            'verify' => false,
                            'curl'   => [
                                CURLOPT_RESOLVE => [
                                    'uc.example.com:80:127.0.0.1',
                                ],
                            ],
                        ])
                        ->post(config('app.url') . "/api/oauth2/access_token", ['client_id'  => $client->client_key,
                                                                                'code'       => $code,
                                                                                'grant_type' => 'authorization_code',
                        ]);

        return $response->json();
    }

    public function testUserInfo(Request $request) {
        $openId = $request->input('open_id');
        $accessToken = $request->input('access_token');
        $clientId = $request->input('client_id');

        // 获取client信息
        $client = OAuthCacher::getClientByKey($clientId);

        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }


        $signatureData = [
                'client_id' => $client->client_key,
                'timestamp' => time(),
                'nonce'     => Str::random(),
            ] + ['open_id' => $openId, 'access_token' => $accessToken];

        $signatureValidator = new SignatureValidator($client->client_key, $client->client_access_secret);

        $signature = $signatureValidator->generateSignature($signatureData);

        $response = Http::withHeaders([
            Tools::HEADER_SIGN      => $signature,
            Tools::HEADER_TIMESTAMP => $signatureData['timestamp'],
            Tools::HEADER_NONCE     => $signatureData['nonce'],
            Tools::HEADER_CLIENT_ID => $client->client_key,
        ])
                        ->withOptions([
                            'verify' => false,
                            'curl'   => [
                                CURLOPT_RESOLVE => [
                                    'uc.example.com:80:127.0.0.1',
                                ],
                            ],
                        ])
                        ->post(config('app.url') . "/api/oauth2/user_info", ['open_id'      => $openId,
                                                                             'access_token' => $accessToken,
                        ]);

        return $response->json();

    }

    public function testInput(Request $request) {
        return Respond::success([
            'query' => $request->query(),
            'post'  => $request->post(),
        ]);
    }

    public function getRealIP(Request $request){
        return Respond::success([
            'ip' => Tools::getClientIp(),
            'x-forwarded-for' => $request->header('x-forwarded-for'),
            'ipx' => $request->ip(),
            'x-real-ip' => $request->header('x-real-ip'),
            'x-real-port' => $request->header('x-real-port'),
            'x-forwarded-host' => $request->header('x-forwarded-host'),
            'x-forwarded-proto' => $request->header('x-forwarded-proto'),
            'x-forwarded-server' => $request->header('x-forwarded-server'),
            'x-forwarded-port' => $request->header('x-forwarded-port'),
            
        ]);
    }
}
