<?php

namespace App\Services\CloudFiles\Providers;

use App\Services\CloudFiles\Contracts\CloudFilesInterface;
use App\Services\CloudFiles\Exceptions\CloudFilesException;

/**
 * 云存储抽象基类
 */
abstract class AbstractCloudFiles implements CloudFilesInterface
{
    /**
     * 基础配置信息
     * @var array
     */
    protected $config = [];

    /**
     * SDK客户端实例
     * @var mixed
     */
    protected $client;

    /**
     * 是否开启调试
     * @var bool
     */
    protected $debug = false;

    /**
     * 结果映射关系
     * @var array
     */
    protected static $resultMap = [
        'Body' => 'raw_contents',
        'Content-Length' => 'size',
        'ContentType' => 'mimetype',
        'Size' => 'size',
        'StorageClass' => 'storage_class',
        'LastModified' => 'timestamp',
        'ETag' => 'etag'
    ];

    /**
     * 配置驱动
     * @param array $config
     * @return $this
     */
    public function config(array $config)
    {
        $this->config = $config;
        $this->debug = $config['debug'] ?? false;

        return $this->initializeClient();
    }

    /**
     * 初始化客户端
     * 由子类实现具体初始化逻辑
     * @return $this
     */
    abstract protected function initializeClient();

    /**
     * 统一的响应格式化
     * @param array $response 原始响应
     * @param string $path 文件路径
     * @return array
     */
    protected function normalizeResponse(array $response, string $path): array
    {
        $result = [];

        // 处理基础字段
        $result['path'] = $this->removePathPrefix($path);
        $result['type'] = $response['type'] ?? 'file';

        // 映射通用字段
        foreach (static::$resultMap as $from => $to) {
            if (isset($response[$from])) {
                if ($to === 'timestamp' && !is_numeric($response[$from])) {
                    $result[$to] = strtotime($response[$from]);
                } else {
                    $result[$to] = $response[$from];
                }
            }
        }

        return $result;
    }

    /**
     * 统一的异常处理
     * @param \Throwable $e 原始异常
     * @param string $action 操作名称
     * @throws CloudFilesException
     */
    protected function handleException(\Throwable $e, string $action): void
    {
        $message = sprintf(
            '[%s] Failed to %s file: %s',
            get_class($this),
            $action,
            $e->getMessage()
        );

        if ($this->debug) {
            info($message, [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        throw new CloudFilesException($message, $e->getCode(), $e);
    }

    /**
     * 移除路径前缀
     * @param string $path
     * @return string
     */
    protected function removePathPrefix(string $path): string
    {
        return ltrim($path, '/');
    }

    /**
     * 获取配置值
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function getConfig(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * 调试日志
     * @param string $message
     * @param array $context
     * @return void
     */
    protected function debug(string $message, array $context = []): void
    {
        if ($this->debug) {
            info(sprintf('[CloudFiles] %s', $message), $context);
        }
    }
}
