<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $user_id
 * @property string $change_key
 * @property string $old_value
 * @property string $new_value
 * @property int $status 审核状态
 * @property int|null $admin_user_id 审核管理员ID
 * @property Carbon|null $audit_time 审核时间
 * @property string $ip
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\AdminUser|null $adminUser
 * @property-read mixed $status_text
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereAdminUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereAuditTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereChangeKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereNewValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereOldValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserChangeLog whereUserId($value)
 * @mixin \Eloquent
 * @mixin IdeHelperUserChangeLog
 */
class UserChangeLog extends Model
{
    use  HasFactory;

    const STATUS_PENDING = 0;   // 待审核
    const STATUS_APPROVED = 1;  // 已批准
    const STATUS_REJECTED = 2;  // 已拒绝

    public static array $statusMap = [
        self::STATUS_PENDING => '待审核',
        self::STATUS_APPROVED => '已批准',
        self::STATUS_REJECTED => '已拒绝',
    ];

    protected $fillable = [
        'user_id',
        'change_key',
        'old_value',
        'new_value',
        'ip',
        'status',
        'admin_user_id',
        'audit_time',
    ];

    protected $casts = [
        'audit_time' => 'datetime',
    ];

    public function user() {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function adminUser() {
        return $this->belongsTo(AdminUser::class, 'admin_user_id', 'id');
    }

    public function getStatusTextAttribute()
    {
        return self::$statusMap[$this->status] ?? '未知';
    }
}
