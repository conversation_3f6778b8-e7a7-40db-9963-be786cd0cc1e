<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Enums\PushMessageCategoryEnum;
use App\Exceptions\AdminException;
use App\Facades\AdminPermission;
use App\Http\Resources\Admins\ClientResource;
use App\Http\Resources\Admins\ClientSubscriptionResource;
use App\Http\Resources\Admins\ClientTemplateResource;
use App\Models\Client;
use App\Models\Material;
use App\Models\OauthClientSubscription;
use App\Models\OauthClientTemplate;
use App\Models\PushTemplate;
use App\Utils\OAuthCacher;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Rtgm\sm\RtSm2;

class AdminApplicationsController extends AdminBaseController
{
    protected $validationMessages = [
        // updateClient 方法的验证消息
        'icon.required'              => '应用图标不能为空',
        'name.required'              => '应用名称不能为空',
        'name.string'                => '应用名称必须是字符串',
        'name.max'                   => '应用名称不能超过32个字符',
        'provider.required'          => '应用提供商不能为空',
        'provider.string'            => '应用提供商必须是字符串',
        'provider.max'               => '应用提供商不能超过32个字符',
        'description.string'         => '应用描述必须是字符串',
        'auth_safe_domains.array'    => '安全域名数据格式无效',
        'auth_safe_domains.*.url'    => '安全域名必须是有效的地址',
        'callback_url.url'           => '事件回调地址必须是有效地址',
        'allowed_scopes.nullable'    => '授权范围格式无效',
        'allowed_scopes.array'       => '授权范围必须是数组',
        'allowed_jsapis.nullable'    => 'JSAPI格式无效',
        'allowed_jsapis.array'       => '允许的JSAPI必须是数组',
        'disabled_jsapis.nullable'   => '禁用JSAPI格式无效',
        'disabled_jsapis.array'      => '禁用的JSAPI必须是数组',

        // createSubscription 方法的验证消息
        'subscription_code.required' => '订阅码不能为空',
        'push_template_id.required'  => '消息模板不能为空',
        'push_template_id.exists'    => '消息模板不存在',
        'filter_conditions.nullable' => '筛选条件格式无效',
        'filter_conditions.array'    => '筛选条件必须是数组',
        'status.required'            => '状态不能为空',
        'status.in'                  => '状态值只能是0或1',

        // updateSubscription 方法的验证消息
        'remark.required'            => '备注不能为空',
        'remark.string'              => '备注必须是字符串',
        'remark.max'                 => '备注不能超过200个字符',

        // resetKeys 方法的验证消息
        'password.required'          => '密码不能为空',
    ];

    protected const PERMISSION_MAP = [
        // 'index'                 => '我的应用.查看列表',
        'show'                  => '我的应用.查看详情',
        'updateClient'          => '我的应用.更新应用',
        'getKeys'               => '我的应用.查看密钥',
        'resetKeys'             => '我的应用.重置密钥',
        'availableTemplates'    => '我的应用.获取可用模板',
        'subscriptions'         => '我的应用.查看订阅列表',
        'createSubscription'    => '我的应用.创建活动订阅',
        'updateSubscription'    => '我的应用.更新活动订阅',
        'deleteSubscription'    => '我的应用.删除订阅',
        'toggleSubscriptionStatus' => '我的应用.切换订阅状态',
    ];

    /**
     * 获取管理员可管理的OAuth客户端列表
     */
    public function index(Request $request) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        // $query =  AdminPermission::can('管理应用.管理') ? Client::query() // 超级管理员可以看到所有客户端
        //     : $admin->oauthClients();
        $query = $admin->oauthClients();
        
        $clients = $query->when($request->input('name'), function ($query, $name) {
            $query->where('name', 'like', "%$name%");
        })
                         ->when($request->input('provider'), function ($query, $provider) {
                             $query->where('provider', 'like', "%$provider%");
                         })
                         ->when($request->input('client_type'), function ($query, $client_type) {
                             $query->where('client_type', $client_type);
                         })
                         ->when($request->input('client_key'), function ($query, $client_key) {
                             $query->where('client_key', $client_key);
                         })
                        //  ->with([
                        //      'userBindings' => function ($query) {
                        //          $query->select('client_id')
                        //                ->selectRaw('count(*) as bindings_count')
                        //                ->groupBy('client_id');
                        //      },
                        //  ])
                         ->orderByDesc('id')
                         ->paginate();

        return Respond::success(ClientResource::collection($clients));
    }

    /**
     * 获取应用详情
     *
     * @throws AdminException
     */
    public function show($clientId) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $client = Client::findOrFail($clientId);

        return Respond::success(ClientResource::make($client));
    }

    /**
     * 修改OAuth客户端
     *
     * @throws AdminException|\Throwable
     */
    public function updateClient(Request $request, $clientId) {
        $client = Client::findOrFail($clientId);

        // 检查权限
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $validated = $request->validate([
            'icon'                => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $material = Material::where('uuid', $value)
                                        ->first();
                    if ($value && !$material) {
                        $fail('无效的图像资源');
                    }
                },
            ],
            'name'                => 'required|string|max:32',
            //            'provider'            => 'required|string|max:32',
            'description'         => 'nullable|string',
            'auth_safe_domains'   => 'nullable|array',
            'auth_safe_domains.*' => 'url',
            'callback_url'        => 'nullable|url',
            'workspace_redirect_url' => 'nullable|url',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $avatarMaterrial = Material::where('uuid', $validated['icon'])
                                       ->first();
            $validated['icon'] = [
                'path'     => $avatarMaterrial->path,
                'provider' => $avatarMaterrial->provider,
                'uuid'     => $avatarMaterrial->uuid,
            ];

            $client->update($validated);

            OAuthCacher::invalidateClient($client->id);

            DB::commit();

            return Respond::success();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    public function getKeys($clientId) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $client = Client::findOrFail($clientId);

        return Respond::success([
            'client_id'            => $client->client_key,
            'client_access_key'    => $client->client_access_key,
            'client_access_secret' => $client->client_access_secret,
        ]);
    }

    /**
     * 重置应用密钥对
     *
     * @throws AdminException
     */
    public function resetKeys(Request $request, $clientId) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $client = Client::findOrFail($clientId);

        $this->validate($request, [
            'password' => ['required'],
        ], $this->validationMessages);

        if (Hash::check($request->input('password'), $admin->password) === false) {
            throw new AdminException(ErrorCodeEnum::ADMIN_PASSWORD_ERROR);
        }

        try {
            DB::beginTransaction();

            // 生成新的SM2密钥对
            $sm2 = new RtSm2();
            [
                $privateKey,
                $publicKey,
            ] = $sm2->generatekey();

            $client->update([
                'client_access_key'    => $publicKey,
                'client_access_secret' => $privateKey,
            ]);

            // 清除缓存
            OAuthCacher::invalidateClient($client->id);
            OAuthCacher::invalidateClientByKey($client->client_key);

            DB::commit();

            return Respond::success([
                'client_access_key'    => $publicKey,
                'client_access_secret' => $privateKey,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 获取应用管理相关选项
     */
    public function options() {
        return Respond::success([
            // 获取启用状态的消息模板
            //            'templates'                   => PushTemplate::where('status', PushTemplate::STATUS_ENABLE)
            //                                                         ->select([
            //                                                             'id',
            //                                                             'name',
            //                                                             'code',
            //                                                             'title',
            //                                                             'category',
            //                                                         ])
            //                                                         ->orderBy('category')
            //                                                         ->get(),
            // 订阅状态选项
            'subscription_status_options' => OauthClientSubscription::$statusMap,
            // 模板状态选项
            'template_status_options'     => OauthClientTemplate::$statusMap,
            // 消息分类选项
            'message_categories'          => PushMessageCategoryEnum::options(),
            'client_types'                => Client::$clientTypeMap,
            'scopes'                      => Client::availableScopes(),
        ]);
    }

    /**
     * 获取应用可用的消息模板列表
     */
    public function availableTemplates($clientId) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $templates = OauthClientTemplate::with(['pushTemplate'])
                                        ->where('oauth_client_id', $clientId)
                                        ->paginate();

        return Respond::success(ClientTemplateResource::collection($templates));
    }

    /**
     * 获取应用的订阅列表
     */
    public function subscriptions($clientId) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $subscriptions = OauthClientSubscription::with(['pushTemplate', 'client'])
                                                ->where('oauth_client_id', $clientId)
                                                ->orderByDesc('id')
                                                ->get();

        return Respond::success(ClientSubscriptionResource::collection($subscriptions));
    }

    /**
     * 创建应用订阅
     */
    public function createSubscription(Request $request, $clientId) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $validated = $request->validate([
            'name'              => 'required|string|max:50',
            'description'       => 'nullable|string',
            'push_template_id'  => [
                'required',
                'exists:push_templates,id',
                function ($attribute, $value, $fail) use ($clientId) {
                    // 验证模板是否可用
                    $template = PushTemplate::find($value);
                    if (!$template || $template->status != PushTemplate::STATUS_ENABLE) {
                        $fail('消息模板不可用');
                    }
                },
            ],
            'filter_conditions' => 'nullable|array',
            'status'            => 'required|in:0,1',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $subscription = OauthClientSubscription::create([
                'oauth_client_id'   => $clientId,
                'subscription_code' => uniqid('SUB_'),
                // 生成唯一订阅码
                'name'              => $validated['name'],
                'description'       => $validated['description'],
                'push_template_id'  => $validated['push_template_id'],
                'filter_conditions' => $validated['filter_conditions'] ?? [],
                'status'            => $validated['status'],
            ]);

            DB::commit();

            return Respond::success(ClientSubscriptionResource::make($subscription));
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 更新应用订阅
     */
    public function updateSubscription(Request $request, $clientId, $subscriptionId) {
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $subscription = OauthClientSubscription::where('oauth_client_id', $clientId)
                                               ->findOrFail($subscriptionId);

        $validated = $request->validate([
            'name'        => 'required|string|max:50',
            'description' => 'nullable|string',
            'status'      => 'required|in:0,1',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $subscription->update($validated);

            DB::commit();

            return Respond::success(ClientSubscriptionResource::make($subscription));
        } catch (\Exception $e) {
            DB::rollBack();
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 删除应用订阅
     */
    public function deleteSubscription($clientId, $subscriptionId) {
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $subscription = OauthClientSubscription::where('oauth_client_id', $clientId)
                                               ->findOrFail($subscriptionId);

        // 检查是否有用户已订阅
        if ($subscription->userSubscriptions()
                         ->exists()) {
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR, [], '该订阅已有用户使用，无法删除');
        }

        $subscription->delete();

        return Respond::success();
    }

    /**
     * 切换订阅状态
     */
    public function toggleSubscriptionStatus($clientId, $subscriptionId) {
        $admin = Auth::guard('admin')
                     ->user();
        if (!$admin->canManageOauthClient($clientId)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION);
        }

        $subscription = OauthClientSubscription::where('oauth_client_id', $clientId)
                                               ->findOrFail($subscriptionId);

        $subscription->update([
            'status' => $subscription->status == OauthClientSubscription::STATUS_ENABLE ? OauthClientSubscription::STATUS_DISABLE : OauthClientSubscription::STATUS_ENABLE,
        ]);

        return Respond::success(ClientSubscriptionResource::make($subscription));
    }
}
