<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminUserStatusChangedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $adminUserUuid;
    public $status;
    /**
     * Create a new event instance.
     */
    public function __construct($adminUserUuid, $status)
    {
        $this->adminUserUuid = $adminUserUuid;
        $this->status = $status;
    }

    public function getAdminUserUuid()
    {
        return $this->adminUserUuid;
    }

    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
