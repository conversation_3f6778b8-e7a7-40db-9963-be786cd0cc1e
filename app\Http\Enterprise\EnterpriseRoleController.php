<?php

namespace App\Http\Enterprise;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Models\Role;
use App\Models\Permission;
use App\Models\EnterpriseAdmin;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class EnterpriseRoleController extends EnterpriseBaseController
{
    protected const PERMISSION_MAP = [
        'index' => '企业角色管理.查看列表',
        'store' => '企业角色管理.创建',
        'show' => '企业角色管理.查看详情',
        'update' => '企业角色管理.编辑',
        'destroy' => '企业角色管理.删除',
        'assignRole' => '企业角色管理.分配角色',
        'revokeRole' => '企业角色管理.撤销角色',
        'permissions' => '企业角色管理.查看权限',
    ];

    protected $validationMessages = [
        'name.required' => '角色名称不能为空',
        'name.string' => '角色名称必须为字符串',
        'name.max' => '角色名称不能超过50个字符',
        'name.unique' => '角色名称已存在',
        'description.string' => '角色描述必须为字符串',
        'description.max' => '角色描述不能超过255个字符',
        'admin_id.required' => '管理员ID不能为空',
        'admin_id.exists' => '管理员不存在',
        'role_id.required' => '角色ID不能为空',
        'role_id.exists' => '角色不存在',
    ];

    /**
     * 获取企业角色列表
     */
    public function index(Request $request)
    {
        $query = Role::where('guard_name', 'enterprise')
                    ->withCount('users');

        // 搜索角色名称
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $roles = $query->orderByDesc('created_at')
                      ->paginate($request->get('per_page', 15));

        return Respond::success($roles);
    }

    /**
     * 创建企业角色
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:50',
                Rule::unique('roles')->where(function ($query) {
                    return $query->where('guard_name', 'enterprise');
                }),
            ],
            'description' => 'nullable|string|max:255',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $role = Role::create([
                'name' => $validated['name'],
                'guard_name' => 'enterprise',
                'description' => $validated['description'] ?? '',
            ]);

            DB::commit();
            return Respond::success($role);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('创建角色失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取角色详情
     */
    public function show($id)
    {
        $role = Role::where('guard_name', 'enterprise')
                   ->with(['permissions', 'users'])
                   ->findOrFail($id);
        
        return Respond::success($role);
    }

    /**
     * 更新角色
     */
    public function update(Request $request, $id)
    {
        $role = Role::where('guard_name', 'enterprise')->findOrFail($id);

        $validated = $request->validate([
            'name' => [
                'sometimes',
                'required',
                'string',
                'max:50',
                Rule::unique('roles')->where(function ($query) {
                    return $query->where('guard_name', 'enterprise');
                })->ignore($id),
            ],
            'description' => 'nullable|string|max:255',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $role->update($validated);

            DB::commit();
            return Respond::success($role);
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('更新角色失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除角色
     */
    public function destroy($id)
    {
        $role = Role::where('guard_name', 'enterprise')->findOrFail($id);

        try {
            DB::beginTransaction();

            // 检查是否有用户使用该角色
            if ($role->users()->exists()) {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '该角色还有用户在使用，无法删除');
            }

            $role->delete();

            DB::commit();
            return Respond::success(null, '删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            if ($e instanceof AdminException) {
                throw $e;
            }
            return Respond::error('删除角色失败: ' . $e->getMessage());
        }
    }

    /**
     * 为企业管理员分配角色
     */
    public function assignRole(Request $request)
    {
        $validated = $request->validate([
            'admin_id' => 'required|exists:enterprise_admins,id',
            'role_id' => 'required|exists:roles,id',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $admin = EnterpriseAdmin::findOrFail($validated['admin_id']);
            $role = Role::where('guard_name', 'enterprise')->findOrFail($validated['role_id']);

            $admin->assignRole($role);

            DB::commit();
            return Respond::success(null, '分配角色成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('分配角色失败: ' . $e->getMessage());
        }
    }

    /**
     * 撤销企业管理员的角色
     */
    public function revokeRole(Request $request)
    {
        $validated = $request->validate([
            'admin_id' => 'required|exists:enterprise_admins,id',
            'role_id' => 'required|exists:roles,id',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $admin = EnterpriseAdmin::findOrFail($validated['admin_id']);
            $role = Role::where('guard_name', 'enterprise')->findOrFail($validated['role_id']);

            $admin->removeRole($role);

            DB::commit();
            return Respond::success(null, '撤销角色成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('撤销角色失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取企业可用的权限列表
     */
    public function permissions(Request $request)
    {
        // 企业只能查看特定的权限，不能编辑权限值
        $permissions = Permission::where('guard_name', 'enterprise')
                                ->where('name', 'like', '企业%')
                                ->orderBy('name')
                                ->get()
                                ->groupBy(function ($permission) {
                                    return explode('.', $permission->name)[0];
                                });

        return Respond::success($permissions);
    }

    /**
     * 获取角色的权限设置（只读）
     */
    public function rolePermissions($roleId)
    {
        $role = Role::where('guard_name', 'enterprise')
                   ->with('permissions')
                   ->findOrFail($roleId);

        $allPermissions = Permission::where('guard_name', 'enterprise')
                                   ->where('name', 'like', '企业%')
                                   ->get();

        $rolePermissionIds = $role->permissions->pluck('id')->toArray();

        $permissionTree = $allPermissions->groupBy(function ($permission) {
            return explode('.', $permission->name)[0];
        })->map(function ($group, $groupName) use ($rolePermissionIds) {
            return [
                'name' => $groupName,
                'permissions' => $group->map(function ($permission) use ($rolePermissionIds) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'description' => $permission->description,
                        'selected' => in_array($permission->id, $rolePermissionIds),
                    ];
                })->values(),
            ];
        })->values();

        return Respond::success([
            'role' => $role,
            'permissions' => $permissionTree,
        ]);
    }

    /**
     * 获取所有企业角色（用于下拉选择）
     */
    public function all()
    {
        $roles = Role::where('guard_name', 'enterprise')
                    ->select(['id', 'name', 'description'])
                    ->orderBy('name')
                    ->get();

        return Respond::success($roles);
    }
}
