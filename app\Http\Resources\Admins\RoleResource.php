<?php
namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Role Resource
 * @property-read \App\Models\Role $resource
 * @mixin \App\Models\Role
 */
class RoleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'guard_name' => $this->guard_name,
            'permissions' => PermissionResource::collection($this->whenLoaded('permissions')),
//            'permission_tree' => $this->when($this->permissions !== null, function() {
//                return app('admin.permission')->getRolePermissions($this->id);
//            }),
            'created_at' => $this->created_at,
        ];
    }
}
