<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Enums\OAuthScopeEnum;
use App\Exceptions\OAuthException;
use App\Http\Resources\ClientResource;
use App\Models\AccessToken;
use App\Models\AdminUser;
use App\Models\AuthCode;
use App\Models\Client;
use App\Models\OAuthUserAuthorization;
use App\Models\OAuthUserBinding;
use App\Models\RefreshToken;
use App\Models\User;
use App\Utils\OAuthCacher;
use App\Utils\OpenIDGenerater;
use App\Utils\Respond;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Models\AdminDepartment;
use App\Models\Role;
use App\Utils\GmSm;
use App\Utils\Tools;

class OAuthController extends Controller
{
    private OpenIDGenerater $openIDGenerater;

    protected $validationMessages = [
        'grant_type.required_if'        => '授权类型为:value时不能为空',
        'code.required_if'              => '当授权类型为authorization_code时授权码不能为空',
        'refresh_token.required_if'     => '当授权类型为refresh_token时刷新令牌不能为空',
        'client_id.required_with'       => '当提供回调地址时客户端ID不能为空',
        'redirect_uri.required_with'    => '当提供客户端ID时回调地址不能为空',
        'state.required_with'           => '当提供回调地址时state参数不能为空',
        'client_id.required'            => '客户端ID不能为空',
        'redirect_uri.required'         => '回调地址不能为空',
        'redirect_uri.url'              => '回调地址格式不正确',
        'scopes.string'                 => '授权范围格式不正确',
        'state.max'                     => '状态参数长度不能超过255个字符',
        'response_type.required'        => '响应类型不能为空',
        'response_type.in'              => '不支持的响应类型',
        'grant_type.required'           => '授权类型不能为空',
        'grant_type.in'                 => '不支持的授权类型',
        'client_access_secret.required' => '客户端密钥不能为空',
        'code.required'                 => '授权码不能为空',
        'refresh_token.required'        => '刷新令牌不能为空',
        'name.required'                 => '名称不能为空',
        'name.max'                      => '名称不能超过50个字符',
        'template_code.required'        => '模板代码不能为空',
        'template_code.max'             => '模板代码不能超过50个字符',
        'description.max'               => '描述不能超过200个字符',
    ];

    public function __construct(OpenIDGenerater $openIDGenerater)
    {
        $this->openIDGenerater = $openIDGenerater;
    }

    /**
     * @throws OAuthException
     */
    public function checkAuthStatus(Request $request)
    {
        $request->validate([
            'client_id' => 'required',
        ], $this->validationMessages);

        //        $client = Client::where('client_key', $request->client_id)
        //                        ->where('is_revoked', Client::IS_REVOKED_NO)
        //                        ->whereNot('client_type', Client::TYPE_BACKEND)
        //                        ->firstOrFail();

        $client = OAuthCacher::getClientByKey($request->client_id);
        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        $user = Auth::guard('api')
            ->user();

        $targetUserUuid = $user->uuid;
        $isBackendApp = $client->is_workspace_client === Client::IS_WORKSPACE_CLIENT_YES;
        $userType = $isBackendApp ? AuthCode::USER_TYPE_ADMIN : AuthCode::USER_TYPE_USER;

        if ($isBackendApp) {
            $adminUser = AdminUser::where('bind_user_uuid', $user->uuid)
                ->first();

            if (!$adminUser) {
                throw new OAuthException(ErrorCodeEnum::OAUTH_USER_NO_ADMIN_BINDING);
            }

            if (!$adminUser->isActive()) {
                throw new OAuthException(ErrorCodeEnum::ADMIN_STATUS_ERROR);
            }

            if (!$adminUser->canAccessOauthClient($client->id)) {
                throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_CLIENT_DENIED);
            }

            $targetUserUuid = $adminUser->uuid;
        }

        if (OAuthCacher::isUserAuthorized($targetUserUuid, $client->id)) {

            $userBinding = OAuthCacher::getUserBinding($targetUserUuid, $client->id);

            if ($userBinding) {
                // 已授权用户，直接生成新的auth code
                $authCode = AuthCode::create([
                    'id'         => Str::random(40),
                    'user_uuid'  => $targetUserUuid,
                    'user_type'  => $userType,
                    'client_id'  => $client->id,
                    'scopes'     => OAuthCacher::getUserAuthorizedScopes($targetUserUuid, $client->id),
                    'expires_at' => now()->addMinutes(10),
                ]);

                return Respond::success([
                    'status'  => 'authorized',
                    'client'  => [
                        'client_id'   => $client->client_key,
                        'client_type' => $client->client_type,
                        'icon'        => $client->display_icon,
                        'name'        => $client->name,
                        'description' => $client->description,
                        'provider'    => $client->provider,
                    ],
                    'code'    => $authCode->id,
                    //                    'open_id' => $userBinding->open_id,
                ]);
            }
        }

        return Respond::success([
            'status' => 'unauthorized',
            'client' => [
                'client_id'   => $client->client_key,
                'client_type' => $client->client_type,
                'icon'        => $client->display_icon,
                'name'        => $client->name,
                'description' => $client->description,
                'provider'    => $client->provider,
            ],
            'scopes' => $client->getAllowedScopesWithLabels(),
        ]);
    }

    /**
     * 获取客户端信息和授权页面所需数据
     * @throws OAuthException
     */
    public function getClientInfo($clientKey)
    {
        //        $client = Client::where('client_key', $clientKey)
        //                        ->where('is_revoked', Client::IS_REVOKED_NO)
        //                        ->whereNot('client_type', Client::TYPE_BACKEND)
        //                        ->firstOrFail();

        $client = OAuthCacher::getClientByKey($clientKey);
        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        return Respond::success(ClientResource::make($client));
    }

    /**
     * 处理用户授权并返回授权码
     * @throws OAuthException
     */
    public function authorize(Request $request)
    {
        $request->validate([
            'client_id'     => 'required',
            'redirect_uri'  => 'nullable|url',
            'scopes'        => 'required|string',
            'state'         => 'nullable|string|max:255',
            'response_type' => [
                'required',
                'in:code',
            ],
        ], $this->validationMessages);

        $client = OAuthCacher::getClientByKey($request->client_id);

        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        $user = Auth::guard('api')
            ->user();

        $isBackendApp = $client->is_workspace_client === Client::IS_WORKSPACE_CLIENT_YES;

        if ($isBackendApp) {
            $user = AdminUser::where('bind_user_uuid', $user->uuid)
                ->first();

            if (!$user) {
                throw new OAuthException(ErrorCodeEnum::OAUTH_USER_NO_ADMIN_BINDING);
            }

            if (!$user->isActive()) {
                throw new OAuthException(ErrorCodeEnum::ADMIN_STATUS_ERROR);
            }

            if (!$user->canAccessOauthClient($client->id)) {
                throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_CLIENT_DENIED);
            }
        }

        $userType = $isBackendApp ? AuthCode::USER_TYPE_ADMIN : AuthCode::USER_TYPE_USER;


        // 验证redirect_uri和scope
        // $redirectUri = $request->redirect_uri ?: $client->default_redirect_url;
        $redirectUri = $request->redirect_uri;
        if (($client->client_type == Client::CLIENT_TYPE_H5) && !$this->validateRedirectUri($redirectUri, $client->auth_safe_domains)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_REDIRECT_INVALIDATE);
        }

        $requestedScopes = $request->scopes ? explode(' ', $request->scopes) : $client->allowed_scopes;

        if (!$this->validateScopes($requestedScopes, $client->allowed_scopes)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_SCOPES_ERROR);
        }

        // if(!$isBackendApp){
        if (in_array(OAuthScopeEnum::IDENTITY->value, $requestedScopes) && !$user->isRealnameAuthConfirmed()) {
            throw new OAuthException(ErrorCodeEnum::REALNAME_UNBINDED);
        }
        // }

        // 确保用户绑定并获取openid
        $userBinding = $this->ensureUserBinding($user->uuid, $client, $userType);

        // 创建授权记录
        $authorization = OAuthUserAuthorization::updateOrCreate([
            'user_uuid' => $user->uuid,
            'user_type' => $userType,
            'client_id' => $client->id,
        ], [
            'granted_scopes' => $requestedScopes,
            'authorized_at'  => now(),
            'last_used_at'   => now(),
            'is_revoked'     => OAuthUserAuthorization::IS_REVOKED_NO,
            'revoked_at'     => null,
        ]);

        OAuthCacher::invalidateUserAuthorization($user->uuid, $client->id);

        // 生成授权码
        $authCode = AuthCode::create([
            'id'         => Str::random(40),
            'user_uuid'  => $user->uuid,
            'user_type'  => $userType,
            'client_id'  => $client->id,
            'scopes'     => $requestedScopes,
            'expires_at' => now()->addMinutes(10),
        ]);

        // 构建回调URL
        if ($client->client_type == Client::CLIENT_TYPE_H5) {
            $redirectUri = $this->buildRedirectUri($redirectUri, $authCode->id, $request->state, $userBinding->open_id);

            return Respond::success([
                'status'  => 'authorized',
                'client'       => [
                    'client_id'   => $client->client_key,
                    'client_type' => $client->client_type,
                ],
                'code'         => $authCode->id,
                'state' => $request->state,
                'redirect_uri' => $redirectUri,
                //                'open_id'      => $userBinding->open_id,
            ]);
        }

        return Respond::success([
            'status'  => 'authorized',
            'client'  => [
                'client_id'   => $client->client_key,
                'client_type' => $client->client_type,
            ],
            'state' => $request->state,
            'code'    => $authCode->id,
            //            'open_id' => $userBinding->open_id,
        ]);
    }

    /**
     * 获取用户信息
     * @throws OAuthException
     */
    public function getUserInfo(Request $request)
    {
        $request->validate([
            'access_token' => 'required|string',
            'open_id'      => 'required|string',
        ], $this->validationMessages);

        $client = OAuthCacher::getClientByKey(Tools::getClientId());
        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        $token = OAuthCacher::getAccessToken($request->access_token);

        if (!$token || $token->client_id !== $client->id) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_TOKEN_INVALIDATE);
        }

        $userBinding = OAuthCacher::getUserBindingByOpenId($request->open_id, $token->client_id);

        if (!$userBinding || $userBinding->user_uuid !== $token->user_uuid) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_OPNEID_NOT_FOUND);
        }

        $user = $token->user;

        if (!$user || !$user->isActive()) {
            throw new OAuthException(ErrorCodeEnum::USER_STATUS_ERROR);
        }

        if ($token->user_type == AccessToken::USER_TYPE_USER) {

            // 根据授权scope返回相应的用户信息
            $responseData = [
                'open_id'  => $userBinding->open_id,
                'union_id'     => $user->uuid,
                'nickname' => $user->nickname,
                'avatar'   => $user->display_avatar,
                'gender'   => $user->gender_txt,
            ];

            // 根据token的scope返回额外的用户信息
            if (in_array('mobile', $token->scopes)) {
                $responseData['mobile'] = $user->mobile;
            }

            if (in_array('identity', $token->scopes)) {
                $responseData['real_name'] = $user->realname_auth_data[User::REALNAME_AUTH_NAME];
                $responseData['identity'] = $user->realname_auth_data[User::REALNAME_AUTH_IDENTITY];
            }
        } else {
            $responseData = [
                'open_id'   => $userBinding->open_id,
                'union_id'  => $user->uuid,
                'nickname'  => $user->nickname,
                'true_name' => $user->true_name,
                'display_name' => $user->display_name,
                'mobile'    => $user->mobile,
                'shot_mobile' => $user->shot_mobile,
                'avatar'    => $user->display_avatar,
                'departments' => $user->departments ? $user->departments->map(function ($department) {
                    return [
                        'id' => $department->id,
                        'name' => $department->name,
                        'code' => $department->code,
                        'parent' => $department->parent_id,
                    ];
                })->toArray() : [],
                'front_user' => $user->bindUser ? [
                    'nickname' => $user->bindUser->nickname,
                    'avatar' => $user->bindUser->display_avatar,
                    'gender' => $user->bindUser->gender_txt,
                ] : [],
            ];

            if (in_array('identity', $token->scopes)) {
                $responseData['real_name'] = $user->bindUser?->realname_auth_data[User::REALNAME_AUTH_NAME];
                $responseData['identity'] = $user->bindUser?->realname_auth_data[User::REALNAME_AUTH_IDENTITY];
            }
        }

        return Respond::success($responseData);
    }

    public function getUserInfoEncrypt(Request $request)
    {

        $request->validate([
            'access_token' => 'required|string',
            'open_id'      => 'required|string',
        ], $this->validationMessages);

        $client = OAuthCacher::getClientByKey(Tools::getClientId());
        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        $token = OAuthCacher::getAccessToken($request->access_token);

        if (!$token || $token->client_id !== $client->id) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_TOKEN_INVALIDATE);
        }

        $userBinding = OAuthCacher::getUserBindingByOpenId($request->open_id, $token->client_id);

        if (!$userBinding || $userBinding->user_uuid !== $token->user_uuid) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_OPNEID_NOT_FOUND);
        }

        $user = $token->user;

        if (!$user || !$user->isActive()) {
            throw new OAuthException(ErrorCodeEnum::USER_STATUS_ERROR);
        }

        // 对敏感数据进行加密,并且对原数据进行匿名隐藏, 比如mobile,real_name,identity

        GmSm::configure(publicKey: $client->client_access_key, privateKey: $client->client_access_secret);

        if ($token->user_type == AccessToken::USER_TYPE_USER) {

            // 根据授权scope返回相应的用户信息
            $responseData = [
                'open_id'  => $userBinding->open_id,
                'union_id'     => $user->uuid,
                'nickname' => $user->nickname,
                'avatar'   => $user->display_avatar,
                'gender'   => $user->gender_txt,
            ];

            // 根据token的scope返回额外的用户信息
            if (in_array('mobile', $token->scopes)) {
                $responseData['mobile'] = $user->mobile;
            }

            if (in_array('identity', $token->scopes)) {
                $responseData['real_name'] = $user->realname_auth_data[User::REALNAME_AUTH_NAME];
                $responseData['identity'] = $user->realname_auth_data[User::REALNAME_AUTH_IDENTITY];
            }
        } else {
            $responseData = [
                'open_id'   => $userBinding->open_id,
                'union_id'  => $user->uuid,
                'nickname'  => $user->nickname,
                'true_name' => $user->true_name,
                'display_name' => $user->display_name,
                'mobile'    => $user->mobile,
                'shot_mobile' => $user->shot_mobile,
                'avatar'    => $user->display_avatar,
                'departments' => $user->departments ? $user->departments->map(function ($department) {
                    return [
                        'id' => $department->id,
                        'name' => $department->name,
                        'code' => $department->code,
                        'parent' => $department->parent_id,
                    ];
                })->toArray() : [],
                'front_user' => $user->bindUser ? [
                    'nickname' => $user->bindUser->nickname,
                    'avatar' => $user->bindUser->display_avatar,
                    'gender' => $user->bindUser->gender_txt,
                ] : [],
            ];

            if (in_array('identity', $token->scopes)) {
                $responseData['real_name'] = $user->bindUser?->realname_auth_data[User::REALNAME_AUTH_NAME];
                $responseData['identity'] = $user->bindUser?->realname_auth_data[User::REALNAME_AUTH_IDENTITY];
            }
        }

        if ($responseData['mobile'] ?? false) {
            $responseData['mobile'] = GmSm::sm2Encrypt($responseData['mobile']);
        }

        if ($responseData['real_name'] ?? false) {
            $responseData['real_name'] = GmSm::sm2Encrypt($responseData['real_name']);
        }

        if ($responseData['identity'] ?? false) {
            $responseData['identity'] = GmSm::sm2Encrypt($responseData['identity']);
        }


        return Respond::success($responseData);
    }

    private function buildRedirectUri(string $redirectUri, string $code, ?string $state, string $openId): string
    {
        $query = http_build_query(array_filter([
            'code'    => $code,
            'state'   => $state,
            //            'open_id' => $openId,
        ]));

        $separator = parse_url($redirectUri, PHP_URL_QUERY) ? '&' : '?';

        return $redirectUri . $separator . $query;
    }

    /**
     * 使用授权码获取访问令牌
     * @throws OAuthException
     */
    public function issueToken(Request $request)
    {
        $request->validate([
            'grant_type'    => [
                'required',
                'in:authorization_code,refresh_token',
            ],
            'client_id'     => 'required',
            //            'client_access_secret' => 'required',
            'code'          => 'required_if:grant_type,authorization_code',
            'refresh_token' => 'required_if:grant_type,refresh_token',
        ], $this->validationMessages);

        //        if (!OAuthCacher::validateClientCredentials($request->client_id, $request->client_access_secret)) {
        //            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        //        }

        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client || $client->is_revoked) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        if ($request->grant_type === 'authorization_code') {
            return $this->handleAuthorizationCode($request->code, $client);
        }

        return $this->handleRefreshToken($request->refresh_token, $client);
    }

    public function jssdkConfig(Request $request) {}

    private function validateScopes(array $requestedScopes, array $allowedScopes): bool
    {
        return empty(array_diff($requestedScopes, $allowedScopes));
    }

    private function validateRedirectUri(?string $requestUri, ?array $safeDomains): bool
    {
        // 验证uri是否为合法url, 并且域名在安全域名列表中
        if (!filter_var($requestUri, FILTER_VALIDATE_URL)) {
            return false;
        }

        $parsedUrl = parse_url($requestUri);
        $host = $parsedUrl['host'] ?? '';

        if (in_array($host, $safeDomains)) {
            return true;
        }

        return false;
    }

    /**
     * @throws OAuthException
     */
    private function handleAuthorizationCode(string $code, Client $client)
    {
        $authCode = AuthCode::where('id', $code)
            ->where('client_id', $client->id)
            ->where('is_revoked', AuthCode::IS_REVOKED_NO)
            ->where('expires_at', '>', now())
            ->first();

        if (!$authCode) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CODE_INVALIDATE);
        }
        // 检查用户授权状态
        //        $authorization = OAuthUserAuthorization::where('user_uuid', $authCode->user_uuid)
        //                                               ->where('client_id', $client->id)
        //                                               ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO)
        //                                               ->first();

        if (!OAuthCacher::isUserAuthorized($authCode->user_uuid, $client->id)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_USER_UNAUTHORIZED);
        }

        // 创建或获取用户绑定
        //        $userBinding = OAuthUserBinding::firstOrCreate([
        //            'user_uuid' => $authCode->user_uuid,
        //            'client_id' => $client->id,
        //        ], ['open_id' => $this->openIDGenerater->generateOpenID($client->id, $authCode->user_uuid)]);

        $userBinding = OAuthCacher::getUserBinding($authCode->user_uuid, $client->id);

        if (!$userBinding) {
            $userBinding = OAuthUserBinding::create([
                'user_uuid' => $authCode->user_uuid,
                'user_type' => $authCode->user_type,
                'client_id' => $client->id,
                'open_id'   => $this->openIDGenerater->generateOpenID($client->id, $authCode->user_uuid),
            ]);

            OAuthCacher::invalidateUserBinding($authCode->user_uuid, $client->id);
        }

        // 创建访问令牌
        $accessToken = $this->createAccessToken($authCode);

        $authCode->update(['is_revoked' => AuthCode::IS_REVOKED_YES]);

        return $this->tokenResponse($accessToken, $userBinding);
    }

    /**
     * @throws OAuthException
     */
    private function handleRefreshToken(string $refreshTokenId, Client $client)
    {
        $refreshToken = RefreshToken::where('id', $refreshTokenId)
            ->where('is_revoked', RefreshToken::IS_REVOKED_NO)
            ->where('expires_at', '>', now())
            ->first();

        if (!$refreshToken) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_REFRESH_TOKEN_INVALIDATE);
        }

        $oldToken = $refreshToken->accessToken;
        if ($oldToken->client_id !== $client->id) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_REFRESH_TOKEN_INVALIDATE);
        }

        // 撤销旧令牌
        $oldToken->update(['is_revoked' => AccessToken::IS_REVOKED_YES]);
        $refreshToken->update(['is_revoked' => RefreshToken::IS_REVOKED_YES]);

        OAuthCacher::invalidateAccessToken($oldToken->id);

        // 创建新令牌
        $accessToken = $this->createAccessToken($oldToken);

        //        $userBinding = OAuthUserBinding::where('user_uuid', $accessToken->user_uuid)
        //                                       ->where('client_id', $client->id)
        //                                       ->firstOrFail();

        $userBinding = OAuthCacher::getUserBinding($accessToken->user_uuid, $client->id);
        if (!$userBinding) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_OPNEID_NOT_FOUND);
        }

        return $this->tokenResponse($accessToken, $userBinding);
    }

    /**
     * @param $source
     *
     * @return AccessToken
     */
    private function createAccessToken($source)
    {
        // 过往accessToken 失效
        //        AccessToken::where('user_uuid', $source->user_uuid)
        //                   ->where('client_id', $source->client_id)
        //                   ->where('revoked', false)
        //                   ->where('expires_at', '>', now())
        //                   ->update(['revoked' => true]);

        // 创建新accessToken
        $accessToken = AccessToken::create([
            'id'         => Str::random(64),
            'user_uuid'  => $source->user_uuid,
            'user_type'  => $source->user_type,
            'client_id'  => $source->client_id,
            'scopes'     => $source->scopes,
            'expires_at' => now()->addDays(30),
        ]);

        RefreshToken::create([
            'id'              => Str::random(64),
            'access_token_id' => $accessToken->id,
            'expires_at'      => now()->addDays(90),
        ]);

        return $accessToken;
    }

    /**
     * @param AccessToken $token
     * @param OAuthUserBinding $binding
     *
     * @return \Illuminate\Http\JsonResponse
     */
    private function tokenResponse(AccessToken $token, OAuthUserBinding $binding)
    {
        return Respond::success([
            'access_token'       => $token->id,
            'expires_in'         => (int)Carbon::now()
                ->diffInSeconds($token->expires_at),
            'refresh_token'      => $token->refreshToken->id,
            'refresh_expires_in' => (int)Carbon::now()
                ->diffInSeconds($token->refreshToken->expires_at),
            'open_id'            => $binding->open_id,
        ]);
    }

    /**
     * 创建或获取用户绑定并生成openid
     */
    private function ensureUserBinding(string $userUuid, Client $client, string $userType = 'user'): OAuthUserBinding
    {
        $userBinding = OAuthCacher::getUserBinding($userUuid, $client->id);
        if ($userBinding) {
            return $userBinding;
        }

        $userBinding = OAuthUserBinding::create([
            'user_uuid' => $userUuid,
            'user_type' => $userType,
            'client_id' => $client->id,
            'open_id'   => $this->openIDGenerater->generateOpenID($client->id, $userUuid),
        ]);

        // 更新缓存
        OAuthCacher::invalidateUserBinding($userUuid, $client->id);

        return $userBinding;
    }

    /**
     * 获取部门列表
     * @throws OAuthException
     */
    public function getDepartmentList(Request $request)
    {
        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        if ($client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_DENIED);
        }

        // 检查是否有部门列表权限
        if (!in_array(OAuthScopeEnum::DEPARTMENTLIST->value, $client->allowed_admin_scopes)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_DENIED);
        }

        $departments = AdminDepartment::with('parentDepartment')
            ->where('status', AdminDepartment::STATUS_ENABLED)
            ->get();

        return Respond::success($departments->map(function ($department) {
            return [
                'id'          => $department->id,
                'name'        => $department->name,
                'code'        => $department->code,
                'level'       => $department->level,
                'full_name'   => $department->full_name,
                'status'      => $department->status,
                'status_text' => $department->status_text,
                'remark'      => $department->remark,
                'parent'      => $department->parentDepartment ? [
                    'id'   => $department->parentDepartment->id,
                    'code' => $department->parentDepartment->code,
                    'name' => $department->parentDepartment->name,
                ] : null,
            ];
        }));
    }

    /**
     * 获取管理员列表
     * @throws OAuthException
     */
    public function getAdminList(Request $request)
    {
        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        if ($client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_DENIED);
        }

        // 检查是否有部门列表权限
        if (!in_array(OAuthScopeEnum::ACCESS_ADMIN_LIST->value, $client->allowed_admin_scopes)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_DENIED);
        }

        $admins = AdminUser::with([
            'departments',
            'oAuthUserBinding' => function ($query) use ($client) {
                $query->where('client_id', $client->id);
            },
        ])
            ->whereHas('oAuthUserBinding', function ($query) use ($client) {
                $query->where('client_id', $client->id);
            })
            ->where('status', AdminUser::STATUS_ENABLED)
            ->get();

        return Respond::success($admins->map(function ($admin) {
            return [
                'open_id'      => $admin->oAuthUserBinding?->open_id,
                'union_id'     => $admin->uuid,
                'true_name'    => $admin->true_name,
                'display_name' => $admin->display_name,
                'nickname'     => $admin->nickname,
                'mobile'       => $admin->mobile,
                'departments'  => $admin->departments->map(function ($department) {
                    return [
                        'id'        => $department->id,
                        'code'      => $department->code,
                        'name'      => $department->name,
                        'is_leader' => $department->pivot->is_leader,
                    ];
                }),
            ];
        }));
    }

    public function getFullAdminContacts(Request $request)
    {
        $client = OAuthCacher::getClientByKey(Tools::getClientId());

        if (!$client) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        if ($client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_DENIED);
        }

        if (!in_array(OAuthScopeEnum::FULL_ADMIN_CONTACTS->value, $client->allowed_admin_scopes)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_DENIED);
        }

        $admins = AdminUser::with([
            'departments',
            'oAuthUserBinding' => function ($query) use ($client) {
                $query->where('client_id', $client->id);
            },
        ])
            ->whereHas('oAuthUserBinding', function ($query) use ($client) {
                $query->where('client_id', $client->id);
            })
            ->where('status', AdminUser::STATUS_ENABLED)
            ->get();

        return Respond::success($admins->map(function ($admin) {
            return [
                'open_id'      => $admin->oAuthUserBinding?->open_id,
                'union_id'     => $admin->uuid,
                'true_name'    => $admin->true_name,
                'display_name' => $admin->display_name,
                'nickname'     => $admin->nickname,
                'mobile'       => $admin->mobile,
                'departments'  => $admin->departments->map(function ($department) {
                    return [
                        'id'        => $department->id,
                        'code'      => $department->code,
                        'name'      => $department->name,
                        'is_leader' => $department->pivot->is_leader,
                    ];
                }),
            ];
        }));
    }
}
