<?php

namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Admin User Resource
 * @property-read \App\Models\AdminUser $resource
 * @mixin \App\Models\AdminUser
 */
class AdminUserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'uuid'            => $this->uuid,
            'username'        => $this->username,
            'true_name'       => $this->true_name,
            'display_name'    => $this->display_name,
            'mobile'          => $this->mobile,
            'status'          => $this->status,
            'status_text'     => $this->status_text,
            'avatar'          => $this->avatar,
            'display_avatar'  => $this->display_avatar,
            'last_login_ip'   => $this->last_login_ip,
            'last_login_time' => $this->last_login_time,
            'roles'           => RoleResource::collection($this->whenLoaded('roles')),
            'permissions'     => PermissionResource::collection($this->whenLoaded('permissions')),
            'departments'     => DepartmentResource::collection($this->whenLoaded('departments')),
            'enterprise'      => EnterpriseResource::make($this->whenLoaded('enterprise')),
            'bind_user'       => $this->whenLoaded('bindUser', function () {
                return $this->bindUser->only(['nickname', 'mobile', 'display_avatar']);
            }),
            'created_at'      => $this->created_at,
        ];
    }
}
