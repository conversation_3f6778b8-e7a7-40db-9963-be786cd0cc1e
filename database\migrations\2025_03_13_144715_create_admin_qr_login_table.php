<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_qr_login', function (Blueprint $table) {
            $table->id();
            $table->string('code', 64)->unique()->comment('二维码唯一标识');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态：0-未扫码，1-已扫码待确认，2-已确认登录，3-已取消，4-已过期');
            $table->unsignedBigInteger('admin_id')->nullable()->comment('确认登录的管理员ID');
            $table->string('user_uuid', 36)->nullable()->comment('扫码的前端用户UUID');
            $table->string('device_id', 255)->nullable()->comment('设备ID');
            $table->string('client_ip', 64)->nullable()->comment('客户端IP');
            $table->string('user_agent', 255)->nullable()->comment('客户端UA');
            $table->timestamp('scanned_at')->nullable()->comment('扫码时间');
            $table->timestamp('confirmed_at')->nullable()->comment('确认时间');
            $table->datetime('expired_at')->comment('过期时间');
            $table->timestamps();
            
            $table->index('code');
            $table->index('status');
            $table->index('admin_id');
            $table->index('expired_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_qr_login');
    }
};
