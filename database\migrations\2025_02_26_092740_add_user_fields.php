<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedTinyInteger('comment_confirmed')
                  ->after('comment')
                  ->default(0)
                  ->nullable()
                  ->comment('签名是否确认');
        });

        Schema::table('admin_users', function (Blueprint $table) {
            $table->string('display_name')
                  ->after('true_name')
                  ->nullable()
                  ->comment('显示名字');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('comment_confirmed');
        });

        Schema::table('admin_users', function (Blueprint $table) {
            $table->dropColumn('display_name');
        });
    }
};
