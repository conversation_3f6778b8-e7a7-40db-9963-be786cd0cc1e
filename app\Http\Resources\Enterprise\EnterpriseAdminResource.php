<?php

namespace App\Http\Resources\Enterprise;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * EnterpriseAdmin Resource
 * @property-read \App\Models\EnterpriseAdmin $resource
 * @mixin \App\Models\EnterpriseAdmin
 */
class EnterpriseAdminResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'enterprise_id' => $this->enterprise_id,
            'user_id' => $this->user_id,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'remark' => $this->remark,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // 关联数据
            'enterprise' => $this->whenLoaded('enterprise', function () {
                return [
                    'id' => $this->enterprise->id,
                    'name' => $this->enterprise->name,
                    'code' => $this->enterprise->code,
                    'status' => $this->enterprise->status,
                    'status_text' => $this->enterprise->status_text,
                ];
            }),
            
            'user' => $this->whenLoaded('user', function () {
                return [
                    'uuid' => $this->user->uuid,
                    'username' => $this->user->username,
                    'nickname' => $this->user->nickname,
                    'mobile' => $this->user->mobile,
                    'avatar' => $this->user->display_avatar,
                    'status' => $this->user->status,
                ];
            }),
            
            'roles' => $this->whenLoaded('roles', function () {
                return $this->roles->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'description' => $role->description,
                    ];
                });
            }),
            
            'clients' => $this->whenLoaded('clients', function () {
                return $this->clients->map(function ($client) {
                    return [
                        'id' => $client->id,
                        'name' => $client->name,
                        'client_key' => $client->client_key,
                        'client_type' => $client->client_type,
                        'status' => $client->status,
                        'audit_status_text' => $client->audit_status_text,
                        'pivot' => [
                            'status' => $client->pivot->status,
                            'remark' => $client->pivot->remark,
                        ],
                    ];
                });
            }),
            
            'owned_clients' => $this->whenLoaded('ownedClients', function () {
                return $this->ownedClients->map(function ($client) {
                    return [
                        'id' => $client->id,
                        'name' => $client->name,
                        'client_key' => $client->client_key,
                        'client_type' => $client->client_type,
                        'status' => $client->status,
                        'audit_status_text' => $client->audit_status_text,
                    ];
                });
            }),
        ];
    }
}
