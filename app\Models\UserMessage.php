<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $user_uuid 用户ID
 * @property int $push_message_id 消息ID
 * @property int $is_read 是否已读:0未读,1已读
 * @property \Illuminate\Support\Carbon|null $read_time 阅读时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read mixed $is_read_str
 * @property-read \App\Models\PushMessage|null $pushMessage
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage whereIsRead($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage wherePushMessageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage whereReadTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage whereUserUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserMessage withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperUserMessage
 */
class UserMessage extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'user_uuid',
        'push_message_id',
        'is_read',
        'read_time',
    ];

    protected $casts = [
        'read_time' => 'datetime',
    ];

    const IS_READ_NO = 0;
    const IS_READ_YES = 1;

    public static array $isReadMap = [
        self::IS_READ_YES => '已读',
        self::IS_READ_NO => '未读',
    ];

    protected function isReadStr(): Attribute {
        return Attribute::make(get: fn($value) => self::$isReadMap[$value]);
    }

    public function pushMessage(): BelongsTo {
        return $this->belongsTo(PushMessage::class);
    }
}
