<?php

namespace App\Models;

use App\Enums\PushMessageCategoryEnum;
use App\Enums\PushMessageDeliveryEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $code 模板唯一编码
 * @property string $name 模板名称
 * @property string $title 消息标题模板
 * @property string $content 消息内容模板
 * @property int|null $show_client_info 是否显示客户端信息
 * @property int $category 消息分类:1系统通知,2活动通知,3@我的,4钱包通知,5被评论,6被点赞,7关注通知
 * @property int $delivery_type 目标类型:1安卓设备,2iOS设备,3用户,5实时活动pushToken,6实时活动activityId,7鸿蒙设备
 * @property string|null $cloud_template_id 云推送模板ID
 * @property int $is_silent 是否静默推送 0否 1是
 * @property string|null $show_toast 是否显示通知栏
 * @property array<array-key, mixed>|null $allowed_params 允许的模板参数定义:{key:描述}
 * @property array<array-key, mixed>|null $allowed_extend_params 允许的扩展参数定义:{key:描述}
 * @property int $status 状态:1启用,0禁用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $xiaomi_channel_id
 * @property string|null $vivo_category
 * @property string|null $oppo_channel_id
 * @property string|null $oppo_category
 * @property string|null $huawei_channel_category
 * @property string|null $huawei_local_category
 * @property string|null $harmony_channel_category
 * @property-read mixed $category_text
 * @property-read mixed $is_silent_text
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OauthClientTemplate> $oauthClientTemplates
 * @property-read int|null $oauth_client_templates_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\PushMessage> $pushMessages
 * @property-read int|null $push_messages_count
 * @property-read mixed $show_client_info_text
 * @property-read mixed $show_toast_text
 * @property-read mixed $status_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereAllowedExtendParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereAllowedParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereCloudTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereDeliveryType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereHarmonyChannelCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereHuaweiChannelCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereHuaweiLocalCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereIsSilent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereOppoCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereOppoChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereShowClientInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereShowToast($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereVivoCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate whereXiaomiChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PushTemplate withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperPushTemplate
 */
class PushTemplate extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'code',
        'name',
        'title',
        'content',
        'category',
        'delivery_type',
        'cloud_template_id',
        'is_silent',
        'show_toast',
        'allowed_params',
        'allowed_extend_params',
        'status',
        'xiaomi_channel_id',
        'vivo_category',
        'oppo_channel_id',
        'oppo_category',
        'huawei_channel_category',
        'huawei_local_category',
        'harmony_channel_category',
        'show_client_info',
    ];

    protected $casts = [
        'allowed_params'        => 'array',
        'allowed_extend_params' => 'array',
    ];

    protected $appends = [
        'status_text',
        'is_silent_text',
        'category_text',
        'show_toast_text',
    ];

    const STATUS_DISABLE = 0;
    const STATUS_ENABLE = 1;

    public static array $statusMap = [
        self::STATUS_DISABLE => '禁用',
        self::STATUS_ENABLE  => '启用',
    ];

    const IS_SILENT_NO = 0;
    const IS_SILENT_YES = 1;

    public static array $isSilentMap = [
        self::IS_SILENT_YES => '应用内消息',
        self::IS_SILENT_NO  => '通知栏消息',
    ];

    const SHOW_TOAST_NO = 0;
    const SHOW_TOAST_YES = 1;

    public static array $showToastMap = [
        self::SHOW_TOAST_YES => '展示Toast',
        self::SHOW_TOAST_NO  => '不展示Toast',
    ];

    const SHOW_CLIENT_INFO_NO = 0;
    const SHOW_CLIENT_INFO_YES = 1;

    public static array $showClientInfoMap = [
        self::SHOW_CLIENT_INFO_YES => '展示应用信息',
        self::SHOW_CLIENT_INFO_NO  => '不展示应用信息',
    ];

    protected function showToastText(): Attribute {
        return Attribute::make(get: fn() => self::$showToastMap[$this->show_toast] ?? '未知');
    }

    protected function isSilentText(): Attribute {
        return Attribute::make(get: fn() => self::$isSilentMap[$this->is_silent] ?? '未知');
    }

    protected function statusText(): Attribute {
        return Attribute::make(get: fn() => self::$statusMap[$this->status] ?? '未知');
    }

    protected function showClientInfoText(): Attribute {
        return Attribute::make(get: fn() => self::$showClientInfoMap[$this->show_client_info] ?? '未知');
    }

    protected function categoryText(): Attribute {
        return Attribute::make(get: fn() => PushMessageCategoryEnum::from($this->category)
                                                                   ->label());
    }

    public function isBroadcast() {
        return $this->delivery_type == PushMessageDeliveryEnum::DELIVERY_ALL->value;
    }

    public function oauthClientTemplates(): HasMany {
        return $this->hasMany(OauthClientTemplate::class);
    }

    public function pushMessages(): HasMany {
        return $this->hasMany(PushMessage::class);
    }
}
