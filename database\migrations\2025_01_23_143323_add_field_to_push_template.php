<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('push_templates', function (Blueprint $table) {
            $table->string('show_toast')
                  ->default(\App\Models\PushTemplate::SHOW_TOAST_YES)
                  ->nullable()
                  ->after('is_silent')
                  ->comment('是否显示通知栏');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('push_templates', function (Blueprint $table) {
            $table->dropColumn('show_toast');
        });
    }
};
