<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->renameColumn('revoked', 'is_revoked');
        });

        Schema::table('oauth_auth_codes', function (Blueprint $table) {
            $table->renameColumn('revoked', 'is_revoked');
        });

        Schema::table('oauth_access_tokens', function (Blueprint $table) {
            $table->renameColumn('revoked', 'is_revoked');
        });

        Schema::table('oauth_refresh_tokens', function (Blueprint $table) {
            $table->renameColumn('revoked', 'is_revoked');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('oauth_clients', function (Blueprint $table) {
            $table->renameColumn('is_revoked', 'revoked');
        });

        Schema::table('oauth_auth_codes', function (Blueprint $table) {
            $table->renameColumn('is_revoked', 'revoked');
        });

        Schema::table('oauth_access_tokens', function (Blueprint $table) {
            $table->renameColumn('is_revoked', 'revoked');
        });

        Schema::table('oauth_refresh_tokens', function (Blueprint $table) {
            $table->renameColumn('is_revoked', 'revoked');
        });
    }
};
