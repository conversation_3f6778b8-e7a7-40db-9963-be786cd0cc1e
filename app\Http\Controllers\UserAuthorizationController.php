<?php

namespace App\Http\Controllers;

use App\Events\UnAuthorizedEvent;
use App\Http\Resources\AuthorizationResouce;
use App\Http\Resources\ClientResource;
use App\Models\Client;
use App\Models\AccessToken;
use App\Models\OAuthUserAuthorization;
use App\Models\OAuthUserBinding;
use App\Models\RefreshToken;
use App\Utils\OAuthCacher;
use App\Utils\Respond;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class UserAuthorizationController extends Controller
{
    /**
     * 获取用户授权的应用列表
     */
    public function authorizedApplications()
    {
        $user = Auth::guard('api')->user();

        $authorizations = OAuthUserAuthorization::with([
            'client' => function ($query) {
                $query->select('id', 'icon', 'client_key', 'client_type', 'name', 'provider', 'description');
            },
        ])
            ->where('user_uuid', $user->getAuthIdentifier())
            ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO)
            ->orderByDesc('authorized_at')
            ->paginate();

        return Respond::success(AuthorizationResouce::collection($authorizations));
    }

    /**
     * 获取特定应用的授权详情
     */
    public function applicationDetail(Request $request, $authoirzationId)
    {
        $user = Auth::guard('api')->user();

        $authorization = OAuthUserAuthorization::with([
            'client' => function ($query) {
                return $query->select('id', 'icon', 'client_key', 'client_type', 'name', 'provider', 'description');
            },
        ])
            ->where('id', $authoirzationId)
            ->where('user_uuid', $user->getAuthIdentifier())
            ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO)
            ->firstOrFail();

        return Respond::success(AuthorizationResouce::make($authorization));
    }

    /**
     * 撤销应用授权
     * @throws \Throwable
     */
    public function revokeAuthorization(Request $request, $authoirzationId): JsonResponse
    {
        $user = Auth::guard('api')->user();

        $authorization = OAuthUserAuthorization::where('id', $authoirzationId)
            ->where('user_uuid', $user->uuid)
            ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO)
            ->firstOrFail();

        $client = $authorization->client;

        // 开启事务确保数据一致性
        \DB::transaction(function () use ($authorization, $user, $client) {
            // 撤销授权记录
            $authorization->update([
                'is_revoked' => OAuthUserAuthorization::IS_REVOKED_YES,
                'revoked_at' => Carbon::now(),
            ]);

            // 撤销所有活跃的访问令牌
            AccessToken::where('user_uuid', $user->uuid)
                ->where('client_id', $client->id)
                ->where('is_revoked', AccessToken::IS_REVOKED_NO)
                ->where('expires_at', '>', Carbon::now())
                ->update(['is_revoked' => AccessToken::IS_REVOKED_YES]);

            // 撤销所有相关的刷新令牌
            \DB::table('oauth_refresh_tokens')
                ->whereIn('access_token_id', function ($query) use ($user, $client) {
                    $query->select('id')
                        ->from('oauth_access_tokens')
                        ->where('user_uuid', $user->uuid)
                        ->where('client_id', $client->id);
                })
                ->update(['is_revoked' => RefreshToken::IS_REVOKED_YES]);

            OAuthCacher::invalidateUserAuthorization($user->uuid, $client->id);
            OAuthCacher::invalidateUserBinding($user->uuid, $client->id);
            OAuthCacher::invalidateAccessToken($user->uuid, $client->id);

            event(new UnAuthorizedEvent($client, $user));
        });

        return Respond::success();
    }

    /**
     * 获取活跃令牌数量
     */
    private function getActiveTokensCount(string $userUuid, int $clientId): int
    {
        return AccessToken::where('user_uuid', $userUuid)
            ->where('client_id', $clientId)
            ->where('is_revoked', AccessToken::IS_REVOKED_NO)
            ->where('expires_at', '>', Carbon::now())
            ->count();
    }

    /**
     * 获取活跃令牌详情
     */
    private function getActiveTokensDetail(string $userUuid, int $clientId): array
    {
        return AccessToken::where('user_uuid', $userUuid)
            ->where('client_id', $clientId)
            ->where('is_revoked', AccessToken::IS_REVOKED_NO)
            ->where('expires_at', '>', Carbon::now())
            ->get()
            ->map(function ($token) {
                return [
                    'id' => substr($token->id, 0, 8) . '...',
                    // 只显示部分token
                    'created_at' => $token->created_at->format('Y-m-d H:i:s'),
                    'expires_at' => $token->expires_at->format('Y-m-d H:i:s'),
                    'expires_in' => Carbon::now()
                        ->diffInSeconds($token->expires_at),
                    'scopes' => $token->scopes,
                ];
            })
            ->toArray();
    }
}
