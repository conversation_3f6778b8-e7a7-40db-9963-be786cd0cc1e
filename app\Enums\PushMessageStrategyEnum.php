<?php

namespace App\Enums;

enum PushMessageStrategyEnum: int
{

    // 定时策略类型
    case STRATEGY_IMMEDIATE = 0; // 立即发送
    case STRATEGY_FIXED = 1;     // 定时发送
    case STRATEGY_CIRCLE = 2;    // 循环发送


    public function label(): string {
        return match ($this) {
            self::STRATEGY_IMMEDIATE => '立即发送',
            self::STRATEGY_FIXED => '定时发送',
            self::STRATEGY_CIRCLE => '循环发送',
            default => '',
        };
    }

    public static function options(): array {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }
}
