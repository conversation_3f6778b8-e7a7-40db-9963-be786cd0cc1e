<?php


/*
|--------------------------------------------------------------------------
| 测试路由
|--------------------------------------------------------------------------
*/

use App\Http\Controllers\TestController;

//                  Route::post('upload/callback/{driver}', [App\Http\Controllers\MaterialController::class, 'callback'])->name('api.upload.callback');
Route::post('check-signature', [TestController::class, 'checkSignature']); // 签名校验
Route::post('event-callback', [TestController::class, 'eventCall']);       // 事件回调
Route::post('check-captcha', [TestController::class, 'checkCaptcha']);     // 验证码校验
Route::post('push/personal', [TestController::class, 'testPersonalPush']);
Route::post('push/silent', [TestController::class, 'testSilentPush']);
Route::post('push/broadcast', [TestController::class, 'testBroadcastPush']);
Route::post('access-token', [TestController::class, 'testAccessToken']);
Route::post('user-info', [TestController::class, 'testUserInfo']);
Route::post('input', [TestController::class, 'testInput']);
