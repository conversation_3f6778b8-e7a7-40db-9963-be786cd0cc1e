<?php

namespace App\Exceptions;

class SignatureException extends BaseException
{
    // 参数缺失, 重复请求, 签名错误, 签名过期, 非法参数
//    const PARAMS_MISSING = 120001;
//    const REPLAY_ATTACK = 120002;
//    const SIGNATURE_ERROR = 120003;
//    const SIGNATURE_EXPIRED = 120004;
//    const ILLEGAL_PARAMS = 120005;
//
//    public static function message($code) {
//        $msgArr = [
//            static::PARAMS_MISSING => '参数缺失',
//            static::REPLAY_ATTACK => '重复请求',
//            static::SIGNATURE_ERROR => '非法请求',
//            static::SIGNATURE_EXPIRED => '签名过期',
//            static::ILLEGAL_PARAMS => '非法参数',
//        ];
//
//        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
//    }

}
