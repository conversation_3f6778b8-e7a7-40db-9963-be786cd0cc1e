<?php

namespace App\Http\Controllers;

use AlibabaCloud\SDK\Dypnsapi\V20170525\Dypnsapi;
use AlibabaCloud\SDK\Dypnsapi\V20170525\Models\GetMobileRequest;
use App\Enums\ErrorCodeEnum;
use App\Events\UserLogoutEvent;
use App\Exceptions\AuthException;
use App\Exceptions\UserException;
use App\Models\AdminUser;
use App\Models\Client;
use App\Models\User;
use App\Services\JwtWithSm2\SMProvider;
use App\Utils\GmSm;
use App\Utils\Respond;
use App\Utils\Tools;
use AppleSignIn\ASDecoder;
use Carbon\Carbon;
use Darabonba\OpenApi\Models\Config;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Overtrue\LaravelSocialite\Socialite;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;
use Tymon\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    protected $validationMessages = [
        'username.required'             => '用户名不能为空',
        'password.required'             => '密码不能为空',
        'mobile.required'               => '手机号不能为空',
        'mobile.is_mobile'              => '请输入正确的手机号格式',
        'code.required'                 => '验证码不能为空',
        'code.check_code'               => '验证码不正确',
        'third_platform.required'       => '第三方平台不能为空',
        'third_platform.in'             => '不支持的第三方平台',
        'third_code.required'           => '第三方授权码不能为空',
        'apple_identity_token.required' => 'Apple授权信息不能为空',
        'oc_access_token.required'      => '一键登录token不能为空',
        'access_token.required'         => '授权token不能为空',
        'user_id.required'              => '用户ID不能为空',
    ];

    /**
     * @throws ValidationException
     * @throws \Exception
     */
    public function login(Request $request, $type = null) {
        //        $this->validate($request, [
        //            'captcha_scene' => 'required',
        //            'captcha_verify_param' => 'required|checkRobotAnalyze',
        //        ], $this->validationMessages);

        $user = match ($type) {
            'sms' => $this->_loginWithSmsCode($request),
            'third' => $this->_loginWithThird($request),
            'wechat_mini' => $this->_loginWithWechatMini($request),
            'apple_id' => $this->_loginWithAppleId($request),
            'oneclick' => $this->_loginWithOneclick($request),
            default => $this->_loginWithPwd($request),
        };

        if (!$user) {
            throw new AuthException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        if (!$user->isMobileConfirmed()) {
            // 如果用户没有绑定手机号码，返回需要绑定手机号码的错误
            $bindMobileToken = Str::random(64);
            Cache::put('bind_mobile_token_' . $user->uuid, $bindMobileToken, 300);

            return Respond::success([
                'need_bind_mobile' => true,
                'user_id'          => $user->uuid,
                'access_token'     => $bindMobileToken,
                'expires_in'       => 300,
            ], '请绑定手机号码');
        }

        $user->last_login_ip = Tools::getClientIp();
        $user->last_login_date = Carbon::now();
        $user->save();

        $accessToken = Auth::guard('api')
                           ->fromUser($user);

        $clientAppAccessTokens = [];
        if ($request->input('with_applications')) {
            // 生成clientAppAccessTokens
            $clientAppAccessTokens = Client::where('is_system', Client::IS_SYSTEM_YES)
                                           ->where('is_revoked', Client::IS_REVOKED_NO)
                                           ->get()
                                           ->mapWithKeys(function ($client) use ($user) {
                                               try {
                                                   $encryptData = json_encode($user->base_info);

                                                   $cacheKey = config('sm2.cache.prefix') . ":" . $client->client_key . ":" . md5($encryptData);

                                                   if (!($result = Cache::get($cacheKey))) {

                                                       GmSm::configure(publicKey: $client->client_access_key);

                                                       $encrypted = GmSm::sm2Encrypt($encryptData);
                                                       $result = 'sm2:' . base64_encode($encrypted);

                                                       Cache::put($cacheKey, $result, now()->addMinutes(config('sm2.cache.ttl')));
                                                   }

                                                   $user->setAttribute('encrypt_base_info', $result);
                                                   $payload = app('tymon.jwt')
                                                       ->makePayload($user)
                                                       ->toArray();
                                                   $payload['client_id'] = $client->client_key;

                                                   $accessToken = (new SMProvider(secret: config('jwt.secret'), algo: SMProvider::ALGO_SM2, keys: [
                                                       'private' => $client->client_access_secret,
                                                       'public'  => $client->client_access_key,
                                                   ], withKey: true))->encode($payload);
                                               } catch (\Exception $e) {
                                                   $accessToken = '';
                                               }

                                               return [
                                                   $client->client_key => $accessToken,
                                               ];
                                           });
        }

        return Respond::success([
            'need_bind_mobile'         => false,
            'user_id'                  => $user->uuid,
            'access_token'             => $accessToken,
            'access_token_for_clients' => $clientAppAccessTokens,
            'expires_in'               => JWTAuth::factory()
                                                 ->getTTL() * 60,
        ], '登录成功');
    }

    public function logout() {
        $user = Auth::guard('api')
                    ->user();

        Auth::guard('api')
            ->logout(true);

        // 触发登出事件
        event(new UserLogoutEvent($user->getAuthIdentifier()));

        return Respond::success();
    }

    public function refresh(Request $request) {
        $token = Auth::guard('api')
                     ->refresh(true);

        Auth::guard('api')
            ->setToken($token);

        $user = Auth::guard('api')
                    ->user();

        $clientAppAccessTokens = [];
        if ($request->input('with_applications')) {

            // 生成clientAppAccessTokens
            $clientAppAccessTokens = Client::where('is_system', Client::IS_SYSTEM_YES)
                                           ->where('is_revoked', Client::IS_REVOKED_NO)
                                           ->get()
                                           ->mapWithKeys(function ($client) use ($user) {
                                               try {
                                                   $encryptData = json_encode($user->base_info);

                                                   $cacheKey = config('sm2.cache.prefix') . ":" . $client->client_key . ":" . md5($encryptData);

                                                   if (!($result = Cache::get($cacheKey))) {
                                                       GmSm::configure(publicKey: $client->client_access_key);

                                                       $encrypted = GmSm::sm2Encrypt($encryptData);
                                                       $result = 'sm2:' . base64_encode($encrypted);

                                                       Cache::put($cacheKey, $result, now()->addMinutes(config('sm2.cache.ttl')));
                                                   }

                                                   $user->setAttribute('encrypt_base_info', $result);
                                                   $payload = app('tymon.jwt')
                                                       ->makePayload($user)
                                                       ->toArray();
                                                   $payload['client_id'] = $client->client_key;

                                                   $accessToken = (new SMProvider(secret: config('jwt.secret'), algo: SMProvider::ALGO_SM2, keys: [
                                                       'private' => $client->client_access_secret,
                                                       'public'  => $client->client_access_key,
                                                   ], withKey: true))->encode($payload);
                                               } catch (\Exception $e) {
                                                   $accessToken = '';
                                               }

                                               return [
                                                   $client->client_key => $accessToken,
                                               ];
                                           });
        }

        return response()->json([
            'user_id'                  => $user->uuid,
            'access_token'             => $token,
            'access_token_for_clients' => $clientAppAccessTokens,
            'expires_in'               => JWTAuth::factory()
                                                 ->getTTL() * 60,
        ]);
    }

    public function checkAuth() {
        return Respond::success([
            'status' => Auth::guard('api')
                            ->check(),
        ]);
    }

    /**
     * @throws UserException
     * @throws ValidationException
     * @throws AuthException
     */
    public function bindMobile(Request $request) {
        //        $this->validate($request, [
        //            'captcha_scene' => 'required',
        //            'captcha_verify_param' => 'required|checkRobotAnalyze',
        //        ], $this->validationMessages);

        $this->validate($request, [
            'access_token' => 'required',
            'user_id'      => 'required',
            'mobile'       => 'required|isMobile',
            'code'         => 'required|checkCode:' . $request->input('mobile'),
        ], $this->validationMessages);

        // 验证access_token 是否存在且正确
        $bindMobileToken = Cache::get('bind_mobile_token_' . $request->input('user_id'));

        if (!$bindMobileToken || $bindMobileToken != $request->input('access_token')) {
            throw new AuthException(ErrorCodeEnum::BIND_PHONE_TIMEOUT);
        }

        // 检查手机号已经绑定过
        $user = User::withTrashed()
                    ->where('mobile', $request->input('mobile'))
                    ->first();

        if ($user) {
            throw new AuthException(ErrorCodeEnum::PHONE_ALREADY_BIND);
        }

        $user = User::where('uuid', $request->input('user_id'))
                    ->first();

        if (!$user) {
            throw new AuthException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        if ($user->isMobileConfirmed()) {
            throw new AuthException(ErrorCodeEnum::PHONE_ALREADY_BIND);
        }

        // 删除绑定手机号的缓存
        Cache::forget('bind_mobile_token_' . $request->input('user_id'));

        $user->mobile = $request->input('mobile');
        $user->mobile_confirmed = User::CONFIRMED_YES;
        $user->last_login_ip = Tools::getClientIp();
        $user->last_login_date = Carbon::now();
        $user->save();

        $accessToken = Auth::guard('api')
                           ->fromUser($user);

        $clientAppAccessTokens = [];
        if ($request->input('with_applications')) {
            // 生成clientAppAccessTokens
            $clientAppAccessTokens = Client::where('is_system', Client::IS_SYSTEM_YES)
                                           ->where('is_revoked', Client::IS_REVOKED_NO)
                                           ->get()
                                           ->mapWithKeys(function ($client) use ($user) {
                                               try {
                                                   $encryptData = json_encode($user->base_info);

                                                   $cacheKey = config('sm2.cache.prefix') . ":" . $client->client_key . ":" . md5($encryptData);

                                                   if (!($result = Cache::get($cacheKey))) {

                                                       GmSm::configure(publicKey: $client->client_access_key);

                                                       $encrypted = GmSm::sm2Encrypt($encryptData);
                                                       $result = 'sm2:' . base64_encode($encrypted);

                                                       Cache::put($cacheKey, $result, now()->addMinutes(config('sm2.cache.ttl')));
                                                   }

                                                   $user->setAttribute('encrypt_base_info', $result);
                                                   $payload = app('tymon.jwt')
                                                       ->makePayload($user)
                                                       ->toArray();
                                                   $payload['client_id'] = $client->client_key;

                                                   $accessToken = (new SMProvider(secret: config('jwt.secret'), algo: SMProvider::ALGO_SM2, keys: [
                                                       'private' => $client->client_access_secret,
                                                       'public'  => $client->client_access_key,
                                                   ], withKey: true))->encode($payload);
                                               } catch (\Exception $e) {
                                                   $accessToken = '';
                                               }

                                               return [
                                                   $client->client_key => $accessToken,
                                               ];
                                           });
        }

        return Respond::success([
            'need_bind_mobile'         => false,
            'user_id'                  => $user->uuid,
            'access_token'             => $accessToken,
            'access_token_for_clients' => $clientAppAccessTokens,
            'expires_in'               => JWTAuth::factory()
                                                 ->getTTL() * 60,
        ], '登录成功');
    }

    /**
     * @throws ValidationException
     * @throws AuthException
     */
    protected function _loginWithPwd(Request $request): User {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required',
        ], $this->validationMessages);

        $user = User::where('username', $request->input('username'))
                    ->orWhere('mobile', $request->input('username'))
                    ->first();
        if (!$user) {
            throw new AuthException(ErrorCodeEnum::USER_NOT_FOUND);
        }

        if (!password_verify($request->input('password'), $user->password)) {
            throw new AuthException(ErrorCodeEnum::USER_PASSWORD_ERROR);
        }

        return $user;
    }

    /**
     * @throws AuthException
     * @throws ValidationException
     */
    protected function _loginWithSmsCode(Request $request): User {
        $this->validate($request, [
            'mobile' => 'required|isMobile',
            'code'   => 'required|checkCode:' . $request->input('mobile'),
        ], $this->validationMessages);

        $user = User::where('mobile', $request->input('mobile'))
                    ->first();

        if ($user) {
            $this->_checkUserStatus($user);
        } else {
            $user = User::create([
                'uuid'                    => Str::uuid()
                                                ->toString(),
                'username'                => 'mc_' . Str::random(8),
                'password'                => 0,
                'nickname'                => '常友_' . Str::random(8),
                'nickname_confirmed'      => User::CONFIRMED_YES,
                'gender'                  => User::GENDER_UNKNOWN,
                'status'                  => User::STATUS_ACTIVE,
                'mobile'                  => $request->input('mobile'),
                'mobile_confirmed'        => User::CONFIRMED_YES,
                'avatar'                  => ['path' => config('uc.default_avatar'), 'provider' => 'url', 'uuid' => ''],
                'avatar_confirmed'        => User::CONFIRMED_YES,
                'realname_auth_confirmed' => User::CONFIRMED_NO,
                'register_date'           => Carbon::now(),
                'register_ip'             => Tools::getClientIp(),
                'last_login_date'         => Carbon::now(),
                'last_login_ip'           => Tools::getClientIp(),
                'comment'                 => '',
                'comment_confirmed'       => User::CONFIRMED_YES,
                'register_env_appid'      => Tools::getAppId(),
                // 'register_env_uni_platform' => Tools::getUniPlatform(),
                'register_env_os_name'    => Tools::getOs(),
                // 'register_env_app_name'   => Tools::getAppName(),
                'register_env_app_version' => Tools::getAppVersion(),
                // 'register_env_app_version_code' => Tools::getAppVersionCode(),
                'register_env_channel'    => Tools::getChannel(),
                'register_env_client_ip'  => Tools::getClientIp(),

            ]);

            // 检查绑定管理员
            $this->_checkBindAdmin($user);
        }

        return $user;
    }

    /**
     * @throws AuthException
     * @throws ValidationException
     */
    protected function _loginWithThird(Request $request): User {
        $this->validate($request, [
            'third_platform' => 'required|in:' . implode(',', array_keys(User::$thirdPlatformMap)),
            'third_code'     => 'required',
        ], $this->validationMessages);

        try {
            $thirdUser = Socialite::create($request->input('third_platform'))
                                  ->userFromCode($request->input('third_code'));
        } catch (ValidationException $exception) {
            throw new AuthException(ErrorCodeEnum::THIRD_AUTH_FAILED);
        }

        if (!$thirdUser->getId()) {
            throw new AuthException(ErrorCodeEnum::THIRD_AUTH_FAILED);
        }

        $user = Cache::lock('third_' . $thirdUser->getId(), 10)
                     ->get(function () use ($thirdUser, $request) {
                         if ($request->input('third_platform') == User::THIRD_PLATFORM_WECHAT) {
                             $unionid = key_exists('unionid', $thirdUser->getRaw()) ? $thirdUser->getRaw()['unionid'] : null;
                             $user = User::where('wx_unionid', $unionid)
                                         ->orWhere('wx_openids->app', $thirdUser->getId())
                                         ->first();

                             if ($user) {
                                 $this->_checkUserStatus($user);
                             } else {
                                 $user = User::create([
                                     'uuid'               => Str::uuid()
                                                                ->toString(),
                                     'username'           => 'th_' . Str::random(8),
                                     'password'           => 0,
                                     'nickname'           => $thirdUser->getNickname(),
                                     'nickname_confirmed' => User::CONFIRMED_YES,
                                     'comment'            => '',
                                     'comment_confirmed'  => User::CONFIRMED_YES,
                                     'avatar'             => [
                                         'path'     => $thirdUser->getAvatar() ?? config('uc.default_avatar'),
                                         'provider' => 'url',
                                         'uuid'     => '',
                                     ],
                                     'avatar_confirmed'   => User::CONFIRMED_YES,
                                     'gender'             => User::GENDER_UNKNOWN,
                                     'status'             => User::STATUS_ACTIVE,
                                     'wx_unionid'         => $unionid ?: '',
                                     'wx_openids'         => [
                                         'app' => $thirdUser->getId(),
                                     ],
                                     'third_party'        => [
                                         'app_wechat_nickname'      => $thirdUser->getNickname(),
                                         'app_wechat_access_token'  => $thirdUser->getAccessToken(),
                                         'app_wechat_refresh_token' => $thirdUser->getRefreshToken(),
                                         'app_wechat_expires_in'    => $thirdUser->getExpiresIn(),
                                     ],
                                     'register_date'      => Carbon::now(),
                                     'register_ip'        => Tools::getClientIp(),
                                     'last_login_date'    => Carbon::now(),
                                     'last_login_ip'      => Tools::getClientIp(),

                                     'register_env_appid'      => Tools::getAppId(),
                                     // 'register_env_uni_platform' => Tools::getUniPlatform(),
                                     'register_env_os_name'    => Tools::getOs(),
                                     // 'register_env_app_name'   => Tools::getAppName(),
                                     'register_env_app_version' => Tools::getAppVersion(),
                                     // 'register_env_app_version_code' => Tools::getAppVersionCode(),
                                     'register_env_channel'    => Tools::getChannel(),
                                     'register_env_client_ip'  => Tools::getClientIp(),
                                 ]);
                             }

                             return $user;
                         } else {
                             return false;
                         }
                     });

        if (!$user) {
            throw new TooManyRequestsHttpException(10, '操作过于频繁，请稍后再试！');
        }

        return $user;
    }

    protected function _loginWithWechatMini(Request $request): User {
        
    }

    /**
     * @throws AuthException
     * @throws ValidationException
     */
    protected function _loginWithAppleId(Request $request): User {
        $this->validate($request, [
            'apple_identity_token' => 'required',
        ], $this->validationMessages);

        try {
            $appleSignInPayload = ASDecoder::getAppleSignInPayload($request->input('apple_identity_token'));

            $appleUser = $appleSignInPayload->getUser();
        } catch (ValidationException $exception) {
            throw new AuthException(ErrorCodeEnum::THIRD_AUTH_FAILED);
        }

        $user = Cache::lock('apple_' . $appleUser, 10)
                     ->get(function () use ($appleUser, $request) {
                         $user = User::where('apple_openid', $appleUser)
                                     ->first();

                         if ($user) {
                             $this->_checkUserStatus($user);
                         } else {
                             $user = User::create([
                                 'uuid'                    => Str::uuid()
                                                                 ->toString(),
                                 'username'                => 'apple_' . Str::random(8),
                                 'password'                => 0,
                                 'nickname'                => '常友_' . Str::random(8),
                                 'nickname_confirmed'      => User::CONFIRMED_YES,
                                 'comment'                 => '',
                                 'comment_confirmed'       => User::CONFIRMED_YES,
                                 'gender'                  => User::GENDER_UNKNOWN,
                                 'status'                  => User::STATUS_ACTIVE,
                                 'mobile'                  => '',
                                 'mobile_confirmed'        => User::CONFIRMED_NO,
                                 'avatar'                  => [
                                     'path'     => config('uc.default_avatar'),
                                     'provider' => 'url',
                                     'uuid'     => '',
                                 ],
                                 'avatar_confirmed'        => User::CONFIRMED_YES,
                                 'realname_auth_confirmed' => User::CONFIRMED_NO,
                                 'register_date'           => Carbon::now(),
                                 'register_ip'             => Tools::getClientIp(),
                                 'last_login_date'         => Carbon::now(),
                                 'last_login_ip'           => Tools::getClientIp(),
                                 'apple_openid'            => $appleUser,

                                 'register_env_appid'      => Tools::getAppId(),
                                 // 'register_env_uni_platform' => Tools::getUniPlatform(),
                                 'register_env_os_name'    => Tools::getOs(),
                                 // 'register_env_app_name'   => Tools::getAppName(),
                                 'register_env_app_version' => Tools::getAppVersion(),
                                 // 'register_env_app_version_code' => Tools::getAppVersionCode(),
                                 'register_env_channel'    => Tools::getChannel(),
                                 'register_env_client_ip'  => Tools::getClientIp(),
                             ]);
                         }

                         return $user;
                     });

        if (!$user) {
            throw new TooManyRequestsHttpException(10, '操作过于频繁，请稍后再试！');
        }

        return $user;
    }

    /**
     * @throws ValidationException
     * @throws \Exception
     */
    protected function _loginWithOneclick(Request $request): User {
        $this->validate($request, [
            'oc_access_token' => 'required',
        ], $this->validationMessages);

        $config = new Config([
            'accessKeyId'     => config('uc.mobile_auth.aliyun.access_key_id'),
            'accessKeySecret' => config('uc.mobile_auth.aliyun.access_key_secret'),
        ]);

        $config->endpoint = 'dypnsapi.aliyuncs.com';
        $client = new Dypnsapi($config);

        $outId = Str::uuid()
                    ->toString();

        $getMobileRequest = new GetMobileRequest([
            'accessToken' => $request->input('oc_access_token'),
            'outId'       => $outId,
        ]);

        try {
            $res = $client->getMobile($getMobileRequest);

            if ($res->statusCode == 200 && $res->body->code == 'OK' && $res->body->getMobileResultDTO->mobile) {
                $mobile = $res->body->getMobileResultDTO->mobile;

                $user = Cache::lock('oc_' . $mobile, 10)
                             ->get(function () use ($mobile, $request) {
                                 $user = User::where('mobile', $mobile)
                                             ->first();

                                 if ($user) {
                                     $this->_checkUserStatus($user);
                                 } else {
                                     $user = User::create([
                                         'uuid'                    => Str::uuid()
                                                                         ->toString(),
                                         'username'                => 'oc_' . Str::random(8),
                                         'password'                => 0,
                                         'nickname'                => '常友_' . Str::random(8),
                                         'nickname_confirmed'      => User::CONFIRMED_YES,
                                         'comment'                 => '',
                                         'comment_confirmed'       => User::CONFIRMED_YES,
                                         'gender'                  => User::GENDER_UNKNOWN,
                                         'status'                  => User::STATUS_ACTIVE,
                                         'mobile'                  => $mobile,
                                         'mobile_confirmed'        => User::CONFIRMED_YES,
                                         'avatar'                  => [
                                             'path'     => config('uc.default_avatar'),
                                             'provider' => 'url',
                                             'uuid'     => '',
                                         ],
                                         'avatar_confirmed'        => User::CONFIRMED_YES,
                                         'realname_auth_confirmed' => User::CONFIRMED_NO,
                                         'register_date'           => Carbon::now(),
                                         'register_ip'             => Tools::getClientIp(),
                                         'last_login_date'         => Carbon::now(),
                                         'last_login_ip'           => Tools::getClientIp(),

                                         'register_env_appid'      => Tools::getAppId(),
                                         // 'register_env_uni_platform' => Tools::getUniPlatform(),
                                         'register_env_os_name'    => Tools::getOs(),
                                         // 'register_env_app_name'   => Tools::getAppName(),
                                         'register_env_app_version' => Tools::getAppVersion(),
                                         // 'register_env_app_version_code' => Tools::getAppVersionCode(),
                                         'register_env_channel'    => Tools::getChannel(),
                                         'register_env_client_ip'  => Tools::getClientIp(),
                                     ]);

                                     // 检查绑定管理员
                                     $this->_checkBindAdmin($user);
                                }

                                 return $user;
                             });

                if (!$user) {
                    throw new TooManyRequestsHttpException(10, '操作过于频繁，请稍后再试！');
                }

                return $user;
            } else {
                throw new AuthException(ErrorCodeEnum::ONECLICK_GET_PHONE_FAILED, [
                    'message' => $res->body->message,
                    'code'    => $res->body->code,
                ]);
            }
        } catch (\Exception $e) {
            throw new AuthException(ErrorCodeEnum::ONECLICK_GET_PHONE_FAILED, [
                'message' => $e->getMessage(),
                'code'    => $e->getCode(),
            ]);
        }
    }

    /**
     * @throws AuthException
     */
    private function _checkUserStatus(User $user) {
        if ($user->trashed() || $user->status != User::STATUS_ACTIVE) {
            throw new AuthException(ErrorCodeEnum::USER_STATUS_ERROR);
        }
    }

    /**
     * @throws AuthException
     */
    private function _checkBindAdmin(User $user) {
        $admin = AdminUser::where('mobile', $user->mobile)
                          ->where('status', AdminUser::STATUS_ENABLED)
                          ->whereNull('bind_user_uuid')
                          ->first();

        if ($admin) {
            $admin->bind_user_uuid = $user->uuid;
            $admin->save();
        }
    }
}
