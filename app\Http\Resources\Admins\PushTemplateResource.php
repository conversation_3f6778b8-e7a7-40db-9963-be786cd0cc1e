<?php

namespace App\Http\Resources\Admins;

use App\Enums\PushMessageCategoryEnum;
use App\Enums\PushMessageDeliveryEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class PushTemplateResource
 *
 * @property-read \App\Models\PushTemplate $resource
 * @mixin \App\Models\PushTemplate
 */
class PushTemplateResource extends JsonResource
{
    public function toArray(Request $request): array {
        return [
            'id'                       => $this->id,
            'code'                     => $this->code,
            'name'                     => $this->name,
            'title'                    => $this->title,
            'content'                  => $this->content,
            'category'                 => $this->category,
            'category_text'            => $this->category ? PushMessageCategoryEnum::from($this->category)
                                                                                   ->label() : '',
            'delivery_type'            => $this->delivery_type,
            'delivery_type_text'       => $this->delivery_type ? PushMessageDeliveryEnum::from($this->delivery_type)
                                                                                        ->label() : '',
            'allowed_params'           => $this->allowed_params,
            'allowed_extend_params'    => $this->allowed_extend_params,
            'status'                   => $this->status,
            'status_text'              => $this->status_text,
            'is_silent'                => $this->is_silent,
            'is_silent_text'           => $this->is_silent_text,
            'show_toast'               => $this->show_toast,
            'show_toast_text'          => $this->show_toast_text,
            'show_client_info'         => $this->show_client_info,
            'show_client_info_text'    => $this->show_client_info_text,
            'xiaomi_channel_id'        => $this->xiaomi_channel_id,
            'vivo_category'            => $this->vivo_category,
            'oppo_channel_id'          => $this->oppo_channel_id,
            'oppo_category'            => $this->oppo_category,
            'huawei_channel_category'  => $this->huawei_channel_category,
            'huawei_local_category'    => $this->huawei_local_category,
            'harmony_channel_category' => $this->harmony_channel_category,
        ];
    }
}
