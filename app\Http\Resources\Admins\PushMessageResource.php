<?php

namespace App\Http\Resources\Admins;

use App\Utils\Tools;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

/**
 * Push Message Resource
 * @property-read \App\Models\PushMessage $resource
 * @mixin \App\Models\PushMessage
 */
class PushMessageResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'msg_key' => $this->msg_key,
            'title' => $this->parseTemplateContent($this->title, $this->template_params ?? []),
            'content' => $this->parseTemplateContent($this->content, $this->template_params ?? []),
            $this->merge(Tools::mergeDateTimeFormat('push_time', $this->push_time)),
            'template_params' => $this->template_params ?? [],
            'extend_params' => Arr::except($this->extend_params ?? [], 'redirect_to'),
            'redirect_to' => $this->extend_params['redirect_to'] ?? '',
            'status' => $this->status,
            'status_text' => $this->status_text,
            'push_type' => $this->push_type,
            'push_type_text' => $this->push_type_text,
            'delivery_type' => $this->delivery_type,
            'client_info' => $this->whenLoaded('oauthClient', function () {
                return [
                    'id' => $this->oauthClient->id,
                    'name' => $this->oauthClient->name,
                    'icon' => $this->oauthClient->display_icon,
                ];
            }),
        ];
    }

    /**
     * 解析模板内容，替换占位符
     *
     * @param string $templateContent
     * @param array $params
     * @return string
     */
    private function parseTemplateContent(string $templateContent, array $params): string
    {
        foreach ($params as $key => $value) {
            $placeholder = '#{' . $key . '}#';
            $templateContent = str_replace($placeholder, $value, $templateContent);
        }

        return $templateContent;
    }
} 