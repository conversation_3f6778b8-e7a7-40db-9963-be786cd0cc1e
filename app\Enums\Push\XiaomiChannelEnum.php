<?php

namespace App\Enums\Push;

enum XiaomiChannelEnum: string
{
    case SYSTEM = '132246';      // 系统通知渠道
    case ACTIVITY = '132244';    // 活动通知渠道
    case MESSAGE = '132245';     // 消息通知渠道

    public function label(): string {
        return match ($this) {
            self::SYSTEM => '系统通知渠道',
            self::ACTIVITY => '活动通知渠道',
            self::MESSAGE => '消息通知渠道',
            default => '',
        };
    }

    public static function options(): array {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }
}