<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('push_templates', function (Blueprint $table) {
            $table->string('xiaomi_channel_id')
                  ->nullable();
            $table->string('vivo_category')
                  ->nullable();
            $table->string('oppo_channel_id')
                  ->nullable();
            $table->string('oppo_category')
                  ->nullable();
            $table->string('huawei_channel_category')
                  ->nullable();
            $table->string('huawei_local_category')
                  ->nullable();
            $table->string('harmony_channel_category')
                  ->nullable();
        });

        //        Schema::table('push_messages', function (Blueprint $table) {
        //            $table->string('xiaomi_channel_id')
        //                  ->nullable();
        //            $table->string('vivo_category')
        //                  ->nullable();
        //            $table->string('oppo_channel_id')
        //                  ->nullable();
        //            $table->string('oppo_category')
        //                  ->nullable();
        //            $table->string('huawei_channel_category')
        //                  ->nullable();
        //            $table->string('harmony_channel_category')
        //                  ->nullable();
        //        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('push_templates', function (Blueprint $table) {
            $table->dropColumn('xiaomi_channel_id');
            $table->dropColumn('vivo_category');
            $table->dropColumn('oppo_channel_id');
            $table->dropColumn('oppo_category');
            $table->dropColumn('huawei_channel_category');
            $table->dropColumn('huawei_local_category');
            $table->dropColumn('harmony_channel_category');
        });

        //        Schema::table('push_messages', function (Blueprint $table) {
        //            $table->dropColumn('xiaomi_channel_id');
        //            $table->dropColumn('vivo_category');
        //            $table->dropColumn('oppo_channel_id');
        //            $table->dropColumn('oppo_category');
        //            $table->dropColumn('huawei_channel_category');
        //            $table->dropColumn('harmony_channel_category');
        //        });
    }
};
