<?php

namespace App\Enums;

enum PushMessageCircelEnum: int
{

    // 循环类型
    case CIRCLE_DAILY = 1;   // 每天
    case CIRCLE_WEEKLY = 2;  // 每周
    case CIRCLE_MONTHLY = 3; // 每月

    public function label(): string {
        return match ($this) {
            self::CIRCLE_DAILY => '每天',
            self::CIRCLE_WEEKLY => '每周',
            self::CIRCLE_MONTHLY => '每月',
            default => '',
        };
    }
    public static function options(): array {
        return collect(self::cases())
            ->mapWithKeys(fn($item) => [
                $item->value => $item->label(),
            ])
            ->toArray();
    }
}
