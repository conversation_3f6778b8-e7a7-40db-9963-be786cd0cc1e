<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static bool can(string $permission, ?string $guard = null)
 * @method static bool cant(string $permission, ?string $guard = null)
 * @method static bool any(array $permissions, ?string $guard = null)
 * @method static bool all(array $permissions, ?string $guard = null)
 * @method static array getAllPermissions()
 * @method static array getPermissionsByGroup()
 * @method static array getRolePermissions(int $roleId)
 * @method static bool evaluateExpression(string $expression)
 * @method static string[] getModules()
 * @method static array getPermissionTree()
 * @method static array getGroupedPermissions()
 *
 * @see \App\Services\AdminPermissionService
 */
class AdminPermission extends Facade
{
    protected static function getFacadeAccessor() {
        return 'admin.permission';
    }
}
