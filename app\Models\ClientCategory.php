<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name 分类名称
 * @property string|null $description 分类描述
 * @property int $status 状态:1启用,0禁用
 * @property int $sort 排序
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Client> $clients
 * @property-read int|null $clients_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ClientCategory withoutTrashed()
 * @mixin \Eloquent
 * @mixin IdeHelperClientCategory
 */
class ClientCategory extends Model
{
    use SoftDeletes;

    protected $table = 'oauth_client_categories';

    protected $fillable = ['name', 'description', 'status', 'sort'];

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    public static array $statusMap = [
        self::STATUS_ACTIVE   => '启用',
        self::STATUS_INACTIVE => '禁用',
    ];

    public function clients()
    {
        return $this->hasMany(Client::class, 'category_id', 'id');
    }
}
