<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('materials', function (Blueprint $table) {
            $table->id();
            $table->uuid()->unique()->comment('uuid');
            $table->string('name')->nullable()->comment('名称');
            $table->string('path')->comment('路径');
            $table->string('provider')->comment('提供商');
            $table->string('format')->nullable()->comment('格式');
            $table->string('mini_type')->nullable()->comment('类型');
            $table->string('width')->nullable()->comment('宽度');
            $table->string('height')->nullable()->comment('高度');
            $table->string('size')->nullable()->comment('大小');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('materials');
    }
};
