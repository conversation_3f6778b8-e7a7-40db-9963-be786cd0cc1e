<?php

namespace App\Http\Enterprise;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Models\EnterpriseAdmin;
use App\Models\Enterprise;
use App\Models\User;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class EnterpriseAdminController extends EnterpriseBaseController
{
    protected const PERMISSION_MAP = [
        'index' => '企业管理员.查看列表',
        'store' => '企业管理员.创建',
        'show' => '企业管理员.查看详情',
        'update' => '企业管理员.编辑',
        'destroy' => '企业管理员.删除',
        'bindUser' => '企业管理员.绑定用户',
        'unbindUser' => '企业管理员.解绑用户',
    ];

    protected $validationMessages = [
        'enterprise_id.required' => '企业ID不能为空',
        'enterprise_id.exists' => '企业不存在',
        'user_id.required' => '用户ID不能为空',
        'user_id.exists' => '用户不存在',
        'user_id.unique' => '该用户已经是企业管理员',
        'status.required' => '状态不能为空',
        'status.integer' => '状态必须为整数',
        'status.in' => '状态值无效',
        'remark.string' => '备注必须为字符串',
        'remark.max' => '备注不能超过255个字符',
    ];

    /**
     * 获取企业管理员列表
     */
    public function index(Request $request)
    {
        $query = EnterpriseAdmin::with(['enterprise', 'user']);

        // 按企业筛选
        if ($request->filled('enterprise_id')) {
            $query->where('enterprise_id', $request->enterprise_id);
        }

        // 按状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 搜索用户
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('nickname', 'like', "%{$search}%")
                  ->orWhere('mobile', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        $admins = $query->orderByDesc('created_at')
                       ->paginate($request->get('per_page', 15));

        return Respond::success($admins);
    }

    /**
     * 创建企业管理员
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'enterprise_id' => 'required|exists:enterprises,id',
            'user_id' => [
                'required',
                'exists:users,uuid',
                Rule::unique('enterprise_admins')->where(function ($query) use ($request) {
                    return $query->where('enterprise_id', $request->enterprise_id);
                }),
            ],
            'status' => ['required', 'integer', Rule::in([EnterpriseAdmin::STATUS_ENABLED, EnterpriseAdmin::STATUS_DISABLED])],
            'remark' => 'nullable|string|max:255',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            // 检查用户是否已经是其他企业的管理员
            $existingAdmin = EnterpriseAdmin::where('user_id', $validated['user_id'])
                                          ->where('enterprise_id', '!=', $validated['enterprise_id'])
                                          ->first();

            if ($existingAdmin) {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '该用户已经是其他企业的管理员');
            }

            // 检查用户是否已经是企业拥有者
            $user = User::find($validated['user_id']);
            if ($user && $user->bindAdminUser && $user->bindAdminUser->type === 'enterprise') {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '该用户已经是企业拥有者，不能同时成为企业管理员');
            }

            $validated['created_by'] = auth('enterprise')->id();
            $admin = EnterpriseAdmin::create($validated);

            DB::commit();
            return Respond::success($admin->load(['enterprise', 'user']));
        } catch (\Exception $e) {
            DB::rollBack();
            if ($e instanceof AdminException) {
                throw $e;
            }
            return Respond::error('创建企业管理员失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取企业管理员详情
     */
    public function show($id)
    {
        $admin = EnterpriseAdmin::with(['enterprise', 'user', 'clients'])
                               ->findOrFail($id);
        
        return Respond::success($admin);
    }

    /**
     * 更新企业管理员
     */
    public function update(Request $request, $id)
    {
        $admin = EnterpriseAdmin::findOrFail($id);

        $validated = $request->validate([
            'status' => ['sometimes', 'required', 'integer', Rule::in([EnterpriseAdmin::STATUS_ENABLED, EnterpriseAdmin::STATUS_DISABLED])],
            'remark' => 'nullable|string|max:255',
        ], $this->validationMessages);

        try {
            DB::beginTransaction();

            $validated['updated_by'] = auth('enterprise')->id();
            $admin->update($validated);

            DB::commit();
            return Respond::success($admin->load(['enterprise', 'user']));
        } catch (\Exception $e) {
            DB::rollBack();
            return Respond::error('更新企业管理员失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除企业管理员
     */
    public function destroy($id)
    {
        $admin = EnterpriseAdmin::findOrFail($id);

        try {
            DB::beginTransaction();

            // 检查是否有关联的客户端
            if ($admin->clients()->exists()) {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '该管理员还有关联的客户端，无法删除');
            }

            $admin->delete();

            DB::commit();
            return Respond::success(null, '删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            if ($e instanceof AdminException) {
                throw $e;
            }
            return Respond::error('删除企业管理员失败: ' . $e->getMessage());
        }
    }

    /**
     * 绑定用户到企业管理员
     */
    public function bindUser(Request $request)
    {
        $validated = $request->validate([
            'enterprise_id' => 'required|exists:enterprises,id',
            'user_mobile' => 'required|exists:users,mobile',
        ]);

        try {
            DB::beginTransaction();

            $user = User::where('mobile', $validated['user_mobile'])->first();
            
            // 检查用户是否已经绑定了企业管理员
            if ($user->enterpriseAdmins()->exists()) {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '该用户已经绑定了企业管理员');
            }

            // 检查用户是否已经是企业拥有者
            if ($user->bindAdminUser && $user->bindAdminUser->type === 'enterprise') {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '该用户已经是企业拥有者，不能同时成为企业管理员');
            }

            $admin = EnterpriseAdmin::create([
                'enterprise_id' => $validated['enterprise_id'],
                'user_id' => $user->uuid,
                'status' => EnterpriseAdmin::STATUS_ENABLED,
                'created_by' => auth('enterprise')->id(),
            ]);

            DB::commit();
            return Respond::success($admin->load(['enterprise', 'user']));
        } catch (\Exception $e) {
            DB::rollBack();
            if ($e instanceof AdminException) {
                throw $e;
            }
            return Respond::error('绑定用户失败: ' . $e->getMessage());
        }
    }

    /**
     * 解绑用户
     */
    public function unbindUser(Request $request, $id)
    {
        $admin = EnterpriseAdmin::findOrFail($id);

        try {
            DB::beginTransaction();

            // 检查是否有关联的客户端
            if ($admin->clients()->exists()) {
                throw new AdminException(ErrorCodeEnum::ADMIN_VALIDATION_ERROR, '该管理员还有关联的客户端，无法解绑');
            }

            $admin->delete();

            DB::commit();
            return Respond::success(null, '解绑成功');
        } catch (\Exception $e) {
            DB::rollBack();
            if ($e instanceof AdminException) {
                throw $e;
            }
            return Respond::error('解绑用户失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取可绑定的用户列表
     */
    public function availableUsers(Request $request)
    {
        $query = User::whereDoesntHave('enterpriseAdmins')
                    ->whereDoesntHave('bindAdminUser', function ($q) {
                        $q->where('type', 'enterprise');
                    });

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nickname', 'like', "%{$search}%")
                  ->orWhere('mobile', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        $users = $query->select(['uuid', 'username', 'nickname', 'mobile', 'avatar'])
                      ->orderByDesc('created_at')
                      ->paginate($request->get('per_page', 15));

        return Respond::success($users);
    }
}
