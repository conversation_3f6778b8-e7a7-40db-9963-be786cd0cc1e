<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class EnterprisePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建企业相关权限
        $enterprisePermissions = [
            // 企业管理员管理
            '企业管理员.查看列表' => '查看企业管理员列表',
            '企业管理员.创建' => '创建企业管理员',
            '企业管理员.查看详情' => '查看企业管理员详情',
            '企业管理员.编辑' => '编辑企业管理员',
            '企业管理员.删除' => '删除企业管理员',
            '企业管理员.绑定用户' => '绑定用户为企业管理员',
            '企业管理员.解绑用户' => '解绑企业管理员',

            // 企业角色管理
            '企业角色管理.查看列表' => '查看企业角色列表',
            '企业角色管理.创建' => '创建企业角色',
            '企业角色管理.查看详情' => '查看企业角色详情',
            '企业角色管理.编辑' => '编辑企业角色',
            '企业角色管理.删除' => '删除企业角色',
            '企业角色管理.分配角色' => '为管理员分配角色',
            '企业角色管理.撤销角色' => '撤销管理员角色',
            '企业角色管理.查看权限' => '查看企业权限列表',

            // 企业应用管理
            '企业应用管理.查看列表' => '查看企业应用列表',
            '企业应用管理.创建' => '创建企业应用',
            '企业应用管理.查看详情' => '查看企业应用详情',
            '企业应用管理.编辑' => '编辑企业应用',
            '企业应用管理.删除' => '删除企业应用',
            '企业应用管理.审核通过' => '审核通过企业应用',
            '企业应用管理.审核拒绝' => '审核拒绝企业应用',
            '企业应用管理.查看待审核' => '查看待审核应用',

            // 企业部门管理
            '企业部门管理.查看列表' => '查看企业部门列表',
            '企业部门管理.创建' => '创建企业部门',
            '企业部门管理.查看详情' => '查看企业部门详情',
            '企业部门管理.编辑' => '编辑企业部门',
            '企业部门管理.删除' => '删除企业部门',

            // 企业联系人管理
            '企业联系人管理.查看列表' => '查看企业联系人列表',
            '企业联系人管理.创建' => '创建企业联系人',
            '企业联系人管理.查看详情' => '查看企业联系人详情',
            '企业联系人管理.编辑' => '编辑企业联系人',
            '企业联系人管理.删除' => '删除企业联系人',
            '企业联系人管理.批量导入' => '批量导入企业联系人',
        ];

        $this->command->info('创建企业权限...');
        foreach ($enterprisePermissions as $name => $description) {
            Permission::firstOrCreate([
                'name' => $name,
                'guard_name' => 'enterprise',
            ], [
                'description' => $description,
            ]);
        }

        // 创建默认的企业角色
        $defaultRoles = [
            '企业超级管理员' => [
                'description' => '拥有所有企业权限',
                'permissions' => array_keys($enterprisePermissions), // 所有权限
            ],
            '企业应用管理员' => [
                'description' => '管理企业应用',
                'permissions' => [
                    '企业应用管理.查看列表',
                    '企业应用管理.创建',
                    '企业应用管理.查看详情',
                    '企业应用管理.编辑',
                    '企业应用管理.删除',
                    '企业应用管理.查看待审核',
                ],
            ],
            '企业部门管理员' => [
                'description' => '管理企业部门和联系人',
                'permissions' => [
                    '企业部门管理.查看列表',
                    '企业部门管理.创建',
                    '企业部门管理.查看详情',
                    '企业部门管理.编辑',
                    '企业部门管理.删除',
                    '企业联系人管理.查看列表',
                    '企业联系人管理.创建',
                    '企业联系人管理.查看详情',
                    '企业联系人管理.编辑',
                    '企业联系人管理.删除',
                    '企业联系人管理.批量导入',
                ],
            ],
            '企业人事管理员' => [
                'description' => '管理企业管理员和角色',
                'permissions' => [
                    '企业管理员.查看列表',
                    '企业管理员.创建',
                    '企业管理员.查看详情',
                    '企业管理员.编辑',
                    '企业管理员.删除',
                    '企业管理员.绑定用户',
                    '企业管理员.解绑用户',
                    '企业角色管理.查看列表',
                    '企业角色管理.查看详情',
                    '企业角色管理.分配角色',
                    '企业角色管理.撤销角色',
                    '企业角色管理.查看权限',
                ],
            ],
        ];

        $this->command->info('创建企业角色...');
        foreach ($defaultRoles as $roleName => $roleData) {
            $role = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'enterprise',
            ], [
                'description' => $roleData['description'],
            ]);

            // 为角色分配权限
            $permissions = Permission::where('guard_name', 'enterprise')
                                   ->whereIn('name', $roleData['permissions'])
                                   ->get();
            
            $role->syncPermissions($permissions);
            
            $this->command->info("角色 '{$roleName}' 已创建并分配 " . count($permissions) . " 个权限");
        }

        $this->command->info('企业权限和角色初始化完成！');
    }
}
