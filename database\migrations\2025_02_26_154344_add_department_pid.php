<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('admin_departments', function (Blueprint $table) {
            $table->integer('parent_id')
                  ->after('code')
                  ->default(0)
                  ->nullable()
                  ->comment('父级部门ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('admin_departments', function (Blueprint $table) {
            $table->dropColumn('parent_id');
        });
    }
};
