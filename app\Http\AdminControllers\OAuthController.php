<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Enums\OAuthScopeEnum;
use App\Exceptions\OAuthException;
use App\Http\Controllers\Controller;
use App\Http\Resources\ClientResource;
use App\Models\AccessToken;
use App\Models\AuthCode;
use App\Models\Client;
use App\Models\OAuthUserAuthorization;
use App\Models\OAuthUserBinding;
use App\Models\RefreshToken;
use App\Models\User;
use App\Utils\OAuthCacher;
use App\Utils\OpenIDGenerater;
use App\Utils\RedirectToBuilder;
use App\Utils\Respond;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class OAuthController extends Controller
{

    private OpenIDGenerater $openIDGenerater;

    protected $validationMessages = [
        'grant_type.required_if'        => '授权类型为:value时不能为空',
        'code.required_if'              => '当授权类型为authorization_code时授权码不能为空',
        'refresh_token.required_if'     => '当授权类型为refresh_token时刷新令牌不能为空',
        'client_id.required_with'       => '当提供回调地址时客户端ID不能为空',
        'redirect_uri.required_with'    => '当提供客户端ID时回调地址不能为空',
        'state.required_with'           => '当提供回调地址时state参数不能为空',
        'client_id.required'            => '客户端ID不能为空',
        'redirect_uri.required'         => '回调地址不能为空',
        'redirect_uri.url'              => '回调地址格式不正确',
        'scopes.string'                 => '授权范围格式不正确',
        'state.max'                     => '状态参数长度不能超过255个字符',
        'response_type.required'        => '响应类型不能为空',
        'response_type.in'              => '不支持的响应类型',
        'grant_type.required'           => '授权类型不能为空',
        'grant_type.in'                 => '不支持的授权类型',
        'client_access_secret.required' => '客户端密钥不能为空',
        'code.required'                 => '授权码不能为空',
        'refresh_token.required'        => '刷新令牌不能为空',
        'name.required'                 => '名称不能为空',
        'name.max'                      => '名称不能超过50个字符',
        'template_code.required'        => '模板代码不能为空',
        'template_code.max'             => '模板代码不能超过50个字符',
        'description.max'               => '描述不能超过200个字符',
    ];

    public function __construct(OpenIDGenerater $openIDGenerater) {
        $this->openIDGenerater = $openIDGenerater;
    }

    /**
     * @throws OAuthException
     */
    public function checkAuthStatus(Request $request) {
        $request->validate([
            'client_id'    => 'required',
            'redirect_uri' => 'required|url',
        ], $this->validationMessages);

        $client = OAuthCacher::getClientByKey($request->client_id);
        if (!$client || $client->is_revoked || $client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }
        /** @var \App\Models\Admin $user */
        $user = Auth::guard('admin')
                    ->user();

        if (!$user->canAccessOauthClient($client->id)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_CLIENT_DENIED, [
                'client'     => [
                    'icon'        => $client->display_icon,
                    'name'        => $client->name,
                    'provider'    => $client->provider,
                    'description' => $client->description,
                ],
                'admin_user' => [
                    'true_name' => $user->true_name,
                    'avatar'    => $user->display_avatar,
                ],
            ]);
        }

        if (OAuthCacher::isUserAuthorized($user->uuid, $client->id)) {

            $userBinding = OAuthCacher::getUserBinding($user->uuid, $client->id);

            if ($userBinding) {
                // 已授权用户，直接生成新的auth code
                $authCode = AuthCode::create([
                    'id'         => Str::random(40),
                    'user_uuid'  => $user->uuid,
                    'user_type'  => $userBinding->user_type,
                    'client_id'  => $client->id,
                    'scopes'     => OAuthCacher::getUserAuthorizedScopes($user->uuid, $client->id),
                    'expires_at' => now()->addMinutes(10),
                ]);

                // $redirectUri = $request->redirect_uri ?: $client->default_redirect_url;
                $redirectUri = $request->redirect_uri;
                if (!$this->validateRedirectUri($redirectUri, $client->auth_safe_domains)) {
                    throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_REDIRECT_INVALIDATE, [
                        'client'     => [
                            'icon'        => $client->display_icon,
                            'name'        => $client->name,
                            'provider'    => $client->provider,
                            'description' => $client->description,
                        ],
                        'admin_user' => [
                            'true_name' => $user->true_name,
                            'avatar'    => $user->display_avatar,
                        ],
                    ]);
                }

                return Respond::success([
                    'status'       => 'authorized',
                    'client'       => [
                        'icon'        => $client->display_icon,
                        'name'        => $client->name,
                        'provider'    => $client->provider,
                        'description' => $client->description,
                    ],
                    'admin_user'   => [
                        'true_name' => $user->true_name,
                        'avatar'    => $user->display_avatar,
                    ],
                    'redirect_url' => $this->buildRedirectUri($redirectUri, $authCode->id, $request->state),
                ]);
            }
        }

        return Respond::success([
            'status'     => 'unauthorized',
            'client'     => [
                'icon'        => $client->display_icon,
                'name'        => $client->name,
                'provider'    => $client->provider,
                'description' => $client->description,
            ],
            'admin_user' => [
                'true_name' => $user->true_name,
                'avatar'    => $user->display_avatar,
            ],
        ]);
    }

    /**
     * 获取客户端信息和授权页面所需数据
     * @throws OAuthException
     */
    public function getClientInfo($clientKey) {
        $client = OAuthCacher::getClientByKey($clientKey);
        if (!$client || $client->is_revoked || $client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        return Respond::success(ClientResource::make($client));
    }

    /**
     * 处理用户授权并返回授权码
     * @throws OAuthException
     */
    public function authorize(Request $request) {
        $request->validate([
            'client_id'     => 'required',
            'redirect_uri'  => 'required|url',
            'scopes'        => 'nullable|string',
            'state'         => 'nullable|string|max:255',
            'response_type' => [
                'required',
                'in:code',
            ],
        ], $this->validationMessages);

        $client = OAuthCacher::getClientByKey($request->client_id);
        if (!$client || $client->is_revoked || $client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        /** @var \App\Models\Admin $user */
        $user = Auth::guard('admin')
                    ->user();

        if (!$user->canAccessOauthClient($client->id)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_ACCESS_CLIENT_DENIED, [
                'client'     => [
                    'icon'        => $client->display_icon,
                    'name'        => $client->name,
                    'provider'    => $client->provider,
                    'description' => $client->description,
                ],
                'admin_user' => [
                    'true_name' => $user->true_name,
                    'avatar'    => $user->display_avatar,
                ],
            ]);
        }

        // 验证redirect_uri和scope
        // $redirectUri = $request->redirect_uri ?: $client->default_redirect_url;
        $redirectUri = $request->redirect_uri;
        if (!$this->validateRedirectUri($redirectUri, $client->auth_safe_domains)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_REDIRECT_INVALIDATE);
        }

        $requestedScopes = $request->scopes ? explode(' ', $request->scopes) : $client->allowed_scopes;

        if (!$this->validateScopes($requestedScopes, $client->allowed_scopes)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_SCOPES_ERROR);
        }

        // 确保用户绑定并获取openid
        $userBinding = $this->ensureUserBinding($user->uuid, $client);

        // 创建授权记录
        $authorization = OAuthUserAuthorization::updateOrCreate([
            'user_uuid' => $user->uuid,
            'user_type' => AuthCode::USER_TYPE_ADMIN,
            'client_id' => $client->id,
        ], [
            'granted_scopes' => $requestedScopes,
            'authorized_at'  => now(),
            'last_used_at'   => now(),
            'is_revoked'     => OAuthUserAuthorization::IS_REVOKED_NO,
            'revoked_at'     => null,
        ]);

        OAuthCacher::invalidateUserAuthorization($user->uuid, $client->id);

        // 生成授权码
        $authCode = AuthCode::create([
            'id'         => Str::random(40),
            'user_uuid'  => $user->uuid,
            'user_type'  => AuthCode::USER_TYPE_ADMIN,
            'client_id'  => $client->id,
            'scopes'     => $requestedScopes,
            'expires_at' => now()->addMinutes(10),
        ]);

        // 构建回调URL
        $redirectUri = $this->buildRedirectUri($redirectUri, $authCode->id, $request->state);

        return Respond::success([
            //            'client'       => [
            //                'client_id'   => $client->client_key,
            //                'client_type' => $client->client_type,
            //            ],
            //            'code'         => $authCode->id,
            'redirect_uri' => $redirectUri,
            //            'open_id'      => $userBinding->open_id,
        ]);
    }

    /**
     * 获取用户信息
     * @throws OAuthException
     */
    public function getUserInfo(Request $request) {
        $request->validate([
            'access_token' => 'required|string',
            'open_id'      => 'required|string',
        ], $this->validationMessages);

        //        $token = AccessToken::where('id', $request->access_token)
        //                            ->where('is_revoked', AccessToken::IS_REVOKED_NO)
        //                            ->where('expires_at', '>', now())
        //                            ->first();

        $token = OAuthCacher::getAccessToken($request->access_token);

        if (!$token) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_TOKEN_INVALIDATE);
        }

        //        $userBinding = OAuthUserBinding::where('open_id', $request->open_id)
        //                                       ->where('client_id', $token->client_id)
        //                                       ->first();

        $userBinding = OAuthCacher::getUserBindingByOpenId($request->open_id, $token->client_id);

        if (!$userBinding || $userBinding->user_uuid !== $token->user_uuid) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_OPNEID_NOT_FOUND);
        }

        $user = $token->user;

        if (!$user || !$user->isActive()) {
            throw new OAuthException(ErrorCodeEnum::USER_STATUS_ERROR);
        }

        // 根据授权scope返回相应的用户信息
        $responseData = [
            'open_id'  => $userBinding->open_id,
            'nickname' => $user->nickname,
            'avatar'   => $user->display_avatar,
            'gender'   => $user->gender_txt,
        ];

        // 根据token的scope返回额外的用户信息
        if (in_array('mobile', $token->scopes)) {
            $responseData['mobile'] = $user->mobile;
        }

        if (in_array('identity', $token->scopes)) {
            $responseData['real_name'] = $user->realname_auth_data[User::REALNAME_AUTH_NAME];
            $responseData['identity'] = $user->realname_auth_data[User::REALNAME_AUTH_IDENTITY];
        }

        return Respond::success($responseData);
    }

    private function buildRedirectUri(string $redirectUri, string $code, ?string $state): string {
        // $query = http_build_query(array_filter([
        //     'code'  => $code,
        //     'state' => $state,
        // ]));

        // $separator = parse_url($redirectUri, PHP_URL_QUERY) ? '&' : '?';

        // return $redirectUri . $separator . $query;

        return RedirectToBuilder::addQueryParams($redirectUri, [
            'code'  => $code,
            'state' => $state,
        ]);
    }

    /**
     * 使用授权码获取访问令牌
     * @throws OAuthException
     */
    public function issueToken(Request $request) {
        $request->validate([
            'grant_type'           => [
                'required',
                'in:authorization_code,refresh_token',
            ],
            'client_id'            => 'required',
            'client_access_secret' => 'required',
            'code'                 => 'required_if:grant_type,authorization_code',
            'refresh_token'        => 'required_if:grant_type,refresh_token',
        ], $this->validationMessages);

        if (!OAuthCacher::validateClientCredentials($request->client_id, $request->client_access_secret)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        //        $client = Client::where('client_key', $request->client_id)
        //                        ->where('client_access_secret', $request->client_access_secret)
        //                        ->where('is_revoked', Client::IS_REVOKED_NO)
        //                        ->whereNot('client_type', Client::TYPE_BACKEND)
        //                        ->first();

        $client = OAuthCacher::getClientByKey($request->client_id);

        if (!$client || $client->is_revoked || $client->is_workspace_client !== Client::IS_WORKSPACE_CLIENT_YES) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CLIENT_NOT_FOUND);
        }

        if ($request->grant_type === 'authorization_code') {
            return $this->handleAuthorizationCode($request->code, $client);
        }

        return $this->handleRefreshToken($request->refresh_token, $client);
    }

    public function jssdkConfig(Request $request) {

    }

    private function validateScopes(array $requestedScopes, array $allowedScopes): bool {
        return empty(array_diff($requestedScopes, $allowedScopes));
    }


    private function validateRedirectUri(?string $requestUri, ?array $safeDomains = []): bool {
        // 验证uri是否为合法url, 并且域名在安全域名列表中
        if (!filter_var($requestUri, FILTER_VALIDATE_URL)) {
            return false;
        }

        $parsedUrl = parse_url($requestUri);
        $host = $parsedUrl['host'] ?? '';

        if (in_array($host, $safeDomains)) {
            return true;
        }

        return false;
    }

    /**
     * @throws OAuthException
     */
    private function handleAuthorizationCode(string $code, Client $client) {
        $authCode = AuthCode::where('id', $code)
                            ->where('client_id', $client->id)
                            ->where('is_revoked', AuthCode::IS_REVOKED_NO)
                            ->where('expires_at', '>', now())
                            ->first();

        if (!$authCode) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_CODE_INVALIDATE);
        }
        // 检查用户授权状态
        //        $authorization = OAuthUserAuthorization::where('user_uuid', $authCode->user_uuid)
        //                                               ->where('client_id', $client->id)
        //                                               ->where('is_revoked', OAuthUserAuthorization::IS_REVOKED_NO)
        //                                               ->first();

        if (!OAuthCacher::isUserAuthorized($authCode->user_uuid, $client->id)) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_USER_UNAUTHORIZED);
        }

        // 创建或获取用户绑定
        //        $userBinding = OAuthUserBinding::firstOrCreate([
        //            'user_uuid' => $authCode->user_uuid,
        //            'client_id' => $client->id,
        //        ], ['open_id' => $this->openIDGenerater->generateOpenID($client->id, $authCode->user_uuid)]);

        $userBinding = OAuthCacher::getUserBinding($authCode->user_uuid, $client->id);

        if (!$userBinding) {
            $userBinding = OAuthUserBinding::create([
                'user_uuid' => $authCode->user_uuid,
                'client_id' => $client->id,
                'open_id'   => $this->openIDGenerater->generateOpenID($client->id, $authCode->user_uuid),
            ]);

            OAuthCacher::invalidateUserBinding($authCode->user_uuid, $client->id);
        }

        // 创建访问令牌
        $accessToken = $this->createAccessToken($authCode);

        $authCode->update(['is_revoked' => AuthCode::IS_REVOKED_YES]);

        return $this->tokenResponse($accessToken, $userBinding);
    }

    /**
     * @throws OAuthException
     */
    private function handleRefreshToken(string $refreshTokenId, Client $client) {
        $refreshToken = RefreshToken::where('id', $refreshTokenId)
                                    ->where('is_revoked', RefreshToken::IS_REVOKED_NO)
                                    ->where('expires_at', '>', now())
                                    ->first();

        if (!$refreshToken) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_REFRESH_TOKEN_INVALIDATE);
        }

        $oldToken = $refreshToken->accessToken;
        if ($oldToken->client_id !== $client->id) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_REFRESH_TOKEN_INVALIDATE);
        }

        // 撤销旧令牌
        $oldToken->update(['is_revoked' => AccessToken::IS_REVOKED_YES]);
        $refreshToken->update(['is_revoked' => RefreshToken::IS_REVOKED_YES]);

        OAuthCacher::invalidateAccessToken($oldToken->id);

        // 创建新令牌
        $accessToken = $this->createAccessToken($oldToken);

        //        $userBinding = OAuthUserBinding::where('user_uuid', $accessToken->user_uuid)
        //                                       ->where('client_id', $client->id)
        //                                       ->firstOrFail();

        $userBinding = OAuthCacher::getUserBinding($accessToken->user_uuid, $client->id);
        if (!$userBinding) {
            throw new OAuthException(ErrorCodeEnum::OAUTH_OPNEID_NOT_FOUND);
        }

        return $this->tokenResponse($accessToken, $userBinding);
    }

    /**
     * @param $source
     *
     * @return AccessToken
     */
    private function createAccessToken($source) {
        // 过往accessToken 失效
        //        AccessToken::where('user_uuid', $source->user_uuid)
        //                   ->where('client_id', $source->client_id)
        //                   ->where('revoked', false)
        //                   ->where('expires_at', '>', now())
        //                   ->update(['revoked' => true]);

        // 创建新accessToken
        $accessToken = AccessToken::create([
            'id'         => Str::random(64),
            'user_uuid'  => $source->user_uuid,
            'client_id'  => $source->client_id,
            'scopes'     => $source->scopes,
            'expires_at' => now()->addDays(30),
        ]);

        RefreshToken::create([
            'id'              => Str::random(64),
            'access_token_id' => $accessToken->id,
            'expires_at'      => now()->addDays(90),
        ]);

        return $accessToken;
    }

    /**
     * @param AccessToken $token
     * @param OAuthUserBinding $binding
     *
     * @return \Illuminate\Http\JsonResponse
     */
    private function tokenResponse(AccessToken $token, OAuthUserBinding $binding) {
        return Respond::success([
            'access_token'       => $token->id,
            'expires_in'         => (int)Carbon::now()
                                               ->diffInSeconds($token->expires_at),
            'refresh_token'      => $token->refreshToken->id,
            'refresh_expires_in' => (int)Carbon::now()
                                               ->diffInSeconds($token->refreshToken->expires_at),
            'open_id'            => $binding->open_id,
        ]);
    }

    /**
     * 创建或获取用户绑定并生成openid
     */
    private function ensureUserBinding(string $userUuid, Client $client): OAuthUserBinding {
        $userBinding = OAuthCacher::getUserBinding($userUuid, $client->id);
        if ($userBinding) {
            return $userBinding;
        }

        $userBinding = OAuthUserBinding::create([
            'user_uuid' => $userUuid,
            'client_id' => $client->id,
            'open_id'   => $this->openIDGenerater->generateOpenID($client->id, $userUuid),
        ]);

        // 更新缓存
        OAuthCacher::invalidateUserBinding($userUuid, $client->id);

        return $userBinding;
    }
}
