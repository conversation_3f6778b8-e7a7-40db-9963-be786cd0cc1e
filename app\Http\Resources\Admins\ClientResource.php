<?php

namespace App\Http\Resources\Admins;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Client Resource
 * @property-read \App\Models\Client $resource
 * @mixin \App\Models\Client
 */
class ClientResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array {
        return [
            'id'                              => $this->id,
            'name'                            => $this->name,
            'icon'                            => $this->icon,
            'display_icon'                    => $this->display_icon,
            'provider'                        => $this->provider,
            'description'                     => $this->description,
            'client_key'                      => $this->client_key,
            'client_type'                     => $this->client_type,
            'is_revoked'                      => $this->is_revoked,
            'revoked_text'                    => $this->revoked_text,
            'allowed_scopes'                  => $this->allowed_scopes,
            'allowed_scopes_with_label'       => $this->getAllowedScopesWithLabels(),
            'allowed_admin_scopes'            => $this->allowed_admin_scopes,
            'allowed_admin_scopes_with_label' => $this->getAllowedAdminScopesWithLabels(),
            'allowed_jsapis'                  => $this->allowed_jsapis,
            'disabled_jsapis'                 => $this->disabled_jsapis,
            'auth_safe_domains'               => $this->auth_safe_domains,
            'callback_url'                    => $this->callback_url,
            'is_system'                       => $this->is_system,
            // 'bindings_count'                  => $this->whenLoaded('userBindings', function () {
            //     return $this->userBindings->first()?->bindings_count ?? 0;
            // }),
            'created_at'                      => $this->created_at,
            'show_in_workspace'               => $this->show_in_workspace,
            'show_in_matrix'                  => $this->show_in_matrix,
            'default_redirect_url'            => $this->default_redirect_url,
            'workspace_redirect_url'          => $this->workspace_redirect_url,
            'is_workspace_client'             => $this->is_workspace_client,
            'enterprise'                      => EnterpriseResource::make($this->whenLoaded('enterprise')),
        ];
    }
}
