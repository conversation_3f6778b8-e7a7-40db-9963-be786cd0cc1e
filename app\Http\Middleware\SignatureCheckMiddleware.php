<?php

namespace App\Http\Middleware;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\SignatureException;
use App\Models\Client;
use App\Utils\OAuthCacher;
use App\Utils\SignatureValidator;
use App\Utils\Tools;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class SignatureCheckMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     *
     * @throws SignatureException
     */
    public function handle(Request $request, Closure $next, string $checkSystem = 'app'): Response
    {
        if (config('uc.enable_signature')) {
            $this->checkSignature($request, $checkSystem);
        }

        return $next($request);
    }

    /**
     * @throws SignatureException
     */
    protected function checkSignature(Request $request, string $checkSystem)
    {
        $clientId = Tools::getClientId();
        $nonce = Tools::getNonce();
        $signature = Tools::getSignature();
        $timestamp = Tools::getTimestamp();

        // 检查参数不为空
        if (!$clientId || !$nonce || !$signature || !$timestamp) {
            throw new SignatureException(ErrorCodeEnum::PARAMS_MISSING);
        }

        if (Cache::has($nonce)) {
            throw new SignatureException(ErrorCodeEnum::REPLAY_ATTACK);
        }

        Cache::put($nonce, true, 700);

        // 检查时间戳
        if (abs(intval($timestamp) - time()) > 600) {
            throw new SignatureException(ErrorCodeEnum::SIGNATURE_EXPIRED);
        }

        if ($checkSystem == 'app') {
            if ($clientId == config('uc.app_client_id')) {
                $clientSecret = config('uc.app_client_access_secret');
            } else {
                throw new SignatureException(ErrorCodeEnum::ILLEGAL_CLIENT);
            }
        } else {
            $clientSecret = OAuthCacher::getClientSecretByKey($clientId);
        }

        if (!$clientSecret) {
            throw new SignatureException(ErrorCodeEnum::ILLEGAL_CLIENT);
        }

        $signatureData = [
            'client_id' => $clientId,
            'nonce'     => $nonce,
            'timestamp' => $timestamp,
            'signature' => $signature,
        ] + $request->input();

        $signatureValidator = new SignatureValidator($clientId, $clientSecret);

        if (!$signatureValidator->verifySignature($signatureData)) {
            throw new SignatureException(ErrorCodeEnum::SIGNATURE_ERROR, config('app.env') != 'production'?[
                'signatureData' => $signatureData,
                'signautre'     => $signatureValidator->generateSignature($signatureData),
                'signature_str' => $signatureValidator->getSignatureStr(),
            ]:[]);
        }
    }
}
