<?php

namespace App\Models\Traits;

use Spa<PERSON>\Activitylog\ActivitylogServiceProvider;
use Spatie\Activitylog\Facades\CauserResolver;
use Spatie\Activitylog\Models\Activity;

/**
 * 批量活动日志记录通用 Trait
 * 用于手动记录批量操作的日志
 */
trait BatchLogsActivityTrait
{
    /**
     * 记录批量操作日志
     *
     * @param string $description 操作描述
     * @param array $models 操作的模型数组
     * @param array $properties 额外属性
     * @return Activity
     */
    public static function logBatchActivity(string $description, array $models, array $properties = []): Activity
    {
        $activity = app(ActivitylogServiceProvider::determineActivityModel());
        $activity->log_name = self::getBatchLogName();
        $activity->description = $description;
        
        // 设置操作者
        if ($causer = CauserResolver::resolve()) {
            $activity->causer()->associate($causer);
        }
        
        // 设置主题 (如果所有模型都是同一类型)
        if (!empty($models) && is_object($models[0])) {
            $firstModel = $models[0];
            $activity->subject_type = get_class($firstModel);
            
            // 添加所有主题ID到属性中
            $subjectIds = array_map(function ($model) {
                return $model->getKey();
            }, $models);
            
            $properties['subject_ids'] = $subjectIds;
            
            // 记录第一个模型作为代表
            $activity->subject()->associate($firstModel);
        }
        
        // 合并默认属性
        $defaultProperties = self::getBatchActivitylogProperties();
        $activity->properties = collect($defaultProperties)->merge($properties);
        
        $activity->save();
        
        return $activity;
    }
    
    /**
     * 获取批量操作的日志名称
     *
     * @return string
     */
    protected static function getBatchLogName(): string
    {
        return 'batch_operations';
    }
    
    /**
     * 获取批量操作的活动日志属性
     *
     * @return array
     */
    protected static function getBatchActivitylogProperties(): array
    {
        return [
            'operation_type' => 'batch_' . strtolower(class_basename(static::class)) . '_management',
            'log_name' => self::getBatchLogName(),
            'batch' => true,
        ];
    }
} 