<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_routes', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('路由名称');
            $table->string('path')->comment('路由路径');
            $table->string('icon')->nullable()->comment('路由图标');
            $table->integer('parent_id')->nullable()->default(0)->comment('父级路由ID, 0表示顶级路由');
            $table->integer('sort')->nullable()->default(0)->comment('排序');
            $table->boolean('hidden')->nullable()->default(false)->comment('是否隐藏');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_routes');
    }
};
