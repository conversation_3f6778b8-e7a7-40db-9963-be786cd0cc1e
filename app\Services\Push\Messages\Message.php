<?php

namespace App\Services\Push\Messages;

class Message extends AbstractMessage
{
    /**
     * 厂商通道相关配置
     */
    protected ?array $channelProperties = null;

    /**
     * 本地通知相关配置
     */
    protected ?array $localProperties = null;

    /**
     * @param string $title 消息标题
     * @param string $content 消息内容
     * @param string|array $target 用户ID或用户ID数组
     * @param array $extras 额外参数
     * @param array $options 附加选项
     */
    public function __construct(string $title, string $content, string|array $target, array $extras = [], array $options = []) {
        $this->title = $title;
        $this->content = $content;
        $this->target = $target;
        $this->extras = $extras;

        // 设置附加选项
        foreach ($options as $key => $value) {
            $method = 'set' . ucfirst($key);
            if (method_exists($this, $method)) {
                $this->$method($value);
            }
        }
    }

    public function getChannelProperties(): ?array {
        return $this->channelProperties;
    }

    public function setChannelProperties(array $properties): self {
        $this->channelProperties = $properties;

        return $this;
    }

    public function getLocalProperties(): ?array {
        return $this->localProperties;
    }

    public function setLocalProperties(array $properties): self {
        $this->localProperties = $properties;

        return $this;
    }

    /**
     * 添加厂商通道配置
     */
    public function addChannelProperties(array $properties): self {
        if (!$this->channelProperties) {
            $this->channelProperties = [];
        }
        $this->channelProperties = array_merge($this->channelProperties, $properties);

        return $this;
    }

    /**
     * 创建透传消息
     */
    public static function message(string $title, string $content, string|array $target, array $extras = [], array $options = []): self {
        $instance = new self($title, $content, $target, $extras, $options);
        $instance->setType('message');

        return $instance;
    }

    /**
     * 创建通知栏消息
     */
    public static function notification(string $title, string $content, string|array $target, array $extras = [], array $options = []): self {
        $instance = new self($title, $content, $target, $extras, $options);
        $instance->setType('notification');

        return $instance;
    }

    /**
     * 创建广播消息
     */
    public static function broadcast(string $title, string $content, array $extras = [], array $options = []): self {
        return new self($title, $content, [], $extras, $options);
    }

    /**
     * 创建URL跳转消息
     */
    public static function url(string $title, string $content, string $url, string|array $target, array $extras = [], array $options = []): self {
        $options['afterOpen'] = 'go_url';
        $options['url'] = $url;

        return new self($title, $content, $target, $extras, $options);
    }

    /**
     * 创建Activity跳转消息
     */
    public static function activity(string $title, string $content, string $activity, string|array $target, array $extras = [], array $options = []): self {
        $options['afterOpen'] = 'go_activity';
        $options['activity'] = $activity;

        return new self($title, $content, $target, $extras, $options);
    }

    /**
     * 创建自定义消息
     */
    public static function custom(string $title, string $content, mixed $customContent, string|array $target, array $extras = [], array $options = []): self {
        $options['afterOpen'] = 'go_custom';
        $options['custom'] = $customContent;

        return new self($title, $content, $target, $extras, $options);
    }

    /**
     * 创建带大图的消息
     */
    public static function withImage(string $title, string $content, string $imageUrl, string|array $target, array $extras = [], array $options = []): self {
        $options['img'] = $imageUrl;

        return new self($title, $content, $target, $extras, $options);
    }

    /**
     * 创建带展开大图的消息(仅支持小米通道)
     */
    public static function withExpandImage(string $title, string $content, string $expandImageUrl, string|array $target, array $extras = [], array $options = []): self {
        $options['expandImage'] = $expandImageUrl;

        return new self($title, $content, $target, $extras, $options);
    }

    /**
     * 创建静默消息(仅iOS)
     */
    public static function silent(string $title, string $content, string|array $target, array $extras = [], array $options = []): self {
        $instance = new self($title, $content, $target, $extras, $options);
        $instance->setType('message');

        return $instance;
    }

    /**
     * 创建重要消息
     */
    public static function important(string $title, string $content, string|array $target, array $extras = [], array $options = []): self {
        $options['category'] = 1; // 系统消息

        return new self($title, $content, $target, $extras, $options);
    }

    /**
     * 创建普通消息
     */
    public static function normal(string $title, string $content, string|array $target, array $extras = [], array $options = []): self {
        $options['category'] = 0; // 运营消息

        return new self($title, $content, $target, $extras, $options);
    }

    /**
     * 创建带角标的消息(iOS/鸿蒙)
     */
    public static function withBadge(string $title, string $content, string|int $badge, string|array $target, array $extras = [], array $options = []): self {
        $options['badge'] = (string)$badge;

        return new self($title, $content, $target, $extras, $options);
    }

    /**
     * 创建带渠道配置的消息
     */
    public static function withChannelProperties(string $title, string $content, string|array $target, array $channelProperties, array $extras = [], array $options = []): self {
        $instance = new self($title, $content, $target, $extras, $options);
        $instance->setChannelProperties($channelProperties);

        return $instance;
    }

    /**
     * 创建带本地通知配置的消息
     */
    public static function withLocalProperties(string $title, string $content, string|array $target, array $localProperties, array $extras = [], array $options = []): self {
        $instance = new self($title, $content, $target, $extras, $options);
        $instance->setLocalProperties($localProperties);

        return $instance;
    }
}
