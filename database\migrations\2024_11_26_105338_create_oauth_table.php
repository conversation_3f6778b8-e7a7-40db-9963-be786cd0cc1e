<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('oauth_clients', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');  // 客户端名称
            $table->string('provider'); // 运营商
            $table->string('description')
                  ->nullable(); // 服务描述
            $table->string('client_key')
                  ->unique();
            $table->string('client_secret', 255);  // 客户端密钥
            $table->text('redirect')->nullable();  // 回调地址
            $table->string('client_type');  // 客户端类型: miniapp/mp
            $table->json('allowed_scopes')
                  ->nullable();  // 允许的权限范围
            $table->json('allowed_jsapis')
                  ->nullable();
            $table->boolean('revoked')
                  ->default(false);  // 是否已撤销
            $table->timestamps();
        });

        Schema::create('oauth_user_bindings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('open_id', 64);  // 平台用户唯一标识
            $table->uuid('user_uuid')
                  ->index();  // 关联的用户ID
            $table->unsignedBigInteger('client_id')
                  ->index();  // 关联的客户端ID
            $table->timestamps();

            $table->unique([
                'open_id',
                'client_id',
            ]);
//            $table->foreign('user_id')
//                  ->references('id')
//                  ->on('users')
//                  ->onDelete('cascade');
//            $table->foreign('client_id')
//                  ->references('id')
//                  ->on('oauth_clients')
//                  ->onDelete('cascade');
        });

        Schema::create('oauth_auth_codes', function (Blueprint $table) {
            $table->string('id', 100)
                  ->primary();  // 授权码
            $table->uuid('user_uuid')
                  ->index();  // 关联的用户ID
            $table->unsignedBigInteger('client_id');  // 关联的客户端ID
            $table->text('scopes')
                  ->nullable();  // 授权范围
            $table->boolean('revoked')
                  ->default(false);  // 是否已撤销
            $table->dateTime('expires_at');  // 过期时间
            $table->timestamps();

//            $table->foreign('user_id')
//                  ->references('id')
//                  ->on('users')
//                  ->onDelete('cascade');
//            $table->foreign('client_id')
//                  ->references('id')
//                  ->on('oauth_clients')
//                  ->onDelete('cascade');
        });

        Schema::create('oauth_access_tokens', function (Blueprint $table) {
            $table->string('id', 100)
                  ->primary();  // 访问令牌
            $table->uuid('user_uuid')
                  ->index();  // 关联的用户ID
            $table->unsignedBigInteger('client_id');  // 关联的客户端ID
            $table->text('scopes')
                  ->nullable();  // 授权范围
            $table->boolean('revoked')
                  ->default(false);  // 是否已撤销
            $table->dateTime('expires_at');  // 过期时间
            $table->timestamps();

//            $table->foreign('user_id')
//                  ->references('id')
//                  ->on('users')
//                  ->onDelete('cascade');
//            $table->foreign('client_id')
//                  ->references('id')
//                  ->on('oauth_clients')
//                  ->onDelete('cascade');
        });

        Schema::create('oauth_refresh_tokens', function (Blueprint $table) {
            $table->string('id', 100)
                  ->primary();  // 刷新令牌
            $table->string('access_token_id', 100)
                  ->index();  // 关联的访问令牌ID
            $table->boolean('revoked')
                  ->default(false);  // 是否已撤销
            $table->dateTime('expires_at');  // 过期时间
            $table->timestamps();

//            $table->foreign('access_token_id')
//                  ->references('id')
//                  ->on('oauth_access_tokens')
//                  ->onDelete('cascade');
        });

        Schema::create('oauth_user_authorizations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->uuid('user_uuid')
                  ->index();
            $table->unsignedBigInteger('client_id')
                  ->index();
            $table->json('granted_scopes')
                  ->nullable(); // 已授权的权限范围
            $table->timestamp('authorized_at'); // 首次授权时间
            $table->timestamp('last_used_at')
                  ->nullable(); // 最后使用时间
            $table->boolean('is_revoked')
                  ->default(false); // 是否已取消授权
            $table->timestamp('revoked_at')
                  ->nullable(); // 取消授权时间
            $table->timestamps();

            $table->unique([
                'user_uuid',
                'client_id',
            ]);
//            $table->foreign('user_id')
//                  ->references('id')
//                  ->on('users')
//                  ->onDelete('cascade');
//            $table->foreign('client_id')
//                  ->references('id')
//                  ->on('oauth_clients')
//                  ->onDelete('cascade');
        });
    }

    public function down(): void {
        Schema::dropIfExists('oauth_user_authorizations');
        Schema::dropIfExists('oauth_refresh_tokens');
        Schema::dropIfExists('oauth_access_tokens');
        Schema::dropIfExists('oauth_auth_codes');
        Schema::dropIfExists('oauth_user_bindings');
        Schema::dropIfExists('oauth_clients');
    }
};
