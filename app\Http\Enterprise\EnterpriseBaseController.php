<?php

namespace App\Http\Enterprise;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Facades\AdminPermission;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;

class EnterpriseBaseController extends Controller implements HasMiddleware
{
    protected const PERMISSION_MAP = [];

    public static function middleware() {
        return [
            function (Request $request, \Closure $next) {
                $route = $request->route();
                $actionName = $route->getActionMethod();

                if (isset(static::PERMISSION_MAP[$actionName])) {
                    if (!AdminPermission::can(static::PERMISSION_MAP[$actionName], 'enterprise')) {
                        throw new AdminException(ErrorCodeEnum::ADMIN_NO_PERMISSION, ['action' => static::PERMISSION_MAP[$actionName]], 403);
                    }
                }

                return $next($request);
            },
        ];
    }
}
