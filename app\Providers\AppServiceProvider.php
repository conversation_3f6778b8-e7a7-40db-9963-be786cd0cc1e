<?php

namespace App\Providers;

use App\Models\AdminUser;
use App\Models\User;
use App\Observers\AdminUserObserver;
use App\Observers\UserObserver;
use App\Services\ValidatorExtends;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Passport;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void {
        Passport::ignoreRoutes();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void {
        URL::forceScheme('https');

        $this->bootValidatorExtends();

        User::observe(UserObserver::class);

        AdminUser::observe(AdminUserObserver::class);
    }

    protected function bootValidatorExtends() {
        app('validator')->extend('isMobile', function ($attribute, $value, $paramers, $validator) {
            return ValidatorExtends::isMobile($attribute, $value, $paramers, $validator);
        });

        app('validator')->extend('usernameOrMobileUnique', function ($attribute, $value, $paramers, $validator) {
            return ValidatorExtends::usernameOrMobileUnique($attribute, $value, $paramers, $validator);
        });

        app('validator')->extend('checkCode', function ($attribute, $value, $paramers, $validator) {
            return ValidatorExtends::checkCode($attribute, $value, $paramers, $validator);
        });

        app('validator')->extend('checkRobotAnalyze', function ($attribute, $value, $paramers, $validator) {
            return ValidatorExtends::checkRobotAnalyze($attribute, $value, $paramers, $validator);
        });
    }
}
