<?php

namespace App\Services\Push;

use Closure;
use App\Services\Push\Contracts\PushManagerInterface;
use App\Services\Push\Contracts\PushServiceInterface;
use App\Services\Push\Exceptions\InvalidConfigurationException;

class PushManager implements PushManagerInterface
{
    /**
     * 应用实例
     */
    protected $app;

    /**
     * 推送服务实例数组
     *
     * @var array
     */
    protected array $services = [];

    /**
     * 自定义创建器
     *
     * @var array
     */
    protected array $customCreators = [];

    /**
     * 默认驱动名称
     */
    protected string $defaultDriver;

    public function __construct($app)
    {
        $this->app = $app;
        $this->defaultDriver = $this->app['config']['push.default'] ?? 'umeng';
    }

    /**
     * 获取指定驱动的推送服务
     * @throws InvalidConfigurationException
     */
    public function driver(?string $driver = null): PushServiceInterface
    {
        $driver = $driver ?: $this->getDefaultDriver();

        if (!isset($this->services[$driver])) {
            $this->services[$driver] = $this->createDriver($driver);
        }

        return $this->services[$driver];
    }

    /**
     * 创建驱动实例
     *
     * @throws InvalidConfigurationException
     */
    protected function createDriver(string $driver): PushServiceInterface
    {
        if (isset($this->customCreators[$driver])) {
            return $this->callCustomCreator($driver);
        }

        $method = 'create'.ucfirst($driver).'Driver';

        if (method_exists($this, $method)) {
            return $this->$method();
        }

        throw new InvalidConfigurationException("Driver [$driver] not supported.");
    }

    /**
     * 调用自定义创建器
     */
    protected function callCustomCreator(string $driver)
    {
        return $this->customCreators[$driver]($this->app);
    }

    /**
     * 创建友盟驱动
     */
    protected function createUmengDriver(): PushServiceInterface
    {
        $config = $this->getDriverConfig('umeng');
        return new Services\UMengService($config);
    }

    /**
     * 创建极光驱动
     */
    protected function createJpushDriver(): PushServiceInterface
    {
        $config = $this->getDriverConfig('jpush');
        return new Services\JPushService($config);
    }

    /**
     * 创建阿里云驱动
     */
    protected function createMpaasDriver(): PushServiceInterface
    {
        $config = $this->getDriverConfig('mpaas');
        return new Services\MPaaSService($config);
    }

    /**
     * 获取驱动配置
     */
    protected function getDriverConfig(string $driver): array
    {
        // 尝试从 push 配置获取
        $config = $this->app['config']["push.drivers.{$driver}"] ?? [];

        // 合并 services 配置
        $servicesConfig = $this->app['config']["services.{$driver}"] ?? [];

        return array_merge($servicesConfig, $config);
    }

    /**
     * 注册自定义驱动创建器
     */
    public function extend(string $driver, Closure $callback): PushManagerInterface
    {
        $this->customCreators[$driver] = $callback;
        return $this;
    }

    /**
     * 获取默认驱动名称
     */
    public function getDefaultDriver(): string
    {
        return $this->defaultDriver;
    }

    /**
     * 设置默认驱动
     */
    public function setDefaultDriver(string $name): PushManagerInterface
    {
        $this->defaultDriver = $name;
        return $this;
    }
}
