<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\AdminException;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admins\AdminUserResource;
use App\Models\AdminOperationLog;
use App\Models\AdminUser;
use App\Models\User;
use App\Utils\GmSm;
use App\Utils\RedirectToBuilder;
use App\Utils\Respond;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class AdminProfileController extends AdminBaseController
{
    protected $validationMessages = [
        'true_name.string'                   => '真实姓名必须是字符串',
        'true_name.max'                      => '真实姓名不能超过50个字符',
        'avatar.string'                      => '头像必须是字符串',
        'mobile.size'                        => '手机号必须是11位',
        'mobile.unique'                      => '手机号已被使用',
        'old_password.required'              => '原密码不能为空',
        'new_password.required'              => '新密码不能为空',
        'new_password.min'                   => '新密码不能少于8个字符',
        'new_password.regex'                 => '密码必须包含大小写字母、数字和特殊符号(!@#$%^&*?-_)且最少8位',
        'new_password_confirmation.required' => '确认密码不能为空',
        'new_password_confirmation.same'     => '两次输入的密码不一致',
    ];

    protected const PERMISSION_MAP = [
        // 'show'              => '个人资料.查看',
        // 'update'            => '个人资料.更新',
        // 'updatePassword'    => '个人资料.修改密码',
        // 'operationLogs'     => '个人资料.操作日志',
        // 'permissions'       => '个人资料.权限列表',
        // 'getWaitBindingUsers' => '个人资料.获取待绑定用户',
        // 'bindWithUser'      => '个人资料.绑定用户',
        // 'unbindWithUser'    => '个人资料.解绑用户',
    ];

    /**
     * 获取当前登录管理员的个人资料
     */
    public function show() {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        // 加载关联数据
        $admin->load([
            'roles',
            'permissions',
            'departments',
            'bindUser',
        ]);

        return Respond::success(AdminUserResource::make($admin));
    }

    /**
     * 更新个人资料
     *
     * @throws AdminException
     */
    public function update(Request $request) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        $validated = $request->validate([
            'true_name' => 'nullable|string|max:50',
            'avatar'    => 'nullable|string',
            'mobile'    => [
                'nullable',
                'string',
                'size:11',
                Rule::unique('admin_users')
                    ->ignore($admin->uuid, 'uuid'),
            ],
        ], $this->validationMessages);

        try {
            $admin->update($validated);

            return Respond::success();
        } catch (\Exception $e) {
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 修改密码
     *
     * @throws AdminException
     */
    public function updatePassword(Request $request) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        $validated = $request->validate([
            'old_password'              => 'required|string',
            'new_password'              => [
                'required',
                'string',
                'min:8',
                'regex:/^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?\-_])\S*$/',
            ],
            'new_password_confirmation' => 'required|same:new_password',
        ], $this->validationMessages);

        // 验证旧密码
        if (!Hash::check($validated['old_password'], $admin->password)) {
            throw new AdminException(ErrorCodeEnum::ADMIN_PASSWORD_ERROR);
        }

        try {
            $admin->update([
                'password' => Hash::make($validated['new_password']),
            ]);

            return Respond::success();
        } catch (\Exception $e) {
            throw new AdminException(ErrorCodeEnum::SYSTEM_ERROR);
        }
    }

    /**
     * 获取个人操作日志
     */
    public function operationLogs(Request $request) {
        $admin = Auth::guard('admin')
                     ->user();

        $logs = AdminOperationLog::where('admin_user_uuid', $admin->uuid)
                                 ->when($request->input('module'), function ($query, $module) {
                                     $query->where('module', $module);
                                 })
                                 ->when($request->input('start_date'), function ($query, $startDate) {
                                     $query->where('created_at', '>=', $startDate);
                                 })
                                 ->when($request->input('end_date'), function ($query, $endDate) {
                                     $query->where('created_at', '<=', $endDate);
                                 })
                                 ->orderByDesc('id')
                                 ->paginate($request->input('per_page', 15));

        return Respond::success($logs);
    }

    /**
     * 获取个人权限列表
     */
    public function permissions() {
        /** @var AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        // 获取所有权限(包括通过角色和部门继承的权限)
        $permissions = $admin->getAllPermissions();

        // 按模块分组
        $groupedPermissions = $permissions->groupBy(function ($permission) {
            return explode('.', $permission->name)[0];
        })
                                          ->map(function ($permissions) {
                                              return $permissions->map(function ($permission) {
                                                  return [
                                                      'id'          => $permission->id,
                                                      'name'        => $permission->name,
                                                      'description' => $permission->description,
                                                      'action'      => explode('.', $permission->name)[1] ?? null,
                                                  ];
                                              });
                                          });

        return Respond::success($groupedPermissions);
    }

    public function getWaitBindingUsers() {
        $admin = Auth::guard('admin')
                     ->user();

        $user = User::where('mobile', $admin->mobile)
                    ->first();

        GmSm::configure(publicKey: config('sm2.data_pub_key'), privateKey: config('sm2.data_priv_key'));
        $qrCode = GmSm::sm2Encrypt(json_encode([
            'uuid'      => $admin->uuid,
            'timestamp' => time(),
        ], JSON_UNESCAPED_UNICODE));

        return Respond::success([
            'is_binded' => (bool)$admin->bind_user_uuid,
            'user'      => $user ? [
                'uuid'     => $user->uuid,
                'nickname' => $user->nickname,
                'avatar'   => $user->display_avatar,
                'mobile'   => $user->mobile,
            ] : [],
            'qr_code'   => route('bind-admin', ['qr_code' => $qrCode]),
        ]);
    }

    public function bindWithUser(Request $request) {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        $validator = $request->validate([
            'user_uuid' => 'required|exists:' . User::class . ',uuid,mobile,' . $admin->mobile,
        ]);

        $admin->bind_user_uuid = $validator['user_uuid'];
        $admin->save();

        return Respond::success();
    }

    public function unbindWithUser() {
        /** @var \App\Models\AdminUser $admin */
        $admin = Auth::guard('admin')
                     ->user();

        $admin->bind_user_uuid = null;
        $admin->save();

        return Respond::success();
    }
}
