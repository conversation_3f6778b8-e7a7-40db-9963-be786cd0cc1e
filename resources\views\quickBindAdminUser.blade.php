<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>快捷确认企业联系人</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .title {
            font-size: 20px;
            color: #333;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 14px;
            color: #666;
        }
        .info-box {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .info-item:last-child {
            margin-bottom: 0;
        }
        .label {
            color: #666;
        }
        .value {
            color: #333;
        }
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #007bff;
            color: #fff;
        }
        .btn-primary:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            text-align: center;
            margin: 20px 0;
            font-size: 14px;
            padding: 15px;
            border-radius: 6px;
        }
        .message.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .message.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .loading {
            text-align: center;
            color: #666;
            margin: 20px 0;
        }
        .loading-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 123, 255, 0.3);
            border-radius: 50%;
            border-top-color: #007bff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        #contentBox {
            display: none;
        }
        #loadingBox {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 环境检测加载界面 -->
        <div id="loadingBox">
            <div class="loading-icon"></div>
            <span>环境检测中，请稍候...</span>
        </div>
        
        <div id="contentBox">
            <div class="header">
                <div class="title">快捷确认企业联系人</div>
            </div>
            <div id="content"></div>
        </div>
        
        <div id="messageBox" style="display: none;" class="message error"></div>
    </div>

    <script>
        // 环境检查
        function isCGAppWeb() {
            var ua = window.navigator.userAgent.toLowerCase();
            return ua.indexOf('changguan') !== -1;
        }

        // 显示消息
        function showMessage(text, type) {
            var messageBox = document.getElementById('messageBox');
            messageBox.textContent = text;
            messageBox.className = 'message ' + (type || 'error');
            messageBox.style.display = 'block';
        }

        // 显示内容区域
        function showContent() {
            document.getElementById('loadingBox').style.display = 'none';
            document.getElementById('contentBox').style.display = 'block';
        }

        // CGApp就绪检查
        function CGAppWebReady() {
            return new Promise(function(resolve, reject) {
                if (isCGAppWeb()) {
                    if (window.cgapp) {
                        resolve();
                    } else {
                        var timeoutId = setTimeout(function() {
                            reject(new Error('常观App环境加载超时'));
                        }, 5000);
                        
                        document.addEventListener(
                            'cgappjsbridgeready',
                            function() {
                                clearTimeout(timeoutId);
                                resolve();
                            },
                            false
                        );
                    }
                } else {
                    reject(new Error('请在常观App内打开页面'));
                }
            });
        }

        // 获取绑定信息
        function getBindInfo() {
            var content = document.getElementById('content');
            content.innerHTML = '<div class="loading">获取信息中...</div>';

            cgapp.systemRequest({
                client_id: 'default',
                method: 'GET',
                uri: "{{ url('/api/workspace/quick-bind/admin-user-info') }}",
                params: {},
                success: function(res) {
                    if (res.statusCode === 200) {
                        var data = res.data;
                        if (data.errcode === 0) {
                            displayUserInfo(data.data);
                        } else {
                            showMessage(data.message || '获取信息失败');
                        }
                    } else {
                        showMessage('请求失败，状态码: ' + res.statusCode);
                    }
                },
                fail: function(error) {
                    showMessage(error.errmsg || '网络请求失败，请重试');
                }
            });
        }

        // 显示用户信息
        function displayUserInfo(info) {
            var content = document.getElementById('content');
            var html = `
                <div class="info-box">
                    <div class="info-item">
                        <span class="label">部门</span>
                        <span class="value">${info.department}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">姓名</span>
                        <span class="value">${info.true_name}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">手机号</span>
                        <span class="value">${info.mobile}</span>
                    </div>
                </div>`;
                
            if (!info.can_bind) {
                // 不能绑定，显示提示信息
                html += `<div class="message ${info.msg.includes('已绑定') ? 'success' : 'error'}">${info.msg}</div>`;
            } else {
                // 可以绑定，显示确认按钮
                html += `<button id="confirmButton" class="btn btn-primary">确认绑定</button>`;
            }
            
            content.innerHTML = html;
            
            // 如果可以绑定，绑定点击事件
            if (info.can_bind) {
                document.getElementById('confirmButton').addEventListener('click', confirmBind);
            }
        }

        // 确认绑定
        function confirmBind() {
            var btn = document.getElementById('confirmButton');
            btn.disabled = true;
            btn.textContent = '绑定中...';

            cgapp.systemRequest({
                client_id: 'default',
                method: 'POST',
                uri: "{{ url('/api/workspace/quick-bind/confirm-bind') }}",
                params: {},
                success: function(res) {
                    if (res.statusCode === 200) {
                        var data = res.data;
                        if (data.errcode === 0) {
                            showSuccess(data.data.msg);
                            setTimeout(function() {
                                // 关闭窗口
                                if (window.cgapp && window.cgapp.closeWebView) {
                                    window.cgapp.closeWebView();
                                }
                            }, 5000);
                        } else {
                            showMessage(data.message || '绑定失败');
                            btn.disabled = false;
                            btn.textContent = '确认绑定';
                        }
                    } else {
                        showMessage('请求失败，状态码: ' + res.statusCode);
                        btn.disabled = false;
                        btn.textContent = '确认绑定';
                    }
                },
                fail: function(error) {
                    showMessage(error.errmsg || '网络请求失败，请重试');
                    btn.disabled = false;
                    btn.textContent = '确认绑定';
                }
            });
        }

        // 显示成功信息
        function showSuccess(message) {
            var content = document.getElementById('content');
            content.innerHTML = `
                <div class="info-box" style="background: #d4edda; color: #155724;">
                    <div style="text-align: center;">${message}</div>
                </div>
            `;
        }

        // 页面初始化
        function initPage() {
            // 先进行环境检测
            CGAppWebReady()
                .then(function() {
                    // 检测成功，显示操作界面并配置客户端
                    showContent();
                    cgapp.config({client_id:"default"});
                    // 获取绑定信息
                    getBindInfo();
                })
                .catch(function(error) {
                    // 环境检测失败
                    document.getElementById('loadingBox').style.display = 'none';
                    showMessage(error.message || '环境检测失败');
                });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
