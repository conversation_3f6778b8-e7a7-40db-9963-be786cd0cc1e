<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Exceptions\SmsException;
use App\Models\SmsCode;
use App\Utils\Respond;
use App\Utils\Tools;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class SmsController extends Controller
{
    protected $validationMessages = [
        'mobile.required' => '手机号不能为空',
        'mobile.is_mobile' => '请输入正确的手机号格式',
        'mobile.exists'   => '该手机号未注册',
    ];

    /**
     * @throws ValidationException
     * @throws SmsException
     */
    public function sendSms(Request $request)
    {
        //        $this->validate($request, [
        //            'captcha_scene' => 'required',
        //            'captcha_verify_param' => 'required|checkRobotAnalyze',
        //        ]);

        $this->validate($request, [
            'mobile' => 'required|isMobile',
        ], $this->validationMessages);

        $lastLog = SmsCode::where('mobile', $request->input('mobile'))
            ->where('status', SmsCode::VERIFIED_NO)
            ->orderByDesc('id')
            ->first();

        if ($lastLog && $lastLog->created_at->diffInSeconds(Carbon::now()) <= 60) {
            throw new SmsException(ErrorCodeEnum::SEND_FREQUENT);
        }

        $code = rand(100000, 999999);

        $this->_clientSend($request->input('mobile'), $code);

        return Respond::success();
    }

    public function sendSmsAdminLogin(Request $request)
    {
        $this->validate($request, [
            'captcha_scene' => 'required',
            'captcha_verify_param' => 'required|checkRobotAnalyze:true',
        ]);

        $this->validate($request, [
            'mobile' => 'required|isMobile',
        ], $this->validationMessages);

        $lastLog = SmsCode::where('mobile', $request->input('mobile'))
            ->where('status', SmsCode::VERIFIED_NO)
            ->orderByDesc('id')
            ->first();

        if ($lastLog && $lastLog->created_at->diffInSeconds(Carbon::now()) <= 60) {
            throw new SmsException(ErrorCodeEnum::SEND_FREQUENT);
        }

        $code = rand(100000, 999999);

        $this->_clientSend($request->input('mobile'), $code);

        return Respond::success();
    }

    /**
     * @throws SmsException
     * @throws ValidationException
     */
    public function sendToSelf(Request $request)
    {
        $user = Auth::guard('api')
            ->user();

        if (!$user->isMobileConfirmed()) {
            throw new SmsException(ErrorCodeEnum::MOBILE_NOT_BIND);
        }

        //        $this->validate($request, [
        //            'captcha_scene' => 'required',
        //            'captcha_verify_param' => 'required|checkRobotAnalyze',
        //        ], []);

        $lastLog = SmsCode::where('mobile', $user->mobile)
            ->where('status', SmsCode::VERIFIED_NO)
            ->orderByDesc('id')
            ->first();

        if ($lastLog && $lastLog->created_at->diffInSeconds(Carbon::now()) <= 60) {
            throw new SmsException(ErrorCodeEnum::SEND_FREQUENT);
        }

        $code = rand(100000, 999999);

        $this->_clientSend($user->mobile, $code);

        return Respond::success();
    }

    /**
     * @throws SmsException
     */
    protected function _clientSend($mobile, $code)
    {
        try {
            app('easy-sms')->send($mobile, [
                'content'  => '验证码' . $code . '，您正在进行身份验证，打死不要告诉别人哦！',
                'template' => config('uc.sms.aliyun.template.register'),
                'data'     => [
                    'code' => $code,
                ],
            ]);

            SmsCode::create([
                'code'   => $code,
                'mobile' => $mobile,
                'status' => SmsCode::VERIFIED_NO,
                'ip'     => Tools::getClientIp(),
            ]);
        } catch (\Exception $e) {
            throw new SmsException(ErrorCodeEnum::SEND_FAILED);
        }
    }
}
