<?php

namespace App\Services\Push\Contracts;

use App\Services\Push\Exceptions\PushException;

interface PushServiceInterface
{
    /**
     * 推送消息
     *
     * @param MessageInterface $message
     * @return array 响应结果
     * @throws PushException
     */
    public function push(MessageInterface $message): array;

    /**
     * 按用户ID推送
     *
     * @param MessageInterface $message
     * @param string|array $userIds 单个或多个用户ID
     * @return array 响应结果
     * @throws PushException
     */
    public function pushToUsers(MessageInterface $message, string|array $userIds): array;

    /**
     * 按设备推送
     *
     * @param MessageInterface $message
     * @param string|array $deviceTokens 单个或多个设备token
     * @return array 响应结果
     * @throws PushException
     */
    public function pushToDevices(MessageInterface $message, string|array $deviceTokens): array;

    /**
     * 模板消息推送
     *
     * @param MessageInterface $message
     * @param string $templateId 模板ID
     * @param array $templateData 模板变量数据
     * @return array 响应结果
     * @throws PushException
     */
    public function pushTemplate(MessageInterface $message, string $templateId, array $templateData = []): array;

    /**
     * 撤回消息
     *
     * @param string $messageId 消息ID
     * @return array 响应结果
     * @throws PushException
     */
    public function revoke(string $messageId): array;

    /**
     * 获取推送统计数据
     *
     * @param array $params 查询参数
     * @return array 统计数据
     * @throws PushException
     */
    public function statistics(array $params): array;

    /**
     * 设置推送平台
     *
     * @param string $platform 平台标识
     * @return self
     */
    public function platform(string $platform): self;

    /**
     * 获取当前平台
     *
     * @return string 平台标识
     */
    public function getPlatform(): string;
}
